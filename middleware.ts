import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'
import { Permission } from './lib/api/types'
import { jwtDecode } from 'jwt-decode'

// Define role interface
interface Role {
  id: string
  name: string
  permissions: Permission[]
}

// Define user interface from JWT token
interface JwtUser {
  id: string
  email: string
  role_id: string
  role?: {
    id: string
    name: string
    permissions: string[] | Permission[]
  } | string // Role can be a string in some JWT tokens
  exp: number
}

// Routes that require authentication but no specific permissions
const AUTH_REQUIRED_ROUTES = [
  '/dashboard',
  '/dashboard/profile',
  '/dashboard/my-tickets', // Allow all authenticated users to access their tickets
  '/dashboard/tickets', // Main tickets page is accessible to all authenticated users
  '/dashboard/settings', // Base settings route is accessible to authenticated users
]

// Public routes that should always be accessible
const PUBLIC_ROUTES = [
  '/',
  '/auth/login',
  '/auth/signup',
  '/auth/forgot-password',
  '/auth/reset-password',
  '/auth/change-password',
  '/forbidden',
  '/error',
]

// Routes that require specific permissions
const PERMISSION_ROUTES = [
  { path: '/dashboard/users', permission: 'user:view' },
  // Main tickets page is accessible to all authenticated users
  // Sub-routes require specific permissions
  { path: '/dashboard/tickets/agents', permission: 'user:view' },
  { path: '/dashboard/tickets/faqs', permission: 'faq:view' }, // Changed from tickets:read to faq:view
  { path: '/dashboard/tickets/escalations', permission: 'ticket:escalate' },
  { path: '/dashboard/contacts', permission: 'contacts:read' },
  { path: '/dashboard/reports', permission: 'report:view' },
  { path: '/dashboard/settings/roles', permission: 'role:view' },
  { path: '/dashboard/settings/departments', permission: 'department:view' },
  { path: '/dashboard/settings/audit', permission: 'system:settings' },
  { path: '/dashboard/settings/kpis', permission: 'system:kpis' },
]

export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;

  // Skip middleware for static files and API routes
  if (pathname.startsWith('/_next') ||
    pathname.startsWith('/api') ||
    pathname.startsWith('/_vercel')) {
    return NextResponse.next();
  }

  // Allow access to public routes and the forbidden page
  if (PUBLIC_ROUTES.some(route => pathname === route || pathname.startsWith(`${route}/`)) ||
    pathname === '/forbidden') {
    return NextResponse.next();
  }

  // Get the token from cookies
  const token = request.cookies.get('token')?.value;
  console.log('Token:', token);

  // Check if route requires authentication
  const requiresAuth = AUTH_REQUIRED_ROUTES.some(route => pathname.startsWith(route));

  if (requiresAuth || !token) {
    if (!token) {
      // If no token and the user is not trying to login, redirect to login with 'from'

      const url = new URL('/auth/login', request.url);

      // Check if the user is already trying to access /auth/login
      if (pathname !== "/auth/login") {
        url.searchParams.set('from', pathname);
      }
      return NextResponse.redirect(url);
    }

    try {
      // Basic token validation and user extraction
      const userData = jwtDecode<JwtUser>(token);

      console.log('JWT Token User Data:', {
        id: userData.id,
        email: userData.email,
        role_id: userData.role_id,
        hasRole: !!userData.role,
        roleDetails: userData.role ? JSON.stringify(userData.role) : 'none'
      });

      // Check token expiration
      if (userData.exp * 1000 < Date.now()) {
        // Token expired, redirect to login
        return NextResponse.redirect(new URL('/auth/login', request.url));
      }

      // Handle the case where role is a string (e.g., "platform_owner")
      if (typeof userData.role === 'string') {
        const roleName = userData.role.toLowerCase();

        // Check if the role string indicates platform_owner or admin
        if (roleName === 'platform_owner' ||
          roleName === 'admin' ||
          roleName === 'super_admin' ||
          roleName === 'administrator') {
          console.log(`Admin role detected from string: ${roleName} - granting access to all routes`);
          return NextResponse.next();
        }
      }

      // Special case for admin users based on email
      if (userData.email && (
        userData.email.includes('admin') ||
        userData.email.includes('platform_owner')
      )) {
        console.log(`Admin email detected: ${userData.email} - granting access to all routes`);
        return NextResponse.next();
      }

      // Special case for platform_owner - always grant access to all routes
      if (typeof userData.role === 'object' && userData.role?.name &&
        userData.role.name.toLowerCase() === 'platform_owner') {
        console.log(`Platform owner ${userData.email} granted access to all routes`);
        return NextResponse.next();
      }

      // Special case for admin roles - grant access to all routes
      if (typeof userData.role === 'object' && userData.role?.name) {
        const adminRoleNames = ['admin', 'super_admin', 'administrator'];
        if (adminRoleNames.includes(userData.role.name.toLowerCase())) {
          console.log(`Admin user ${userData.email} granted access to all routes`);
          return NextResponse.next();
        }
      }

      // Check if route requires specific permissions
      const permissionRoute = PERMISSION_ROUTES.find(route =>
        pathname.startsWith(route.path)
      );

      if (permissionRoute) {
        console.log(`Route ${pathname} requires permission: ${permissionRoute.permission}`);

        // Check if user has the required permission
        const hasPermission = checkUserPermission(userData, permissionRoute.permission);

        if (!hasPermission) {
          // User doesn't have permission, redirect to forbidden page
          console.log(`Permission denied: ${userData.email} tried to access ${pathname} without ${permissionRoute.permission}`);
          return NextResponse.redirect(new URL('/forbidden', request.url));
        } else {
          console.log(`Permission granted: ${userData.email} has permission ${permissionRoute.permission} for ${pathname}`);
        }
      }

      // Check if route requires authentication (but no specific permission)
      const requiresAuth = AUTH_REQUIRED_ROUTES.some(route => pathname.startsWith(route));

      if (requiresAuth || permissionRoute) {
        // User is authenticated and has required permissions if needed
        return NextResponse.next();
      }

      // For non-auth routes, proceed normally
      return NextResponse.next();
    } catch (error) {
      console.error('Error in middleware:', error);
      // Invalid token, redirect to login
      return NextResponse.redirect(new URL('/auth/login', request.url));
    }
  }

  // Helper function to check if a user has a specific permission
  function checkUserPermission(user: JwtUser, requiredPermission: string): boolean {
    // Debug log to see what's in the user object
    console.log(`Checking permission ${requiredPermission} for user:`, {
      email: user.email,
      role: typeof user.role === 'string' ? user.role : (user.role?.name || 'unknown'),
      roleId: user.role_id
    });

    // Define common permissions that should be available to all authenticated users
    const commonPermissions = [
      'faq:view',
      'tickets:read',
      'contacts:read'
    ];

    // If the permission is in the common permissions list, grant access to all authenticated users
    if (commonPermissions.includes(requiredPermission)) {
      console.log(`Permission ${requiredPermission} is a common permission, granting access to ${user.email}`);
      return true;
    }

    // Handle the case where role is a string (e.g., "platform_owner")
    if (typeof user.role === 'string') {
      const roleName = user.role.toLowerCase();

      // Platform Owner has all permissions
      if (roleName === 'platform_owner') {
        console.log('Platform owner access granted (string role)');
        return true;
      }

      // Admin roles have all permissions
      const adminRoleNames = ['admin', 'super_admin', 'super admin', 'administrator'];
      if (adminRoleNames.includes(roleName)) {
        console.log('Admin access granted (string role)');
        return true;
      }

      // Supervisor roles have most permissions except some admin-only ones
      const supervisorRoleNames = ['supervisor', 'team_lead', 'team lead'];
      if (supervisorRoleNames.includes(roleName)) {
        // Define admin-only permissions that supervisors don't have by default
        const adminOnlyPermissions = [
          'user:create', 'user:update', 'user:delete',
          'role:view', 'role:create', 'role:update', 'role:delete',
          'system:settings', 'system:kpis',
          'department:create', 'department:update', 'department:delete',
          'faq:create', 'faq:update', 'faq:delete'
        ];

        // If the required permission is admin-only, deny access
        if (adminOnlyPermissions.includes(requiredPermission)) {
          console.log(`Supervisor denied access to admin-only permission: ${requiredPermission} (string role)`);
          return false;
        }

        console.log('Supervisor access granted (string role)');
        return true;
      }
    }

    // If the user has a role object with a name, check based on role name
    if (typeof user.role === 'object' && user.role?.name) {
      const roleName = user.role.name.toLowerCase();

      // Platform Owner has all permissions
      if (roleName === 'platform_owner') {
        console.log('Platform owner access granted (object role)');
        return true;
      }

      // Admin roles have all permissions
      const adminRoleNames = ['admin', 'super_admin', 'super admin', 'administrator'];
      if (adminRoleNames.includes(roleName)) {
        console.log('Admin access granted (object role)');
        return true;
      }

      // Supervisor roles have most permissions except some admin-only ones
      const supervisorRoleNames = ['supervisor', 'team_lead', 'team lead'];
      if (supervisorRoleNames.includes(roleName)) {
        // Define admin-only permissions that supervisors don't have by default
        const adminOnlyPermissions = [
          'user:create', 'user:update', 'user:delete',
          'role:view', 'role:create', 'role:update', 'role:delete',
          'system:settings', 'system:kpis',
          'department:create', 'department:update', 'department:delete',
          'faq:create', 'faq:update', 'faq:delete'
        ];

        // If the required permission is admin-only, check if they have it specifically
        if (adminOnlyPermissions.includes(requiredPermission)) {
          // Check if they have the specific permission anyway
          if (user.role.permissions) {
            const hasSpecificPermission = user.role.permissions.some((permission: any) => {
              if (typeof permission === 'string') {
                return permission === requiredPermission;
              } else {
                return permission.code === requiredPermission;
              }
            });

            if (hasSpecificPermission) {
              console.log(`Supervisor has specific admin permission: ${requiredPermission}`);
              return true;
            }
          }

          console.log(`Supervisor denied access to admin-only permission: ${requiredPermission} (object role)`);
          return false;
        }

        console.log('Supervisor access granted (object role)');
        return true;
      }
    }

    // If we have permissions in the role object, check them
    if (typeof user.role === 'object' && user.role?.permissions) {
      // Log available permissions for debugging
      const availablePermissions = user.role.permissions.map((p: any) =>
        typeof p === 'string' ? p : p.code
      );

      // Handle both string[] and Permission[] types
      const hasPermission = user.role.permissions.some((permission: any) => {
        if (typeof permission === 'string') {
          return permission === requiredPermission;
        } else {
          return permission.code === requiredPermission;
        }
      });

      console.log(`Checking permission ${requiredPermission} for role: ` +
        `{hasPermission: ${hasPermission}, availablePermissions: ${JSON.stringify(availablePermissions)}}`);

      if (hasPermission) {
        return true;
      }
    }

    // If we don't have role information but have an email that suggests admin privileges
    if (user.email && (
      user.email.includes('admin') ||
      user.email.includes('platform') ||
      user.email.includes('owner')
    )) {
      console.log(`Admin email detected: ${user.email} - granting access to ${requiredPermission}`);
      return true;
    }

    // Check again if this is a common permission that all users should have
    if (commonPermissions.includes(requiredPermission)) {
      console.log(`Permission ${requiredPermission} is a common permission, granting access to ${user.email}`);
      return true;
    }

    console.log(`No permissions found for user ${user.email}`);
    return false;
  }
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public files (public folder)
     */
    '/((?!_next/static|_next/image|favicon.ico|public/).*)',
  ],
}
