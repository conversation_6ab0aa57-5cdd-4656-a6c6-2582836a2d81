{"name": "callcenter", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack -p 3001", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@bprogress/next": "^3.2.12", "@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-aspect-ratio": "^1.1.7", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-context-menu": "^2.2.15", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-hover-card": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-menubar": "^1.1.15", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.14", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-toggle-group": "^1.1.10", "@radix-ui/react-tooltip": "^1.2.7", "@tailwindcss/postcss": "^4.1.8", "@tanstack/react-query": "^5.77.2", "@tanstack/react-table": "^8.21.3", "@types/jssip": "^3.5.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "embla-carousel-react": "^8.6.0", "exceljs": "^4.4.0", "framer-motion": "^12.17.3", "input-otp": "^1.4.2", "jssip": "^3.10.1", "jwt-decode": "^4.0.0", "lucide-react": "^0.479.0", "next": "^15.2.4", "next-themes": "^0.4.6", "nextjs-toploader": "^3.8.16", "react": "^19.1.0", "react-day-picker": "^9.7.0", "react-dom": "^19.1.0", "react-draggable": "^4.4.6", "react-hook-form": "^7.56.4", "react-resizable-panels": "^3.0.2", "recharts": "^3.0.0-beta.1", "socket.io-client": "^4.8.1", "sonner": "^2.0.3", "tailwind-merge": "^3.2.0", "tailwind-variants": "^1.0.0", "tailwindcss-animate": "^1.0.7", "vaul": "^1.1.2", "xlsx": "^0.18.5", "zod": "^3.24.3", "zod-validation-error": "^3.4.1", "zustand": "^5.0.5"}, "devDependencies": {"@eslint/eslintrc": "^3", "@hookform/resolvers": "^4.1.3", "@tailwindcss/upgrade": "^4.1.5", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.1.0", "eslint-config-prettier": "^10.1.1", "postcss": "^8.5.4", "tailwindcss": "^4.1.5", "tw-animate-css": "^1.2.9", "typescript": "^5"}, "overrides": {"react-is": "^19.0.0-rc-69d4b800-20241021"}}