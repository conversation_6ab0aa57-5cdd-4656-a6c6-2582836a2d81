import { Role } from "@/lib/api/types"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { Check, X } from "lucide-react"

interface RoleComparisonProps {
  roles: Role[]
}

export function RoleComparison({ roles }: RoleComparisonProps) {
  // Define all permission categories and types based on the actual API structure
  const allPermissionCategories = [
    { id: "user", display: "Users" },
    { id: "ticket", display: "Tickets" },
    { id: "report", display: "Reports" },
    { id: "role", display: "Roles" },
    { id: "department", display: "Departments" },
    { id: "product", display: "Products" },
    { id: "channel", display: "Channels" },
    { id: "contact", display: "Contacts" },
    { id: "faq", display: "FAQs" },
    { id: "system", display: "System" },
    { id: "call", display: "Calls" },
    { id: "escalation", display: "Escalations" }
  ];

  const allPermissionTypes = [
    { id: "view", display: "View" },
    { id: "create", display: "Create" },
    { id: "update", display: "Update" },
    { id: "delete", display: "Delete" },
    { id: "export", display: "Export" },
    { id: "assign", display: "Assign" },
    { id: "escalate", display: "Escalate" },
    { id: "comment", display: "Comment" },
    { id: "read", display: "Read" }
  ];

  // Sort roles by permission level (highest first)
  const sortedRoles = [...roles].sort((a, b) => b.permission_level - a.permission_level);

  // Function to check if a role has a specific permission
  const hasPermission = (role: Role, category: string, type: string) => {
    // Special case for "read" which is often used instead of "view" in the API
    const actionType = type === "view" ? "(view|read)" : type;

    // Create regex patterns to match various permission code formats
    const permissionRegex = new RegExp(`^${category}:${actionType}$`, 'i');
    const permissionPluralRegex = new RegExp(`^${category}s:${actionType}$`, 'i');

    // Create regex patterns to match various permission name formats
    const nameRegex1 = new RegExp(`${type}\\s+${category}s?`, 'i');  // "View Ticket" or "View Tickets"
    const nameRegex2 = new RegExp(`${category}s?\\s+${type}`, 'i');  // "Ticket View" or "Tickets View"

    // Special case for "read" in names
    const readNameRegex1 = type === "view" ? new RegExp(`read\\s+${category}s?`, 'i') : null;
    const readNameRegex2 = type === "view" ? new RegExp(`${category}s?\\s+read`, 'i') : null;

    return role.permissions.some(p => {
      // Check code matches
      const matchesCode = p.code && (
        permissionRegex.test(p.code) ||
        permissionPluralRegex.test(p.code) ||
        (type === "view" && (p.code.includes(`:read`) && p.category === category))
      );

      // Check name matches
      const matchesName = p.name && (
        nameRegex1.test(p.name.toLowerCase()) ||
        nameRegex2.test(p.name.toLowerCase()) ||
        (readNameRegex1 && readNameRegex1.test(p.name.toLowerCase())) ||
        (readNameRegex2 && readNameRegex2.test(p.name.toLowerCase()))
      );

      // Check category matches
      const matchesCategory = p.category === category;

      return matchesCode || (matchesName && matchesCategory);
    });
  };

  return (
    <div className="space-y-4 overflow-x-auto">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead className="w-[200px] sticky left-0 bg-background">Permission</TableHead>
            {sortedRoles.map((role) => (
              <TableHead key={role.id} className="text-center">
                <div className="font-medium">{role.name}</div>
                <div className="text-xs text-muted-foreground">Level {role.permission_level}</div>
              </TableHead>
            ))}
          </TableRow>
        </TableHeader>
        <TableBody>
          {allPermissionCategories.map((category) => (
            <TableRow key={category.id}>
              <TableCell className="font-medium sticky left-0 bg-background">{category.display}</TableCell>
              {sortedRoles.map((role) => (
                <TableCell key={role.id} className="text-center">
                  <div className="flex flex-col gap-1">
                    {allPermissionTypes.map((type) => {
                      // Skip certain combinations that don't make sense
                      if ((category.id === 'report' && type.id === 'update') ||
                          (category.id === 'system' && ['create', 'update', 'delete'].includes(type.id)) ||
                          (type.id === 'read' && category.id !== 'ticket' && category.id !== 'contact') ||
                          (type.id === 'comment' && category.id !== 'ticket' && category.id !== 'escalation')) {
                        return null;
                      }

                      // Skip "view" for categories that use "read" instead
                      if (type.id === 'view' && (category.id === 'ticket' || category.id === 'contact')) {
                        return null;
                      }

                      const permissionExists = hasPermission(role, category.id, type.id);
                      return (
                        <div key={type.id} className="flex items-center justify-center gap-1">
                          {permissionExists ? (
                            <Check className="size-4 text-green-500" />
                          ) : (
                            <X className="size-4 text-destructive" />
                          )}
                          <span className="text-xs">{type.display}</span>
                        </div>
                      );
                    })}
                  </div>
                </TableCell>
              ))}
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  )
}
