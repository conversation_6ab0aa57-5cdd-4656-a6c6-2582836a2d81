import { Role } from "@/lib/api/types"
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { cn } from "@/lib/utils"

interface RolePermissionsProps {
  role: Role
}

export function RolePermissions({ role }: RolePermissionsProps) {
  // Group permissions by category
  const permissionsByCategory = role.permissions.reduce((acc, permission) => {
    // Extract category from permission code (e.g., "user:view" -> "user")
    const category = permission.category || permission.code?.split(':')[0] || 'other';

    if (!acc[category]) {
      acc[category] = [];
    }

    acc[category].push(permission);
    return acc;
  }, {} as Record<string, typeof role.permissions>);

  // Get sorted categories
  const categories = Object.keys(permissionsByCategory).sort();

  // Function to get display name for a category
  const getCategoryDisplayName = (category: string) => {
    const displayNames: Record<string, string> = {
      'user': 'Users',
      'ticket': 'Tickets',
      'report': 'Reports',
      'role': 'Roles',
      'department': 'Departments',
      'product': 'Products',
      'channel': 'Channels',
      'contact': 'Contacts',
      'faq': 'FAQs',
      'system': 'System'
    };

    return displayNames[category] || category.charAt(0).toUpperCase() + category.slice(1);
  };

  // Function to get action type from permission code
  const getActionType = (permission: typeof role.permissions[0]) => {
    if (permission.code) {
      // Extract action from permission code (e.g., "user:view" -> "view")
      return permission.code.split(':')[1] || '';
    }

    // Fallback to name if code is not available
    const nameParts = permission.name.toLowerCase().split(' ');
    const commonActions = ['view', 'create', 'update', 'edit', 'delete', 'export', 'assign', 'escalate'];

    for (const action of commonActions) {
      if (nameParts.includes(action)) {
        return action;
      }
    }

    return '';
  };

  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
      {categories.map((category) => {
        const categoryPermissions = permissionsByCategory[category];

        return (
          <Card key={category}>
            <CardHeader>
              <CardTitle className="capitalize">{getCategoryDisplayName(category)}</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex flex-wrap gap-2">
                {categoryPermissions.map((permission) => {
                  const type = getActionType(permission);
                  return (
                    <Badge
                      key={permission.id}
                      variant="outline"
                      className={cn(
                        "capitalize",
                        type === "delete" && "border-destructive text-destructive",
                        (type === "edit" || type === "update") && "border-warning text-warning",
                        type === "create" && "border-success text-success",
                        type === "view" && "border-primary text-primary",
                        type === "export" && "border-info text-info",
                        type === "assign" && "border-secondary text-secondary",
                        type === "escalate" && "border-accent text-accent"
                      )}
                      title={permission.name}
                    >
                      {type || permission.name}
                    </Badge>
                  );
                })}
              </div>
            </CardContent>
          </Card>
        );
      })}
    </div>
  );
}
