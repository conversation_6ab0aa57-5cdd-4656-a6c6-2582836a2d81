import { Role } from "@/lib/api/types"
import { But<PERSON> } from "@/components/ui/button"
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
} from "@/components/ui/command"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"
import { cn } from "@/lib/utils"
import { Check, ChevronsUpDown } from "lucide-react"
import { useState } from "react"
import { Badge } from "@/components/ui/badge"

interface RoleAssignmentProps {
  roles: Role[]
  selectedRoleId?: string
  onRoleSelect: (roleId: string) => void
  disabled?: boolean
}

export function RoleAssignment({
  roles,
  selectedRoleId,
  onRoleSelect,
  disabled = false,
}: RoleAssignmentProps) {
  const [open, setOpen] = useState(false)
  const selectedRole = roles.find((role) => role.id === selectedRoleId)

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          className="w-full justify-between"
          disabled={disabled}
        >
          {selectedRole ? (
            <div className="flex items-center gap-2">
              {selectedRole.name}
              <Badge variant="outline">Level {selectedRole.permission_level}</Badge>
              {selectedRole.name.toLowerCase() === 'platform_owner' && (
                <Badge variant="default" className="bg-primary">Admin</Badge>
              )}
              {selectedRole.permission_level >= 5 && selectedRole.name.toLowerCase() !== 'platform_owner' && (
                <Badge variant="default" className="bg-blue-500">Admin</Badge>
              )}
              {selectedRole.permission_level >= 2 && selectedRole.permission_level < 5 && (
                <Badge variant="default" className="bg-green-500">Supervisor</Badge>
              )}
            </div>
          ) : (
            "Select role..."
          )}
          <ChevronsUpDown className="ml-2 size-4 shrink-0 opacity-50" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-full p-0">
        <Command>
          <CommandInput placeholder="Search roles..." />
          <CommandEmpty>No roles found.</CommandEmpty>
          <CommandGroup>
            {roles
              .sort((a, b) => b.permission_level - a.permission_level)
              .map((role) => (
                <CommandItem
                  key={role.id}
                  value={role.name}
                  onSelect={() => {
                    onRoleSelect(role.id)
                    setOpen(false)
                  }}
                >
                  <Check
                    className={cn(
                      "mr-2 size-4",
                      selectedRoleId === role.id ? "opacity-100" : "opacity-0"
                    )}
                  />
                  <div className="flex items-center gap-2">
                    {role.name}
                    <Badge variant="outline">Level {role.permission_level}</Badge>
                    {role.is_system_role && (
                      <Badge variant="secondary">System</Badge>
                    )}
                    {role.name.toLowerCase() === 'platform_owner' && (
                      <Badge variant="default" className="bg-primary">Admin</Badge>
                    )}
                    {role.permission_level >= 5 && role.name.toLowerCase() !== 'platform_owner' && (
                      <Badge variant="default" className="bg-blue-500">Admin</Badge>
                    )}
                    {role.permission_level >= 2 && role.permission_level < 5 && (
                      <Badge variant="default" className="bg-green-500">Supervisor</Badge>
                    )}
                  </div>
                </CommandItem>
              ))}
          </CommandGroup>
        </Command>
      </PopoverContent>
    </Popover>
  )
}
