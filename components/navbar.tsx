"use client"

import type React from "react"

import { useState, useEffect } from "react"
import Link from "next/link"
import { useRouter } from "next/navigation"
import { useAuth } from "@/lib/hooks/useAuth"
import { useAuditLogger } from "@/lib/hooks/useAuditLogger"
import { Search, User, Bell, Settings, LogOut, ChevronDown, Users, Ticket, Headphones, BarChart, AlertTriangle, CheckCircle2, MessageCircle, Coffee, Clock, Moon } from "lucide-react"
import { hasPermission } from "@/lib/utils/roles"
import { UserRole, Role } from "@/lib/api/types"
import { useNotifications } from "@/lib/hooks/useNotifications"
import { formatDistanceToNow } from "date-fns"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuGroup,
    DropdownMenuItem,
    DropdownMenuLabel,
    DropdownMenuSeparator,
    DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import {
    Command,
    CommandEmpty,
    CommandGroup,
    CommandInput,
    CommandItem,
    CommandList
} from "@/components/ui/command"
import { Badge } from "@/components/ui/badge"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from "@/components/ui/dialog";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { WorkspaceSelector } from "@/components/workspace/workspace-selector"
import { GlobalDialButton } from "@/components/calls/GlobalDialButton"

export function Navbar() {
    const router = useRouter()
    const { user, logout, isLoggingOut } = useAuth()
    const { notifications, unreadCount, markAsRead, markAllAsRead } = useNotifications()
    const { logAgentStatusEvent } = useAuditLogger()
    const [searchType, setSearchType] = useState<"all" | "users" | "tickets" | "contacts">("all")
    const [searchQuery, setSearchQuery] = useState("")
    const [isSearchOpen, setIsSearchOpen] = useState(false)
    const [agentStatus, setAgentStatus] = useState<"available" | "break" | "lunch" | "offline">("available")
    const [showLogoutModal, setShowLogoutModal] = useState(false);
    const [logoutReason, setLogoutReason] = useState("");

    const userRole = user?.role as Role
    const isAgent = user?.role?.name?.toLowerCase().includes('agent')

    // Format notification date
    const formatNotificationDate = (date: string) => {
        return formatDistanceToNow(new Date(date), { addSuffix: true })
    }

    // Handle notification click
    const handleNotificationClick = (notification: any) => {
        markAsRead(notification.id)
        if (notification.link) {
            router.push(notification.link)
        }
    }

    const handleSearch = (e: React.FormEvent) => {
        e.preventDefault()
        // In a real app, you would search based on the query and type
        console.log(`Searching for ${searchQuery} in ${searchType}`)
        setIsSearchOpen(false)
    }

    const handleLogout = () => {
        setShowLogoutModal(true);
    };

    const confirmLogout = () => {
        // Optionally: log the reason somewhere
        setShowLogoutModal(false);
        logout();
    };

    // Handle agent status change
    const handleStatusChange = (status: "available" | "break" | "lunch" | "offline") => {
        setAgentStatus(status)

        // Log the status change to audit logs
        if (user?.id) {
            const statusActionMap = {
                available: "AGENT_STATUS_AVAILABLE",
                break: "AGENT_STATUS_BREAK",
                lunch: "AGENT_STATUS_LUNCH",
                offline: "AGENT_STATUS_OFFLINE"
            } as const;

            logAgentStatusEvent(
                statusActionMap[status],
                user.id,
                {
                    previous_status: agentStatus,
                    new_status: status,
                    timestamp: new Date().toISOString()
                }
            );
        }

        // In a real app, you would update the agent status in the backend
        console.log(`Agent status changed to: ${status}`)

        // Show a toast notification or some feedback to the user
        if (status === "offline") {
            // If the agent is going offline, log them out
            setTimeout(() => {
                logout()
            }, 500)
        }
    }

    return (
        <div className="border-b bg-background">
            <div className="flex h-16 items-center px-4">
                <div className="flex items-center gap-2 font-semibold text-xl md:hidden">
                    <Headphones className="h-6 w-6 text-primary" />
                    <span className="font-bold">CallCenter</span>
                </div>

                {/* Workspace Selector - Desktop */}
                {!isAgent && (
                    <div className="hidden md:flex flex-1 justify-center">
                        <WorkspaceSelector />
                    </div>
                )}

                <div className="ml-auto flex items-center space-x-4">
                    {/* Workspace Selector - Mobile */}
                    {!isAgent && (
                        <div className="md:hidden">
                            <WorkspaceSelector showLabel={false} />
                        </div>
                    )}

                    {/* Global Dial Button - Available for agents and supervisors */}
                    {(isAgent || hasPermission(userRole, 'canCreateCalls')) && (
                        <GlobalDialButton />
                    )}

                    {/* Search */}
                    <Popover open={isSearchOpen} onOpenChange={setIsSearchOpen}>
                        <PopoverTrigger asChild>
                            <Button variant="outline" size="icon" className="md:hidden">
                                <Search className="size-4" />
                            </Button>
                        </PopoverTrigger>
                        <div className="hidden md:flex md:w-full md:max-w-sm items-center space-x-2">
                            <form onSubmit={handleSearch} className="flex-1 relative">
                                <Search className="absolute left-2.5 top-2.5 size-4 text-muted-foreground" />
                                <Input
                                    type="search"
                                    placeholder={`Search ${searchType !== "all" ? searchType : ""}...`}
                                    className="pl-8 w-full"
                                    value={searchQuery}
                                    onChange={(e) => setSearchQuery(e.target.value)}
                                />
                            </form>
                            <DropdownMenu>
                                <DropdownMenuTrigger asChild>
                                    <Button variant="outline" className="flex items-center gap-1">
                                        {searchType === "all" && "All"}
                                        {searchType === "users" && "Users"}
                                        {searchType === "tickets" && "Tickets"}
                                        {searchType === "contacts" && "Contacts"}
                                        <ChevronDown className="size-4" />
                                    </Button>
                                </DropdownMenuTrigger>
                                <DropdownMenuContent align="end">
                                    <DropdownMenuItem onClick={() => setSearchType("all")}>All</DropdownMenuItem>
                                    {hasPermission(userRole, 'canViewUsers') && (
                                        <DropdownMenuItem onClick={() => setSearchType("users")}>
                                            <Users className="mr-2 size-4" />
                                            Users
                                        </DropdownMenuItem>
                                    )}
                                    <DropdownMenuItem onClick={() => setSearchType("tickets")}>
                                        <Ticket className="mr-2 size-4" />
                                        Tickets
                                    </DropdownMenuItem>
                                    <DropdownMenuItem onClick={() => setSearchType("contacts")}>
                                        <Headphones className="mr-2 size-4" />
                                        Contacts
                                    </DropdownMenuItem>
                                </DropdownMenuContent>
                            </DropdownMenu>
                        </div>
                        <PopoverContent className="w-80 p-0" align="end">
                            <div className="flex h-full w-full flex-col overflow-hidden rounded-md bg-popover text-popover-foreground">
                                <Command shouldFilter={false}>
                                    <CommandInput placeholder="Search..." />
                                    <CommandList>
                                        <CommandEmpty>No results found.</CommandEmpty>
                                        <CommandGroup heading="Search in">
                                            <CommandItem onSelect={() => setSearchType("all")}>
                                                All
                                            </CommandItem>
                                            {hasPermission(userRole, 'canViewUsers') && (
                                                <CommandItem onSelect={() => setSearchType("users")}>
                                                    <Users className="mr-2 size-4" />
                                                    Users
                                                </CommandItem>
                                            )}
                                            <CommandItem onSelect={() => setSearchType("tickets")}>
                                                <Ticket className="mr-2 size-4" />
                                                Tickets
                                            </CommandItem>
                                            <CommandItem onSelect={() => setSearchType("contacts")}>
                                                <Headphones className="mr-2 size-4" />
                                                Contacts
                                            </CommandItem>
                                        </CommandGroup>
                                    </CommandList>
                                </Command>
                            </div>
                        </PopoverContent>
                    </Popover>

                    {/* Notifications */}
                    <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="icon" className="relative">
                                <Bell className="h-5 w-5" />
                                {unreadCount > 0 && (
                                    <Badge className="absolute -top-1 -right-1 h-5 w-5 rounded-full p-0 flex items-center justify-center">
                                        {unreadCount > 9 ? '9+' : unreadCount}
                                    </Badge>
                                )}
                            </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end" className="w-80">
                            <div className="flex items-center justify-between px-4 py-2">
                                <DropdownMenuLabel className="px-0 py-0">Notifications</DropdownMenuLabel>
                                {notifications.length > 0 && (
                                    <Button
                                        variant="ghost"
                                        size="sm"
                                        onClick={() => markAllAsRead()}
                                        className="h-auto text-xs px-2"
                                    >
                                        Mark all as read
                                    </Button>
                                )}
                            </div>
                            <DropdownMenuSeparator />
                            <div className="max-h-80 overflow-auto">
                                {notifications.length === 0 ? (
                                    <div className="flex flex-col items-center justify-center py-6 px-4">
                                        <Bell className="h-8 w-8 text-muted-foreground mb-2 opacity-50" />
                                        <p className="text-sm text-muted-foreground">No notifications yet</p>
                                    </div>
                                ) : (
                                    notifications.map((notification) => (
                                        <DropdownMenuItem
                                            key={notification.id}
                                            className={`cursor-pointer p-4 ${!notification.isRead ? 'bg-muted/50' : ''}`}
                                            onClick={() => handleNotificationClick(notification)}
                                        >
                                            <div className="flex gap-3 w-full">
                                                <div className="shrink-0 mt-1">
                                                    {notification.type === 'escalation' && (
                                                        <AlertTriangle className="h-5 w-5 text-amber-500" />
                                                    )}
                                                    {notification.type === 'ticket' && (
                                                        <Ticket className="h-5 w-5 text-blue-500" />
                                                    )}
                                                    {notification.type === 'comment' && (
                                                        <MessageCircle className="h-5 w-5 text-green-500" />
                                                    )}
                                                    {notification.type === 'system' && (
                                                        <Bell className="h-5 w-5 text-gray-500" />
                                                    )}
                                                </div>
                                                <div className="flex flex-col gap-1 flex-1">
                                                    <div className="flex items-center justify-between">
                                                        <p className="text-sm font-medium">{notification.title}</p>
                                                        <span className="text-xs text-muted-foreground whitespace-nowrap ml-2">
                                                            {formatNotificationDate(notification.createdAt)}
                                                        </span>
                                                    </div>
                                                    <p className="text-xs text-muted-foreground">{notification.message}</p>
                                                </div>
                                            </div>
                                        </DropdownMenuItem>
                                    ))
                                )}
                            </div>
                            {notifications.length > 0 && (
                                <>
                                    <DropdownMenuSeparator />
                                    <DropdownMenuItem
                                        className="cursor-pointer justify-center text-primary"
                                        onClick={() => router.push('/dashboard/notifications')}
                                    >
                                        View all notifications
                                    </DropdownMenuItem>
                                </>
                            )}
                        </DropdownMenuContent>
                    </DropdownMenu>

                    {/* User Menu */}
                    <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                            <Button variant="ghost" className="relative h-8 w-8 rounded-full">
                                <Avatar className="h-8 w-8">
                                    <AvatarImage src="/placeholder.svg?height=32&width=32" alt="John Doe" />
                                    <AvatarFallback>{(user?.first_name?.charAt(0).toUpperCase() ?? '') + (user?.last_name?.charAt(0).toUpperCase() ?? '')}</AvatarFallback>
                                </Avatar>
                            </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent className="w-56" align="end" forceMount>
                            <DropdownMenuLabel className="font-normal">
                                <div className="flex flex-col space-y-1">
                                    <p className="text-sm font-medium leading-none">{user?.first_name} {user?.last_name}</p>
                                    {isAgent && (
                                        <div className="flex items-center gap-1.5">
                                            <Badge variant={agentStatus === "available" ? "default" : agentStatus === "break" ? "secondary" : agentStatus === "lunch" ? "outline" : "destructive"}>
                                                {agentStatus === "available" ? "Available" :
                                                 agentStatus === "break" ? "On Break" :
                                                 agentStatus === "lunch" ? "Lunch Break" : "Offline"}
                                            </Badge>
                                        </div>
                                    )}
                                </div>
                            </DropdownMenuLabel>
                            <DropdownMenuSeparator />
                            <DropdownMenuGroup>
                                <DropdownMenuItem asChild>
                                    <Link href="/dashboard/profile">
                                        <User className="mr-2 size-4" />
                                        <span>Profile</span>
                                    </Link>
                                </DropdownMenuItem>
                                <DropdownMenuItem asChild>
                                    <Link href="/dashboard/settings">
                                        <Settings className="mr-2 size-4" />
                                        <span>Settings</span>
                                    </Link>
                                </DropdownMenuItem>
                            </DropdownMenuGroup>

                            {/* Agent Status Options */}
                            {isAgent && (
                                <>
                                    <DropdownMenuSeparator />
                                    <DropdownMenuLabel>Agent Status</DropdownMenuLabel>
                                    <DropdownMenuGroup>
                                        <DropdownMenuItem onClick={() => handleStatusChange("available")}>
                                            <CheckCircle2 className="mr-2 size-4 text-green-500" />
                                            <span>Available</span>
                                        </DropdownMenuItem>
                                        <DropdownMenuItem onClick={() => handleStatusChange("break")}>
                                            <Coffee className="mr-2 size-4 text-amber-500" />
                                            <span>Take a Break</span>
                                        </DropdownMenuItem>
                                        <DropdownMenuItem onClick={() => handleStatusChange("lunch")}>
                                            <Clock className="mr-2 size-4 text-blue-500" />
                                            <span>Lunch Break</span>
                                        </DropdownMenuItem>
                                        <DropdownMenuItem onClick={() => handleStatusChange("offline")}>
                                            <Moon className="mr-2 size-4 text-purple-500" />
                                            <span>End Shift</span>
                                        </DropdownMenuItem>
                                    </DropdownMenuGroup>
                                </>
                            )}

                            <DropdownMenuSeparator />
                            <DropdownMenuItem onClick={handleLogout} disabled={isLoggingOut}>
                                {isLoggingOut ? (
                                    <>
                                        <span className="mr-2 size-4 animate-spin">⏳</span>
                                        <span>Logging out...</span>
                                    </>
                                ) : (
                                    <>
                                        <LogOut className="mr-2 size-4" />
                                        <span>Log out</span>
                                    </>
                                )}
                            </DropdownMenuItem>
                        </DropdownMenuContent>
                    </DropdownMenu>
                </div>
            </div>
            {/* Logout Reason Modal */}
            <Dialog open={showLogoutModal} onOpenChange={setShowLogoutModal}>
                <DialogContent>
                    <DialogHeader>
                        <DialogTitle>Select Logout Reason</DialogTitle>
                    </DialogHeader>
                    <RadioGroup value={logoutReason} onValueChange={setLogoutReason}>
                        <RadioGroupItem value="tea">
                            <div className="text-gray-500">

                                Tea Breaks
                            </div>
                        </RadioGroupItem>
                        <RadioGroupItem value="lunch">Lunch Break</RadioGroupItem>
                        <RadioGroupItem value="other">Other Break</RadioGroupItem>
                    </RadioGroup>
                    <DialogFooter>
                        <Button onClick={confirmLogout} disabled={!logoutReason}>
                            Confirm & Log out
                        </Button>
                        <Button variant="ghost" onClick={() => setShowLogoutModal(false)}>
                            Cancel
                        </Button>
                    </DialogFooter>
                </DialogContent>
            </Dialog>
        </div>
    )
}
