"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { ShieldAlert } from "lucide-react"

interface PermissionErrorProps {
  message?: string
  onBack?: () => void
}

export function PermissionError({
  message = "You do not have the required permissions to access this resource.",
  onBack = () => window.history.back()
}: PermissionErrorProps) {
  return (
    <div className="flex flex-col items-center justify-center gap-2 py-8">
      <div className="w-12 h-12 rounded-full bg-destructive/10 flex items-center justify-center mb-2">
        <ShieldAlert className="w-6 h-6 text-destructive" />
      </div>
      <div className="text-destructive font-medium">Access Denied</div>
      <div className="text-muted-foreground text-center max-w-md">
        {message}
      </div>
      <Button
        variant="outline"
        size="sm"
        className="mt-2"
        onClick={onBack}
      >
        Go Back
      </Button>
    </div>
  )
}
