'use client';

import { useEffect, useRef, useState } from 'react';

interface AudioPlayerProps {
  src: string;
  autoPlay?: boolean;
  loop?: boolean;
  volume?: number;
  onPlay?: () => void;
  onError?: (error: Error) => void;
  hidden?: boolean;
}

export function AudioPlayer({
  src,
  autoPlay = false,
  loop = false,
  volume = 0.5,
  onPlay,
  onError,
  hidden = false,
}: AudioPlayerProps) {
  const audioRef = useRef<HTMLAudioElement>(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Initialize audio when component mounts
  useEffect(() => {
    const audio = audioRef.current;
    if (!audio) return;

    // Set up event listeners
    const handlePlay = () => {
      console.log('AudioPlayer: play event fired');
      setIsPlaying(true);
      onPlay?.();
    };

    const handlePause = () => {
      console.log('AudioPlayer: pause event fired');
      setIsPlaying(false);
    };

    const handleError = (e: Event) => {
      console.error('AudioPlayer: error event fired', e);
      const errorMessage = audio.error 
        ? `Error code: ${audio.error.code}, message: ${audio.error.message}` 
        : 'Unknown error';
      setError(errorMessage);
      onError?.(new Error(errorMessage));
    };

    const handleCanPlay = () => {
      console.log('AudioPlayer: canplay event fired');
      if (autoPlay) {
        audio.play().catch(err => {
          console.error('AudioPlayer: autoplay failed', err);
          setError(`Autoplay failed: ${err.message}`);
        });
      }
    };

    // Add event listeners
    audio.addEventListener('play', handlePlay);
    audio.addEventListener('pause', handlePause);
    audio.addEventListener('error', handleError);
    audio.addEventListener('canplay', handleCanPlay);

    // Set initial properties
    audio.volume = volume;
    audio.loop = loop;

    // Clean up event listeners
    return () => {
      audio.removeEventListener('play', handlePlay);
      audio.removeEventListener('pause', handlePause);
      audio.removeEventListener('error', handleError);
      audio.removeEventListener('canplay', handleCanPlay);
    };
  }, [autoPlay, loop, onError, onPlay, volume]);

  // Update audio properties when they change
  useEffect(() => {
    const audio = audioRef.current;
    if (!audio) return;

    audio.volume = volume;
    audio.loop = loop;
  }, [volume, loop]);

  // Play/pause methods
  const play = () => {
    const audio = audioRef.current;
    if (!audio) return;

    audio.play().catch(err => {
      console.error('AudioPlayer: play method failed', err);
      setError(`Play failed: ${err.message}`);
    });
  };

  const pause = () => {
    const audio = audioRef.current;
    if (!audio) return;

    audio.pause();
  };

  return (
    <>
      {/* Actual audio element */}
      <audio 
        ref={audioRef} 
        src={src} 
        style={{ display: hidden ? 'none' : 'block' }}
      />
      
      {/* Error message if needed */}
      {error && !hidden && (
        <div className="text-red-500 text-sm mt-1">
          Error: {error}
        </div>
      )}
    </>
  );
}

// Export a hook to control the audio player from parent components
export function useAudioPlayer() {
  const audioRef = useRef<HTMLAudioElement | null>(null);
  
  const play = () => {
    if (!audioRef.current) return;
    
    audioRef.current.play().catch(err => {
      console.error('useAudioPlayer: play failed', err);
    });
  };
  
  const pause = () => {
    if (!audioRef.current) return;
    audioRef.current.pause();
  };
  
  const stop = () => {
    if (!audioRef.current) return;
    audioRef.current.pause();
    audioRef.current.currentTime = 0;
  };
  
  return {
    audioRef,
    play,
    pause,
    stop
  };
}
