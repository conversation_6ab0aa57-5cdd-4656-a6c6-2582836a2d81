"use client"
import * as React from "react"
import { format } from "date-fns"
import { Calendar } from "@/components/ui/calendar"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Button } from "@/components/ui/button"
import { CalendarIcon } from "lucide-react"

interface DateRange {
  from: Date | null
  to: Date | null
}

interface DatePickerWithRangeProps {
  date: DateRange
  setDate: (date: DateRange) => void
}

export function DatePickerWithRange({ date, setDate }: DatePickerWithRangeProps) {
  const [open, setOpen] = React.useState(false)
  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          className={"w-[260px] justify-start text-left font-normal"}
        >
          <CalendarIcon className="mr-2 h-4 w-4" />
          {date.from ? (
            date.to ? (
              <span>
                {format(date.from, "LLL dd, yyyy")} - {format(date.to, "LLL dd, yyyy")}
              </span>
            ) : (
              <span>{format(date.from, "LLL dd, yyyy")} - ...</span>
            )
          ) : (
            <span>Pick a date range</span>
          )}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-auto p-0" align="start">
        <Calendar
          mode="range"
          selected={{ from: date.from || undefined, to: date.to || undefined }}
          onSelect={(range) => {
            if (range) {
              setDate({
                from: range?.from || null,
                to: range?.to || null,
              })
            }
          }}
          numberOfMonths={2}
        />
      </PopoverContent>
    </Popover>
  )
}
