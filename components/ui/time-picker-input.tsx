"use client"

import * as React from "react"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"

interface TimePickerInputProps {
  value: { hour: number; minute: number }
  onChange: (value: { hour: number; minute: number }) => void
  label?: string
  className?: string
}

export function TimePickerInput({
  value,
  onChange,
  label,
  className,
}: TimePickerInputProps) {
  const handleHourChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newHour = parseInt(e.target.value)
    if (isNaN(newHour) || newHour < 0 || newHour > 23) return
    onChange({ ...value, hour: newHour })
  }

  const handleMinuteChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newMinute = parseInt(e.target.value)
    if (isNaN(newMinute) || newMinute < 0 || newMinute > 59) return
    onChange({ ...value, minute: newMinute })
  }

  // Format hour for display (e.g., 9 -> "09")
  const formatNumber = (num: number) => {
    return num.toString().padStart(2, "0")
  }

  return (
    <div className={className}>
      {label && <Label className="mb-2">{label}</Label>}
      <div className="flex items-center gap-2">
        <Input
          type="text"
          inputMode="numeric"
          pattern="[0-9]*"
          value={formatNumber(value.hour)}
          onChange={handleHourChange}
          className="w-16 text-center"
          maxLength={2}
        />
        <span className="text-lg">:</span>
        <Input
          type="text"
          inputMode="numeric"
          pattern="[0-9]*"
          value={formatNumber(value.minute)}
          onChange={handleMinuteChange}
          className="w-16 text-center"
          maxLength={2}
        />
        <span className="ml-2 text-sm text-muted-foreground">
          {value.hour < 12 ? "AM" : "PM"}
        </span>
      </div>
    </div>
  )
}
