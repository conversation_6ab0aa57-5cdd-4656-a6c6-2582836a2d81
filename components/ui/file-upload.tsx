"use client"

import * as React from "react"
import { cn } from "@/lib/utils"
import { UploadCloud } from "lucide-react"

interface FileUploadProps extends React.InputHTMLAttributes<HTMLInputElement> {
  onFileSelect: (file: File) => void
  acceptedFileTypes?: string
  maxSize?: number // in bytes
  className?: string
  buttonText?: string
  icon?: React.ReactNode
}

const FileUpload = (
  {
    ref,
    onFileSelect,
    acceptedFileTypes = ".xlsx,.xls,.csv",

    // 5MB default
    maxSize = 5 * 1024 * 1024,

    className,
    buttonText = "Select File",
    icon = <UploadCloud className="h-5 w-5 mr-2" />,
    ...props
  }: FileUploadProps & {
    ref: React.RefObject<HTMLInputElement>;
  }
) => {
  const [dragActive, setDragActive] = React.useState(false)
  const [error, setError] = React.useState<string | null>(null)
  const [selectedFile, setSelectedFile] = React.useState<File | null>(null)
  const inputRef = React.useRef<HTMLInputElement>(null)
  
  const handleFile = (file: File) => {
    setError(null)
    
    // Check file type
    const fileType = file.name.split('.').pop()?.toLowerCase()
    const acceptedTypes = acceptedFileTypes.split(',').map(type => 
      type.trim().replace('.', '').toLowerCase()
    )
    
    if (!acceptedTypes.includes(fileType || '')) {
      setError(`File type not accepted. Please upload ${acceptedFileTypes}`)
      return
    }
    
    // Check file size
    if (file.size > maxSize) {
      setError(`File is too large. Maximum size is ${maxSize / (1024 * 1024)}MB`)
      return
    }
    
    setSelectedFile(file)
    onFileSelect(file)
  }
  
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    e.preventDefault()
    if (e.target.files && e.target.files[0]) {
      handleFile(e.target.files[0])
    }
  }
  
  const handleDrag = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault()
    e.stopPropagation()
    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true)
    } else if (e.type === 'dragleave') {
      setDragActive(false)
    }
  }
  
  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault()
    e.stopPropagation()
    setDragActive(false)
    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      handleFile(e.dataTransfer.files[0])
    }
  }
  
  const handleClick = () => {
    inputRef.current?.click()
  }
  
  return (
    <div className="w-full">
      <div
        className={cn(
          "flex flex-col items-center justify-center w-full p-6 border-2 border-dashed rounded-lg cursor-pointer",
          dragActive ? "border-primary bg-primary/5" : "border-input",
          className
        )}
        onDragEnter={handleDrag}
        onDragLeave={handleDrag}
        onDragOver={handleDrag}
        onDrop={handleDrop}
        onClick={handleClick}
      >
        <input
          ref={inputRef}
          type="file"
          className="hidden"
          accept={acceptedFileTypes}
          onChange={handleChange}
          {...props}
        />
        
        <div className="flex flex-col items-center justify-center text-center">
          {icon}
          <p className="mb-2 text-sm text-muted-foreground">
            <span className="font-semibold">Click to upload</span> or drag and drop
          </p>
          <p className="text-xs text-muted-foreground">
            {acceptedFileTypes.replace(/\./g, '').toUpperCase()} (Max {maxSize / (1024 * 1024)}MB)
          </p>
          
          {selectedFile && (
            <div className="mt-4 text-sm font-medium text-primary">
              Selected: {selectedFile.name}
            </div>
          )}
        </div>
      </div>
      
      {error && (
        <p className="mt-2 text-sm text-destructive">{error}</p>
      )}
    </div>
  )
}

FileUpload.displayName = "FileUpload"

export { FileUpload }
