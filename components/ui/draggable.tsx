"use client";

import React, { useState, useRef, useEffect } from "react";
import { cn } from "@/lib/utils";

interface DraggableProps {
  children: React.ReactNode;
  initialPosition?: { x: number; y: number };
  className?: string;
  handle?: string; // CSS selector for the drag handle
}

export function Draggable({
  children,
  initialPosition = { x: 20, y: 20 },
  className,
  handle = ".drag-handle"
}: DraggableProps) {
  const [position, setPosition] = useState(initialPosition);
  const [isDragging, setIsDragging] = useState(false);
  const dragRef = useRef<HTMLDivElement>(null);
  const startPosRef = useRef({ x: 0, y: 0 });
  const dragPosRef = useRef({ x: 0, y: 0 });

  // Handle mouse down on the drag handle
  const handleMouseDown = (e: MouseEvent) => {
    // Only allow dragging from the handle
    if (handle && !(e.target as HTMLElement).closest(handle)) {
      return;
    }

    e.preventDefault();
    setIsDragging(true);

    // Store the initial mouse position and element position
    startPosRef.current = { x: e.clientX, y: e.clientY };
    dragPosRef.current = { ...position };
  };

  // Handle mouse move while dragging
  const handleMouseMove = (e: MouseEvent) => {
    if (!isDragging) return;

    // Calculate the new position based on the mouse movement
    const dx = e.clientX - startPosRef.current.x;
    const dy = e.clientY - startPosRef.current.y;

    setPosition({
      x: dragPosRef.current.x + dx,
      y: dragPosRef.current.y + dy
    });
  };

  // Handle mouse up to stop dragging
  const handleMouseUp = () => {
    setIsDragging(false);
  };

  // Add and remove event listeners
  useEffect(() => {
    const element = dragRef.current;
    if (!element) return;

    element.addEventListener("mousedown", handleMouseDown);
    document.addEventListener("mousemove", handleMouseMove);
    document.addEventListener("mouseup", handleMouseUp);

    return () => {
      element.removeEventListener("mousedown", handleMouseDown);
      document.removeEventListener("mousemove", handleMouseMove);
      document.removeEventListener("mouseup", handleMouseUp);
    };
  }, [isDragging]);

  // Keep the element within the viewport
  useEffect(() => {
    if (!dragRef.current) return;

    const updatePosition = () => {
      const element = dragRef.current;
      if (!element) return;

      const rect = element.getBoundingClientRect();
      const viewportWidth = window.innerWidth;
      const viewportHeight = window.innerHeight;

      // Adjust position if the element is outside the viewport
      let newX = position.x;
      let newY = position.y;

      if (rect.right > viewportWidth) {
        newX = viewportWidth - rect.width;
      }
      if (rect.bottom > viewportHeight) {
        newY = viewportHeight - rect.height;
      }
      if (rect.left < 0) {
        newX = 0;
      }
      if (rect.top < 0) {
        newY = 0;
      }

      if (newX !== position.x || newY !== position.y) {
        setPosition({ x: newX, y: newY });
      }
    };

    updatePosition();
    window.addEventListener("resize", updatePosition);

    return () => {
      window.removeEventListener("resize", updatePosition);
    };
  }, [position]);

  return (
    <div
      ref={dragRef}
      className={cn(
        "fixed shadow-lg rounded-lg",
        isDragging && "cursor-grabbing opacity-90",
        className
      )}
      style={{
        left: `${position.x}px`,
        top: `${position.y}px`,
        zIndex: 100, // Higher z-index to ensure it's above other elements
        cursor: isDragging ? "grabbing" : "default"
      }}
    >
      {children}
    </div>
  );
}
