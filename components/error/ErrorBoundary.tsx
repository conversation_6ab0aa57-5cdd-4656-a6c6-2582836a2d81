"use client";

import React, { Component, ErrorInfo, ReactNode } from "react";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, RefreshCw, Home } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import Link from "next/link";

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
}

interface State {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
}

export class ErrorBoundary extends Component<Props, State> {
  public state: State = {
    hasError: false,
    error: null,
    errorInfo: null,
  };

  public static getDerivedStateFromError(error: Error): State {
    return { hasError: true, error, errorInfo: null };
  }

  public componentDidCatch(error: Error, errorInfo: ErrorInfo): void {
    console.error("Uncaught error:", error, errorInfo);
    
    this.setState({
      error,
      errorInfo,
    });

    if (this.props.onError) {
      this.props.onError(error, errorInfo);
    }
    
    // You could add error logging to a service here
    // Example: logErrorToService(error, errorInfo);
  }

  private resetError = () => {
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
    });
  };

  public render(): ReactNode {
    if (this.state.hasError) {
      if (this.props.fallback) {
        return this.props.fallback;
      }

      return (
        <div className="rounded-lg border bg-card p-4 shadow-2xs">
          <div className="flex flex-col space-y-3">
            <div className="flex items-center gap-2">
              <AlertCircle className="h-5 w-5 text-destructive" />
              <h3 className="font-medium">Something went wrong</h3>
            </div>
            <p className="text-sm text-muted-foreground">
              {this.state.error?.message || "An unexpected error occurred"}
            </p>
            <div className="flex flex-wrap gap-2">
              <Button size="sm" onClick={this.resetError}>
                <RefreshCw className="mr-2 size-4" />
                Try Again
              </Button>
              <Button size="sm" variant="outline" asChild>
                <Link href="/dashboard">
                  <Home className="mr-2 size-4" />
                  Dashboard
                </Link>
              </Button>
            </div>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}
