import { <PERSON><PERSON><PERSON>ir<PERSON>, RefreshCw } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";

interface LoadingErrorStateProps {
  title?: string;
  description?: string;
  onRetry?: () => void;
  error?: Error;
  className?: string;
}

export function LoadingErrorState({
  title = "Failed to load data",
  description = "There was a problem loading the requested data.",
  onRetry,
  error,
  className,
}: LoadingErrorStateProps) {
  return (
    <Card className={className}>
      <CardHeader className="pb-2">
        <div className="flex items-center gap-2">
          <AlertCircle className="h-5 w-5 text-destructive" />
          <CardTitle>{title}</CardTitle>
        </div>
        <CardDescription>
          {description}
        </CardDescription>
      </CardHeader>
      {error && (
        <CardContent className="pb-2">
          <div className="rounded-md bg-muted p-3">
            <p className="text-sm font-mono break-words">{error.message}</p>
          </div>
        </CardContent>
      )}
      {onRetry && (
        <CardFooter className="pt-2">
          <Button 
            variant="outline" 
            onClick={onRetry}
            className="w-full"
          >
            <RefreshCw className="mr-2 size-4" />
            Try Again
          </Button>
        </CardFooter>
      )}
    </Card>
  );
}
