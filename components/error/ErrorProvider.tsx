"use client";

import React, { createContext, useContext, useState, useCallback, ReactNode } from "react";
import { ErrorModal } from "./ErrorModal";
import { logError } from "@/lib/utils/error-utils";

interface ErrorContextType {
  setError: (error: Error) => void;
  clearError: () => void;
}

const ErrorContext = createContext<ErrorContextType | undefined>(undefined);

export function useErrorContext() {
  const context = useContext(ErrorContext);
  if (context === undefined) {
    throw new Error("useErrorContext must be used within an ErrorProvider");
  }
  return context;
}

interface ErrorProviderProps {
  children: ReactNode;
}

export function ErrorProvider({ children }: ErrorProviderProps) {
  const [error, setErrorState] = useState<Error | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);

  const setError = useCallback((error: Error) => {
    logError(error);
    setErrorState(error);
    setIsModalOpen(true);
  }, []);

  const clearError = useCallback(() => {
    setErrorState(null);
    setIsModalOpen(false);
  }, []);

  const handleRetry = useCallback(() => {
    clearError();
    // You could add additional retry logic here
  }, [clearError]);

  return (
    <ErrorContext.Provider value={{ setError, clearError }}>
      {children}
      {error && (
        <ErrorModal
          open={isModalOpen}
          onOpenChange={setIsModalOpen}
          error={error}
          onRetry={handleRetry}
        />
      )}
    </ErrorContext.Provider>
  );
}
