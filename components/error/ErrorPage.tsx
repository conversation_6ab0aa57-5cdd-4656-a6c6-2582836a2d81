"use client";

import { useEffect, useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON>er, CardHeader, CardTitle } from "@/components/ui/card";
import { AlertCircle, ArrowLeft, RefreshCw, Home, Terminal } from "lucide-react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";

interface ErrorPageProps {
  error: Error & { digest?: string };
  reset: () => void;
}

export function ErrorPage({ error, reset }: ErrorPageProps) {
  const router = useRouter();
  const [errorDetails, setErrorDetails] = useState<{
    message: string;
    stack?: string;
    componentStack?: string;
    digest?: string;
  }>({
    message: error.message || "An unexpected error occurred",
    stack: error.stack,
    digest: error.digest,
  });

  // Format the error stack for better readability
  const formatStack = (stack?: string) => {
    if (!stack) return "No stack trace available";
    return stack
      .split("\n")
      .map((line) => line.trim())
      .join("\n");
  };

  // Determine if this is a known error type and provide helpful messages
  const getErrorHelp = (error: Error) => {
    const message = error.message.toLowerCase();
    
    if (message.includes("network") || message.includes("fetch")) {
      return "This appears to be a network error. Please check your internet connection and try again.";
    }
    
    if (message.includes("permission") || message.includes("unauthorized") || message.includes("forbidden")) {
      return "You don't have permission to access this resource. Please log in or contact your administrator.";
    }
    
    if (message.includes("not found") || message.includes("404")) {
      return "The resource you're looking for could not be found. It may have been moved or deleted.";
    }
    
    return "We've encountered an unexpected error. Our team has been notified and is working to fix the issue.";
  };

  // Log the error to the console and potentially to a monitoring service
  useEffect(() => {
    console.error("Application error:", error);
    
    // Here you could add code to send the error to a monitoring service
    // Example: sendToErrorMonitoring(error);
  }, [error]);

  return (
    <div className="flex min-h-screen items-center justify-center bg-background p-4">
      <Card className="mx-auto max-w-2xl w-full shadow-lg">
        <CardHeader className="space-y-1 pb-2">
          <div className="flex items-center gap-2">
            <AlertCircle className="h-6 w-6 text-destructive" />
            <CardTitle className="text-2xl">Something went wrong</CardTitle>
          </div>
          <CardDescription className="text-base">
            {getErrorHelp(error)}
          </CardDescription>
        </CardHeader>
        <CardContent className="pb-2">
          <div className="space-y-4">
            <div className="rounded-md bg-muted p-4">
              <p className="font-mono text-sm break-words">{errorDetails.message}</p>
            </div>
            
            <Accordion type="single" collapsible className="w-full">
              <AccordionItem value="technical-details">
                <AccordionTrigger className="text-sm">
                  <div className="flex items-center gap-2">
                    <Terminal className="size-4" />
                    <span>Technical Details</span>
                  </div>
                </AccordionTrigger>
                <AccordionContent>
                  <div className="space-y-2">
                    {errorDetails.digest && (
                      <div className="space-y-1">
                        <p className="text-xs font-medium">Error ID:</p>
                        <p className="font-mono text-xs bg-muted p-2 rounded-md">{errorDetails.digest}</p>
                      </div>
                    )}
                    {errorDetails.stack && (
                      <div className="space-y-1">
                        <p className="text-xs font-medium">Stack Trace:</p>
                        <pre className="font-mono text-xs bg-muted p-2 rounded-md overflow-auto max-h-40">
                          {formatStack(errorDetails.stack)}
                        </pre>
                      </div>
                    )}
                  </div>
                </AccordionContent>
              </AccordionItem>
            </Accordion>
          </div>
        </CardContent>
        <CardFooter className="flex flex-col sm:flex-row gap-2 pt-2">
          <Button 
            variant="outline" 
            className="w-full sm:w-auto" 
            onClick={() => router.back()}
          >
            <ArrowLeft className="mr-2 size-4" />
            Go Back
          </Button>
          <Button 
            variant="outline" 
            className="w-full sm:w-auto"
            asChild
          >
            <Link href="/dashboard">
              <Home className="mr-2 size-4" />
              Dashboard
            </Link>
          </Button>
          <Button 
            className="w-full sm:w-auto" 
            onClick={() => reset()}
          >
            <RefreshCw className="mr-2 size-4" />
            Try Again
          </Button>
        </CardFooter>
      </Card>
    </div>
  );
}
