"use client";

import { <PERSON>ert<PERSON><PERSON>cle, RefreshCw, ShieldAlert, Wifi, FileX } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";

interface ApiErrorDisplayProps {
  error: Error & { 
    status?: number;
    statusCode?: number;
    data?: any;
  };
  onRetry?: () => void;
  className?: string;
}

export function ApiErrorDisplay({ error, onRetry, className }: ApiErrorDisplayProps) {
  // Get the status code from the error
  const statusCode = error.status || error.statusCode || 500;
  
  // Determine the error type based on status code
  const getErrorIcon = () => {
    if (statusCode === 403) return <ShieldAlert className="h-5 w-5" />;
    if (statusCode === 404) return <FileX className="h-5 w-5" />;
    if (statusCode >= 500) return <AlertCircle className="h-5 w-5" />;
    if (statusCode === 0 || error.message.includes('network')) return <Wifi className="h-5 w-5" />;
    return <AlertCircle className="h-5 w-5" />;
  };
  
  // Get a user-friendly title based on status code
  const getErrorTitle = () => {
    if (statusCode === 401) return "Authentication Required";
    if (statusCode === 403) return "Access Denied";
    if (statusCode === 404) return "Resource Not Found";
    if (statusCode >= 500) return "Server Error";
    if (statusCode === 0 || error.message.includes('network')) return "Network Error";
    return "Request Error";
  };
  
  // Get a user-friendly message based on status code
  const getErrorMessage = () => {
    if (statusCode === 401) return "You need to log in to access this resource.";
    if (statusCode === 403) return "You don't have permission to access this resource.";
    if (statusCode === 404) return "The requested resource could not be found.";
    if (statusCode >= 500) return "The server encountered an error while processing your request.";
    if (statusCode === 0 || error.message.includes('network')) return "Could not connect to the server. Please check your internet connection.";
    
    // Use the error message if available, otherwise provide a generic message
    return error.message || "An error occurred while processing your request.";
  };
  
  // Get the appropriate variant based on status code
  const getVariant = () => {
    if (statusCode === 401 || statusCode === 403) return "destructive";
    if (statusCode === 404) return "default";
    if (statusCode >= 500) return "destructive";
    return "destructive";
  };
  
  return (
    <Alert variant={getVariant() as any} className={className}>
      <div className="flex flex-col space-y-2">
        <div className="flex items-center gap-2">
          {getErrorIcon()}
          <AlertTitle>{getErrorTitle()}</AlertTitle>
        </div>
        <AlertDescription>
          {getErrorMessage()}
        </AlertDescription>
        {onRetry && (
          <div className="pt-2">
            <Button 
              variant="outline" 
              size="sm" 
              onClick={onRetry}
              className="h-8"
            >
              <RefreshCw className="mr-2 h-3 w-3" />
              Try Again
            </Button>
          </div>
        )}
      </div>
    </Alert>
  );
}
