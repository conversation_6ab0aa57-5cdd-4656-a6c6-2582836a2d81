"use client";

import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON> } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { <PERSON><PERSON>rdion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";

interface ErrorModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  error: Error;
  onRetry?: () => void;
  title?: string;
  description?: string;
}

export function ErrorModal({
  open,
  onOpenChange,
  error,
  onRetry,
  title = "An error occurred",
  description,
}: ErrorModalProps) {
  // Format the error stack for better readability
  const formatStack = (stack?: string) => {
    if (!stack) return "No stack trace available";
    return stack
      .split("\n")
      .map((line) => line.trim())
      .join("\n");
  };

  // Determine if this is a known error type and provide helpful messages
  const getErrorHelp = (error: Error) => {
    const message = error.message.toLowerCase();
    
    if (message.includes("network") || message.includes("fetch")) {
      return "This appears to be a network error. Please check your internet connection and try again.";
    }
    
    if (message.includes("permission") || message.includes("unauthorized") || message.includes("forbidden")) {
      return "You don't have permission to access this resource. Please log in or contact your administrator.";
    }
    
    if (message.includes("not found") || message.includes("404")) {
      return "The resource you're looking for could not be found. It may have been moved or deleted.";
    }
    
    return "We've encountered an unexpected error. Our team has been notified and is working to fix the issue.";
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <AlertCircle className="h-5 w-5 text-destructive" />
            {title}
          </DialogTitle>
          <DialogDescription>
            {description || getErrorHelp(error)}
          </DialogDescription>
        </DialogHeader>
        <div className="space-y-4 py-4">
          <div className="rounded-md bg-muted p-3">
            <p className="font-mono text-sm break-words">{error.message}</p>
          </div>
          
          <Accordion type="single" collapsible className="w-full">
            <AccordionItem value="technical-details">
              <AccordionTrigger className="text-sm">Technical Details</AccordionTrigger>
              <AccordionContent>
                <pre className="font-mono text-xs bg-muted p-2 rounded-md overflow-auto max-h-40">
                  {formatStack(error.stack)}
                </pre>
              </AccordionContent>
            </AccordionItem>
          </Accordion>
        </div>
        <DialogFooter className="flex flex-row gap-2">
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            <X className="mr-2 size-4" />
            Close
          </Button>
          {onRetry && (
            <Button onClick={onRetry}>
              <RefreshCw className="mr-2 size-4" />
              Try Again
            </Button>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
