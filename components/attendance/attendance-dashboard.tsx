"use client";

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { 
  Clock, 
  Coffee, 
  LogIn, 
  LogOut, 
  Users, 
  TrendingUp, 
  Calendar,
  Timer,
  Target,
  AlertCircle,
  CheckCircle2,
  Pause,
  Play
} from "lucide-react";
import { useAttendance } from '@/lib/stores/attendance-store';
import { useAuth } from '@/lib/hooks/useAuth';
import { AttendanceRecord, BreakRecord } from '@/lib/types/dashboard';
import { format, differenceInMinutes } from 'date-fns';

interface AttendanceDashboardProps {
  className?: string;
  showSupervisorView?: boolean;
}

export function AttendanceDashboard({ className, showSupervisorView = false }: AttendanceDashboardProps) {
  const { user } = useAuth();
  const {
    attendanceRecords,
    currentUserAttendance,
    attendanceMetrics,
    isLoading,
    clockIn,
    clockOut,
    startBreak,
    endBreak,
    getAttendanceByAgent,
    isOnBreak,
    isClockedIn,
    getCurrentBreak,
    getTotalWorkTime,
  } = useAttendance();

  const [currentTime, setCurrentTime] = useState(new Date());

  // Update current time every minute
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 60000);

    return () => clearInterval(timer);
  }, []);

  const userAttendance = user ? getAttendanceByAgent(user.id) : null;
  const userIsClockedIn = user ? isClockedIn(user.id) : false;
  const userIsOnBreak = user ? isOnBreak(user.id) : false;
  const currentBreak = user ? getCurrentBreak(user.id) : null;
  const userWorkTime = user ? getTotalWorkTime(user.id) : 0;

  const handleClockIn = async () => {
    if (!user) return;
    await clockIn(user.id, `${user.first_name} ${user.last_name}`);
  };

  const handleClockOut = async () => {
    if (!user) return;
    await clockOut(user.id);
  };

  const handleStartBreak = async (type: BreakRecord['type']) => {
    if (!user) return;
    await startBreak(user.id, type);
  };

  const handleEndBreak = async () => {
    if (!user) return;
    await endBreak(user.id);
  };

  const formatTime = (minutes: number): string => {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    return `${hours}h ${mins}m`;
  };

  const formatTimeHHMM = (date: Date): string => {
    return format(date, 'HH:mm');
  };

  const getStatusColor = (status: AttendanceRecord['status']) => {
    switch (status) {
      case 'active':
        return 'bg-green-500';
      case 'on-break':
        return 'bg-yellow-500';
      case 'offline':
        return 'bg-gray-500';
      default:
        return 'bg-gray-500';
    }
  };

  const getStatusLabel = (status: AttendanceRecord['status']) => {
    switch (status) {
      case 'active':
        return 'Active';
      case 'on-break':
        return 'On Break';
      case 'offline':
        return 'Offline';
      default:
        return 'Unknown';
    }
  };

  return (
    <div className={className}>
      <Tabs defaultValue="personal" className="space-y-6">
        <TabsList>
          <TabsTrigger value="personal">My Attendance</TabsTrigger>
          {showSupervisorView && (
            <TabsTrigger value="team">Team Overview</TabsTrigger>
          )}
          <TabsTrigger value="metrics">Metrics</TabsTrigger>
        </TabsList>

        {/* Personal Attendance Tab */}
        <TabsContent value="personal" className="space-y-6">
          {/* Current Status Card */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Clock className="h-5 w-5" />
                Current Status
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <Avatar>
                    <AvatarImage src="/placeholder.svg" alt={user?.first_name} />
                    <AvatarFallback>
                      {user?.first_name?.[0]}{user?.last_name?.[0]}
                    </AvatarFallback>
                  </Avatar>
                  <div>
                    <h3 className="font-medium">{user?.first_name} {user?.last_name}</h3>
                    <p className="text-sm text-muted-foreground">
                      {format(currentTime, 'EEEE, MMMM do, yyyy - HH:mm')}
                    </p>
                  </div>
                </div>
                
                <div className="flex items-center gap-2">
                  <Badge 
                    variant={userIsClockedIn ? (userIsOnBreak ? "secondary" : "default") : "outline"}
                    className="flex items-center gap-1"
                  >
                    <div className={`w-2 h-2 rounded-full ${
                      userIsClockedIn ? (userIsOnBreak ? 'bg-yellow-500' : 'bg-green-500') : 'bg-gray-500'
                    }`} />
                    {userIsClockedIn ? (userIsOnBreak ? 'On Break' : 'Active') : 'Offline'}
                  </Badge>
                </div>
              </div>

              {/* Clock In/Out Actions */}
              <div className="flex gap-2">
                {!userIsClockedIn ? (
                  <Button onClick={handleClockIn} disabled={isLoading} className="flex items-center gap-2">
                    <LogIn className="h-4 w-4" />
                    Clock In
                  </Button>
                ) : (
                  <>
                    {!userIsOnBreak ? (
                      <>
                        <Button 
                          variant="outline" 
                          onClick={() => handleStartBreak('tea')}
                          disabled={isLoading}
                          className="flex items-center gap-2"
                        >
                          <Coffee className="h-4 w-4" />
                          Tea Break
                        </Button>
                        <Button 
                          variant="outline" 
                          onClick={() => handleStartBreak('lunch')}
                          disabled={isLoading}
                          className="flex items-center gap-2"
                        >
                          <Coffee className="h-4 w-4" />
                          Lunch Break
                        </Button>
                        <Button 
                          variant="outline" 
                          onClick={() => handleStartBreak('other')}
                          disabled={isLoading}
                          className="flex items-center gap-2"
                        >
                          <Pause className="h-4 w-4" />
                          Other Break
                        </Button>
                      </>
                    ) : (
                      <Button 
                        onClick={handleEndBreak}
                        disabled={isLoading}
                        className="flex items-center gap-2"
                      >
                        <Play className="h-4 w-4" />
                        End Break
                      </Button>
                    )}
                    <Button 
                      variant="destructive" 
                      onClick={handleClockOut}
                      disabled={isLoading}
                      className="flex items-center gap-2"
                    >
                      <LogOut className="h-4 w-4" />
                      Clock Out
                    </Button>
                  </>
                )}
              </div>

              {/* Current Session Info */}
              {userAttendance && (
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 pt-4 border-t">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-blue-600">
                      {formatTimeHHMM(userAttendance.loginTime)}
                    </div>
                    <div className="text-sm text-muted-foreground">Clock In</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-green-600">
                      {formatTime(userWorkTime)}
                    </div>
                    <div className="text-sm text-muted-foreground">Work Time</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-yellow-600">
                      {formatTime(userAttendance.totalBreakTime)}
                    </div>
                    <div className="text-sm text-muted-foreground">Break Time</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-purple-600">
                      {userAttendance.productivity}%
                    </div>
                    <div className="text-sm text-muted-foreground">Productivity</div>
                  </div>
                </div>
              )}

              {/* Current Break Info */}
              {currentBreak && (
                <div className="p-3 bg-yellow-50 border border-yellow-200 rounded-md">
                  <div className="flex items-center gap-2 text-yellow-800">
                    <Coffee className="h-4 w-4" />
                    <span className="font-medium">
                      Currently on {currentBreak.type} break
                    </span>
                  </div>
                  <div className="text-sm text-yellow-700 mt-1">
                    Started at {formatTimeHHMM(currentBreak.startTime)} • 
                    Duration: {differenceInMinutes(currentTime, currentBreak.startTime)} minutes
                  </div>
                  {currentBreak.reason && (
                    <div className="text-sm text-yellow-600 mt-1">
                      Reason: {currentBreak.reason}
                    </div>
                  )}
                </div>
              )}
            </CardContent>
          </Card>

          {/* Today's Breaks */}
          {userAttendance && userAttendance.breaks.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle>Today's Breaks</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {userAttendance.breaks.map((breakRecord) => (
                    <div key={breakRecord.id} className="flex items-center justify-between p-3 border rounded-md">
                      <div className="flex items-center gap-3">
                        <div className={`w-3 h-3 rounded-full ${
                          breakRecord.type === 'tea' ? 'bg-blue-500' :
                          breakRecord.type === 'lunch' ? 'bg-green-500' : 'bg-purple-500'
                        }`} />
                        <div>
                          <div className="font-medium capitalize">{breakRecord.type} Break</div>
                          {breakRecord.reason && (
                            <div className="text-sm text-muted-foreground">{breakRecord.reason}</div>
                          )}
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="text-sm font-medium">
                          {formatTimeHHMM(breakRecord.startTime)} - {
                            breakRecord.endTime ? formatTimeHHMM(breakRecord.endTime) : 'Ongoing'
                          }
                        </div>
                        <div className="text-sm text-muted-foreground">
                          {breakRecord.duration ? `${breakRecord.duration} min` : 'In progress'}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        {/* Team Overview Tab */}
        {showSupervisorView && (
          <TabsContent value="team" className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center gap-2">
                    <Users className="h-5 w-5 text-blue-500" />
                    <div>
                      <div className="text-2xl font-bold">{attendanceMetrics.totalAgents}</div>
                      <div className="text-sm text-muted-foreground">Total Agents</div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center gap-2">
                    <CheckCircle2 className="h-5 w-5 text-green-500" />
                    <div>
                      <div className="text-2xl font-bold">{attendanceMetrics.activeAgents}</div>
                      <div className="text-sm text-muted-foreground">Active</div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center gap-2">
                    <Coffee className="h-5 w-5 text-yellow-500" />
                    <div>
                      <div className="text-2xl font-bold">{attendanceMetrics.onBreakAgents}</div>
                      <div className="text-sm text-muted-foreground">On Break</div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center gap-2">
                    <AlertCircle className="h-5 w-5 text-gray-500" />
                    <div>
                      <div className="text-2xl font-bold">{attendanceMetrics.offlineAgents}</div>
                      <div className="text-sm text-muted-foreground">Offline</div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Team Attendance List */}
            <Card>
              <CardHeader>
                <CardTitle>Team Attendance</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {attendanceRecords.map((record) => (
                    <div key={record.id} className="flex items-center justify-between p-3 border rounded-md">
                      <div className="flex items-center gap-3">
                        <Avatar className="h-8 w-8">
                          <AvatarFallback className="text-xs">
                            {record.agentName.split(' ').map(n => n[0]).join('')}
                          </AvatarFallback>
                        </Avatar>
                        <div>
                          <div className="font-medium">{record.agentName}</div>
                          <div className="text-sm text-muted-foreground">
                            Clocked in at {formatTimeHHMM(record.loginTime)}
                          </div>
                        </div>
                      </div>
                      
                      <div className="flex items-center gap-4">
                        <div className="text-right">
                          <div className="text-sm font-medium">{formatTime(record.totalWorkTime)}</div>
                          <div className="text-xs text-muted-foreground">Work time</div>
                        </div>
                        <div className="text-right">
                          <div className="text-sm font-medium">{record.productivity}%</div>
                          <div className="text-xs text-muted-foreground">Productivity</div>
                        </div>
                        <Badge 
                          variant={record.status === 'active' ? 'default' : record.status === 'on-break' ? 'secondary' : 'outline'}
                          className="flex items-center gap-1"
                        >
                          <div className={`w-2 h-2 rounded-full ${getStatusColor(record.status)}`} />
                          {getStatusLabel(record.status)}
                        </Badge>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        )}

        {/* Metrics Tab */}
        <TabsContent value="metrics" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm">Attendance Rate</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{attendanceMetrics.attendanceRate.toFixed(1)}%</div>
                <Progress value={attendanceMetrics.attendanceRate} className="mt-2" />
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm">Avg Work Time</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{formatTime(attendanceMetrics.averageWorkTime)}</div>
                <div className="text-xs text-muted-foreground mt-1">Per agent today</div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm">Avg Break Time</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{formatTime(attendanceMetrics.averageBreakTime)}</div>
                <div className="text-xs text-muted-foreground mt-1">Per agent today</div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm">Team Productivity</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{attendanceMetrics.productivityScore.toFixed(1)}%</div>
                <Progress value={attendanceMetrics.productivityScore} className="mt-2" />
              </CardContent>
            </Card>
          </div>

          {/* Additional Metrics */}
          <Card>
            <CardHeader>
              <CardTitle>Attendance Insights</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="text-center">
                  <div className="text-3xl font-bold text-green-600">
                    {((attendanceMetrics.activeAgents / attendanceMetrics.totalAgents) * 100).toFixed(0)}%
                  </div>
                  <div className="text-sm text-muted-foreground">Agents Currently Active</div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-blue-600">
                    {formatTime(attendanceMetrics.averageWorkTime)}
                  </div>
                  <div className="text-sm text-muted-foreground">Average Work Time</div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-purple-600">
                    {attendanceMetrics.productivityScore.toFixed(1)}%
                  </div>
                  <div className="text-sm text-muted-foreground">Overall Productivity</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}