'use client';

import { createContext, useContext, useEffect, useState } from 'react';
import { preloadAllSounds, canPlaySounds, SOUNDS, preloadSound } from '@/lib/utils/sound';

// Define the context type
interface SoundContextType {
  soundsPreloaded: boolean;
  canPlaySounds: boolean;
}

// Create the context with default values
const SoundContext = createContext<SoundContextType>({
  soundsPreloaded: false,
  canPlaySounds: false,
});

// Hook to use the sound context
export const useSoundContext = () => useContext(SoundContext);

export function SoundProvider({ children }: { children: React.ReactNode }) {
  const [soundsPreloaded, setSoundsPreloaded] = useState(false);
  const [canPlay, setCanPlay] = useState(false);

  // Preload sounds when the component mounts
  useEffect(() => {
    console.log('SoundProvider: Initializing sound system');
    
    // Preload all sounds
    preloadAllSounds();
    setSoundsPreloaded(true);
    
    // Check if we can play sounds initially
    setCanPlay(canPlaySounds());
    
    // Set up event listeners to detect when user interaction enables sound
    const checkCanPlay = () => {
      if (!canPlay && canPlaySounds()) {
        console.log('SoundProvider: User interaction detected, sounds can now be played');
        setCanPlay(true);
        
        // Try to preload sounds again now that we have user interaction
        preloadAllSounds();
      }
    };
    
    // Listen for user interaction events
    const interactionEvents = ['click', 'touchstart', 'keydown'];
    interactionEvents.forEach(event => {
      window.addEventListener(event, checkCanPlay);
    });
    
    // Clean up event listeners
    return () => {
      interactionEvents.forEach(event => {
        window.removeEventListener(event, checkCanPlay);
      });
    };
  }, [canPlay]);

  // Provide the sound context to children
  return (
    <SoundContext.Provider value={{ soundsPreloaded, canPlaySounds: canPlay }}>
      {children}
    </SoundContext.Provider>
  );
}
