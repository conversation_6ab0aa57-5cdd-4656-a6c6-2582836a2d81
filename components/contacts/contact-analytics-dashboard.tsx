"use client"

import { useState } from "react"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Calendar, TrendingUp, TrendingDown, Users, Filter, RefreshCw } from "lucide-react"
import { useContactAnalytics, generateMockContactAnalytics } from "@/lib/hooks/useContactAnalytics"
import { useProducts } from "@/lib/hooks/useProducts"
import { ContactAnalyticsParams } from "@/lib/api/contacts"
import { PeriodSelector } from "@/components/dashboard/period-selector"
import { useAdvancedFilters } from "@/lib/stores/filter-store"
import { ContactTrendsChart } from "./contact-trends-chart"
import { ContactBreakdownCharts } from "./contact-breakdown-charts"

interface ContactAnalyticsDashboardProps {
  className?: string;
}

export function ContactAnalyticsDashboard({ className }: ContactAnalyticsDashboardProps) {
  const { filters } = useAdvancedFilters();
  const { data: products = [] } = useProducts();
  const [selectedProduct, setSelectedProduct] = useState<string>("all");
  const [selectedCategory, setSelectedCategory] = useState<string>("all");

  // Helper function to safely convert date to ISO string
  const toISOStringSafe = (date: Date | string | undefined): string | undefined => {
    if (!date) return undefined;

    try {
      // If it's already a string, assume it's an ISO string
      if (typeof date === 'string') return date;

      // If it's a Date object, convert to ISO string
      if (date instanceof Date) return date.toISOString();

      // If it's neither, try to create a Date object
      const dateObj = new Date(date);
      if (isNaN(dateObj.getTime())) return undefined;

      return dateObj.toISOString();
    } catch (error) {
      console.warn('Failed to convert date to ISO string:', date, error);
      return undefined;
    }
  };

  // Get default date range if no dates are available
  const getDefaultDateRange = () => {
    const now = new Date();
    const startOfCurrentMonth = new Date(now.getFullYear(), now.getMonth(), 1);
    const endOfCurrentMonth = new Date(now.getFullYear(), now.getMonth() + 1, 0);

    return {
      startDate: startOfCurrentMonth.toISOString(),
      endDate: endOfCurrentMonth.toISOString(),
    };
  };

  // Build analytics params with safe date handling
  const defaultDates = getDefaultDateRange();
  const analyticsParams: ContactAnalyticsParams = {
    startDate: toISOStringSafe(filters.period.startDate) || defaultDates.startDate,
    endDate: toISOStringSafe(filters.period.endDate) || defaultDates.endDate,
    ...(selectedProduct !== "all" ? { productId: selectedProduct } : {}),
    ...(selectedCategory !== "all" ? { category: selectedCategory } : {}),
  };

  // For now, use mock data since the API endpoint might not exist yet
  const mockData = generateMockContactAnalytics(analyticsParams);
  const { data: analyticsData = mockData, isLoading, refetch } = useContactAnalytics(analyticsParams);

  const formatNumber = (num: number) => {
    return new Intl.NumberFormat().format(num);
  };

  const formatPercentage = (num: number) => {
    const sign = num >= 0 ? "+" : "";
    return `${sign}${num.toFixed(1)}%`;
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header with filters */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Contact Analytics</h2>
          <p className="text-muted-foreground">
            Track contact acquisition and growth trends
          </p>
        </div>
        
        <div className="flex items-center gap-2">
          <PeriodSelector className="flex-shrink-0" />
          <Button
            variant="outline"
            size="sm"
            onClick={() => refetch()}
            disabled={isLoading}
          >
            <RefreshCw className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
          </Button>
        </div>
      </div>

      {/* Filters */}
      <div className="flex flex-wrap items-center gap-4">
        <div className="flex items-center gap-2">
          <Filter className="h-4 w-4 text-muted-foreground" />
          <span className="text-sm font-medium">Filters:</span>
        </div>
        
        <Select value={selectedProduct} onValueChange={setSelectedProduct}>
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="Select product" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Products</SelectItem>
            {products.map((product) => (
              <SelectItem key={product.id} value={product.id}>
                {product.name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>

        <Select value={selectedCategory} onValueChange={setSelectedCategory}>
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="Select category" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Categories</SelectItem>
            <SelectItem value="customer">Customer</SelectItem>
            <SelectItem value="lead">Lead</SelectItem>
            <SelectItem value="partner">Partner</SelectItem>
            <SelectItem value="vendor">Vendor</SelectItem>
            <SelectItem value="other">Other</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Metrics Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Contacts</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatNumber(analyticsData.metrics.totalContacts)}</div>
            <p className="text-xs text-muted-foreground">
              In selected period
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">New Contacts</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatNumber(analyticsData.metrics.newContacts)}</div>
            <p className="text-xs text-muted-foreground">
              Created in period
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Growth Rate</CardTitle>
            {analyticsData.metrics.growthRate >= 0 ? (
              <TrendingUp className="h-4 w-4 text-green-600" />
            ) : (
              <TrendingDown className="h-4 w-4 text-red-600" />
            )}
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {formatPercentage(analyticsData.metrics.growthRate)}
            </div>
            <p className="text-xs text-muted-foreground">
              vs previous period
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Previous Period</CardTitle>
            <Calendar className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatNumber(analyticsData.metrics.previousPeriodTotal)}</div>
            <p className="text-xs text-muted-foreground">
              Comparison baseline
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Charts */}
      <div className="grid gap-6 lg:grid-cols-2">
        <ContactTrendsChart data={analyticsData.trends} isLoading={isLoading} />
        <ContactBreakdownCharts 
          productData={analyticsData.productBreakdown}
          categoryData={analyticsData.categoryBreakdown}
          isLoading={isLoading}
        />
      </div>
    </div>
  );
}
