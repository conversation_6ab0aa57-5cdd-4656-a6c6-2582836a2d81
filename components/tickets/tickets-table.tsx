import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { TicketActions } from "@/components/tickets/ticket-actions"
import { PriorityBadge } from "@/components/tickets/priority-badge"
import { StatusBadge } from "@/components/tickets/status-badge"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import type { Category, Ticket } from "@/lib/api/types"
import { useCategories } from "@/lib/hooks/useCategories"
import { useState } from "react"
import { DatePickerWithRange } from "@/components/ui/date-picker-with-range"
import { Button } from "@/components/ui/button"
import { Download } from "lucide-react"
import { format } from "date-fns"

interface TicketsTableProps {
  tickets: Ticket[]
  onViewTicket: (ticket: Ticket) => void
  onAssignTicket: (ticket: Ticket) => void
  onEscalateTicket: (ticket: Ticket) => void
}

export function TicketsTable({ tickets, onViewTicket, onAssignTicket, onEscalateTicket }: TicketsTableProps) {
  const { data: categories = [], isLoading: isLoadingCategories } = useCategories();
  const [dateRange, setDateRange] = useState<{ from: Date | null; to: Date | null }>({ from: null, to: null });

  // Filter tickets by date range
  const filteredTickets = tickets.filter(ticket => {
    if (!dateRange.from && !dateRange.to) return true;
    const created = new Date(ticket.created_at);
    if (dateRange.from && created < dateRange.from) return false;
    if (dateRange.to && created > dateRange.to) return false;
    return true;
  });

  // CSV export
  const handleExport = () => {
    const headers = [
      "Customer", "Description", "Category", "Priority", "Status", "Created By", "Assigned To", "SLA"
    ];
    const rows = filteredTickets.map(ticket => [
      `${ticket.contact.first_name} ${ticket.contact.last_name}`,
      ticket.description,
      categories.find((c: Category) => c.id === ticket.category_id)?.name || 'Unknown',
      ticket.priority,
      ticket.status,
      ticket.creator && typeof ticket.creator === 'object'
        ? `${ticket.creator.first_name || ''} ${ticket.creator.last_name || ''}`
        : 'Unknown',
      ticket.assignee
        ? `${ticket.assignee.first_name} ${ticket.assignee.last_name}`
        : 'Unassigned',
      ticket.status // SLA placeholder
    ]);
    const csv = [headers, ...rows].map(row => row.map(String).map(cell => `"${cell.replace(/"/g, '""')}"`).join(",")).join("\n");
    const blob = new Blob([csv], { type: 'text/csv' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `tickets-${format(new Date(), 'yyyyMMdd')}.csv`;
    a.click();
    URL.revokeObjectURL(url);
  };

  // Create a map of category IDs to category names for efficient lookup
  const categoryMap = new Map(categories.map((category: Category) => [category.id, category.name]));
  return (
    <div>
      <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4 mb-2">
        <DatePickerWithRange date={dateRange} setDate={setDateRange} />
        <Button variant="outline" onClick={handleExport} className="ml-auto">
          <Download className="mr-2 size-4" />
          Export CSV
        </Button>
      </div>
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Customer</TableHead>
            <TableHead>Description</TableHead>
            <TableHead>Category</TableHead>
            <TableHead>Priority</TableHead>
            <TableHead>Status</TableHead>
            <TableHead>Created By</TableHead>
            {/* <TableHead>Assigned To</TableHead> */}
            <TableHead>SLA</TableHead>
            <TableHead className="text-right">Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {filteredTickets.map((ticket) => (
            <TableRow key={ticket.id}>
              <TableCell>{ticket.contact.first_name} {ticket.contact.last_name}</TableCell>
              <TableCell className="max-w-[200px] truncate">{ticket.description}</TableCell>
              <TableCell>
                {isLoadingCategories ? (
                  <span className="animate-pulse h-4 w-20 bg-muted rounded" />
                ) : (
                  categoryMap.get(ticket.category_id) || 'Unknown'
                )}
              </TableCell>
              <TableCell>
              <PriorityBadge priority={ticket.priority.toLowerCase() as "low" | "medium" | "high" | "urgent"} />
              </TableCell>
              <TableCell>
              <StatusBadge status={ticket.status.toLowerCase() as "open" | "in_progress" | "pending" | "resolved" | "closed"} />
              </TableCell>
              <TableCell>
                {ticket.creator && typeof ticket.creator === 'object' ? (
                  <div className="flex items-center gap-2">
                    <Avatar className="h-6 w-6">
                      <AvatarImage
                        src={"/placeholder.svg?height=24&width=24"}
                        alt={`${(ticket.creator as any).first_name || ''} ${(ticket.creator as any).last_name || ''}`}
                      />
                      <AvatarFallback>
                        {`${((ticket.creator as any).first_name || '')[0] || '?'}${((ticket.creator as any).last_name || '')[0] || '?'}`}
                      </AvatarFallback>
                    </Avatar>
                    <span className="text-xs">
                      {(ticket.creator as any).first_name || ''} {(ticket.creator as any).last_name || ''}
                    </span>
                  </div>
                ) : (
                  <span className="text-xs text-muted-foreground">Unknown</span>
                )}
              </TableCell>
              {/* <TableCell>
                {ticket.assignee ? (
                  <div className="flex items-center gap-2">
                    <Avatar className="h-6 w-6">
                      <AvatarImage
                        src={"/placeholder.svg?height=24&width=24"}
                        alt={`${ticket.assignee.first_name} ${ticket.assignee.last_name}`}
                      />
                      <AvatarFallback>
                        {`${ticket.assignee.first_name[0]}${ticket.assignee.last_name[0]}`}
                      </AvatarFallback>
                    </Avatar>
                    <span className="text-xs">
                      {ticket.assignee.first_name} {ticket.assignee.last_name}
                    </span>
                  </div>
                ) : (
                  <Badge variant="outline">Unassigned</Badge>
                )}
              </TableCell> */}
              <TableCell>
                <div className="flex gap-1 flex-wrap">
                  <StatusBadge status={ticket.status as any} />
                  {ticket.escalation_reason && (
                    <Badge variant="outline" className="bg-amber-100 text-amber-800 hover:bg-amber-200 border-amber-200">
                      Escalated
                    </Badge>
                  )}
                </div>
              </TableCell>
              <TableCell className="text-right">
                <TicketActions
                  ticket={ticket}
                  onView={() => onViewTicket(ticket)}
                  onAssign={() => onAssignTicket(ticket)}
                  onEscalate={() => onEscalateTicket(ticket)}
                />
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  )
}