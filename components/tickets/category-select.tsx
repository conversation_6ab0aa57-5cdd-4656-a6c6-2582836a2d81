"use client"

import { useCategories, useSubcategories } from "@/lib/hooks/useCategories"
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { useState } from "react"

interface CategorySelectProps {
  onCategoryChange: (categoryId: string) => void
  defaultValue?: string
  error?: string
}

export function CategorySelect({
  onCategoryChange,
  defaultValue,
  error,
}: CategorySelectProps) {
  const [selectedMainCategory, setSelectedMainCategory] = useState<string>("")
  const { data: categories, isLoading: isLoadingCategories } = useCategories()
  const { data: subcategories, isLoading: isLoadingSubcategories } =
    useSubcategories(selectedMainCategory)

  const handleMainCategoryChange = (value: string) => {
    setSelectedMainCategory(value)
    // If the category has no subcategories, use it as the final selection
    if (!subcategories || subcategories.length === 0) {
      onCategoryChange(value)
    }
  }

  const handleSubcategoryChange = (value: string) => {
    onCategoryChange(value)
  }

  if (isLoadingCategories) {
    return <div>Loading categories...</div>
  }

  return (
    <div className="space-y-2">
      <Select onValueChange={handleMainCategoryChange} defaultValue={defaultValue}>
        <SelectTrigger>
          <SelectValue placeholder="Select a category" />
        </SelectTrigger>
        <SelectContent>
          <SelectGroup>
            <SelectLabel>Categories</SelectLabel>
            {categories?.map((category) => (
              <SelectItem key={category.id} value={category.id}>
                {category.name}
              </SelectItem>
            ))}
          </SelectGroup>
        </SelectContent>
      </Select>

      {selectedMainCategory && subcategories && subcategories.length > 0 && (
        <Select onValueChange={handleSubcategoryChange}>
          <SelectTrigger>
            <SelectValue placeholder="Select a subcategory" />
          </SelectTrigger>
          <SelectContent>
            <SelectGroup>
              <SelectLabel>Subcategories</SelectLabel>
              {subcategories.map((subcategory) => (
                <SelectItem key={subcategory.id} value={subcategory.id}>
                  {subcategory.name}
                </SelectItem>
              ))}
            </SelectGroup>
          </SelectContent>
        </Select>
      )}

      {error && <p className="text-sm text-destructive">{error}</p>}
    </div>
  )
}
