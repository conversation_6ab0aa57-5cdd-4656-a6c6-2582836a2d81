import { Badge } from "@/components/ui/badge"

type Status = "open" | "in_progress" | "pending" | "resolved" | "closed"

interface StatusBadgeProps {
  status: Status
}

const statusConfig: Record<Status, { label: string; variant: "default" | "secondary" | "outline" | "success" }> = {
  open: { label: "Open", variant: "default" },
  in_progress: { label: "In Progress", variant: "secondary" },
  pending: { label: "Pending", variant: "outline" },
  resolved: { label: "Resolved", variant: "success" },
  closed: { label: "Closed", variant: "outline" }
}

export function StatusBadge({ status }: StatusBadgeProps) {
  const config = statusConfig[status]
  
  return (
    <Badge variant={config.variant}>
      {config.label}
    </Badge>
  )
}