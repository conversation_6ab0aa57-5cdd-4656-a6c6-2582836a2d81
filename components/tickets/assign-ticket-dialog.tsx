"use client"

import { useState } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Tit<PERSON> } from "@/components/ui/dialog"
import { But<PERSON> } from "@/components/ui/button"
import { useQuery } from "@tanstack/react-query"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { agentService } from "@/lib/api"
import type { Ticket } from "@/lib/api/types"
import { useTickets } from "@/lib/hooks/useTickets"
import { toast } from "sonner"
import { Loader2 } from "lucide-react"

interface AssignTicketDialogProps {
  ticket: Ticket
  open: boolean
  onOpenChange: (open: boolean) => void
}

export function AssignTicketDialog({ ticket, open, onOpenChange }: AssignTicketDialogProps) {
  const [selectedAgentId, setSelectedAgentId] = useState<string>("")
  const { assignTicket, isAssigningTicket } = useTickets()

  // Fetch agents
  const { data: agentsResponse = { data: [] } } = useQuery({
    queryKey: ['agents'],
    queryFn: () => agentService.getAllAgents(),
  })

  // Extract agents from the response
  const agents = agentsResponse.data || []

  // Add type for agents
  type Agent = {
    id: string;
    first_name: string;
    last_name: string;
  }

  const handleAssign = () => {
    if (!selectedAgentId) {
      toast.error("Please select an agent")
      return
    }

    assignTicket(
      {
        id: ticket.id,
        data: { assigned_agent_id: selectedAgentId }
      },
      {
        onSuccess: () => {
          toast.success("Ticket assigned successfully")
          onOpenChange(false)
          setSelectedAgentId("")
        },
        onError: (error) => {
          console.error("Failed to assign ticket:", error)
          toast.error(error instanceof Error ? error.message : "Failed to assign ticket")
        }
      }
    )
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Assign Ticket #{ticket.title}</DialogTitle>
        </DialogHeader>

        <div className="grid gap-4 py-4">
        {agents.map((agent: Agent) => (
            <div
              key={agent.id}
              className={`flex items-center gap-4 p-4 rounded-lg cursor-pointer border transition-colors
                ${selectedAgentId === agent.id ? 'border-primary bg-primary/5' : 'border-secondary'}`}
              onClick={() => setSelectedAgentId(agent.id)}
            >
              <Avatar>
                <AvatarImage src={`https://api.dicebear.com/7.x/initials/svg?seed=${agent.first_name} ${agent.last_name}`} />
                <AvatarFallback>{`${agent.first_name[0]}${agent.last_name[0]}`}</AvatarFallback>
              </Avatar>
              <div>
                <div className="font-medium">{`${agent.first_name} ${agent.last_name}`}</div>
              </div>
            </div>
          ))}
        </div>

        <div className="flex justify-end gap-4">
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancel
          </Button>
          <Button
            onClick={handleAssign}
            disabled={!selectedAgentId || isAssigningTicket}
          >
            {isAssigningTicket && <Loader2 className="mr-2 size-4 animate-spin" />}
            Assign Ticket
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  )
}