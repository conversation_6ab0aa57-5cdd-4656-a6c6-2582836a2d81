import { Badge } from "@/components/ui/badge"

type Priority = "low" | "medium" | "high" | "urgent"

interface PriorityBadgeProps {
  priority: Priority
}

const priorityConfig: Record<Priority, { label: string; variant: "default" | "secondary" | "destructive" | "outline" }> = {
  low: { label: "Low", variant: "secondary" },
  medium: { label: "Medium", variant: "default" },
  high: { label: "High", variant: "destructive" },
  urgent: { label: "Urgent", variant: "destructive" }
}

export function PriorityBadge({ priority }: PriorityBadgeProps) {
  const config = priorityConfig[priority]
  
  return (
    <Badge variant={config.variant}>
      {config.label}
    </Badge>
  )
}