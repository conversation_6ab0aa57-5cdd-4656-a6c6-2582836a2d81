import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  <PERSON><PERSON>Footer,
  <PERSON>alogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { useTickets } from "@/lib/hooks/useTickets";
import { Ticket, TicketPriority, TicketStatus } from "@/lib/api/types";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { toast } from "sonner";
import { z } from "zod";
import { CategorySelect } from "../categories/category-select";

const updateTicketSchema = z.object({
  title: z.string().min(1, "Title is required"),
  description: z.string().min(1, "Description is required"),
  priority: z.nativeEnum(TicketPriority),
  status: z.nativeEnum(TicketStatus),
  category_id: z.string().min(1, "Category is required"),
  comment: z.string().optional(),
  is_internal: z.boolean().optional(),
});

type UpdateTicketFormValues = z.infer<typeof updateTicketSchema>;

export interface UpdateTicketDialogProps {
  ticket: Ticket;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onTicketUpdated?: () => void;
}

export function UpdateTicketDialog({
  ticket,
  open,
  onOpenChange,
  onTicketUpdated,
}: UpdateTicketDialogProps) {
  const { updateTicket, isUpdatingTicket } = useTickets();

  const form = useForm<UpdateTicketFormValues>({
    resolver: zodResolver(updateTicketSchema),
    defaultValues: {
      title: ticket.title,
      description: ticket.description,
      priority: ticket.priority as TicketPriority,
      status: ticket.status as TicketStatus,
      category_id: ticket.category_id,
      comment: "",
      is_internal: false,
    },
  });

  const onSubmit = async (data: UpdateTicketFormValues) => {
    try {
      await updateTicket({
        id: ticket.id,
        data,
      });
      toast.success("Ticket updated successfully");
      onOpenChange(false);
      form.reset();
      onTicketUpdated?.();
    } catch (error) {
      console.error("Failed to update ticket:", error);
      toast.error("Failed to update ticket");
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <form onSubmit={form.handleSubmit(onSubmit)}>
          <DialogHeader>
            <DialogTitle>Update Ticket</DialogTitle>
            <DialogDescription>
              Make changes to the ticket details below.
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <Input
                {...form.register("title")}
                placeholder="Enter ticket title"
              />
              {form.formState.errors.title && (
                <p className="text-sm text-red-500">
                  {form.formState.errors.title.message}
                </p>
              )}
            </div>
            <div className="grid gap-2">
              <Textarea
                {...form.register("description")}
                placeholder="Enter ticket description"
                className="min-h-[100px]"
              />
              {form.formState.errors.description && (
                <p className="text-sm text-red-500">
                  {form.formState.errors.description.message}
                </p>
              )}
            </div>
            <div className="grid gap-2">
              <CategorySelect
                value={form.getValues("category_id")}
                onValueChange={(value: string) => form.setValue("category_id", value)}
              />
              {form.formState.errors.category_id && (
                <p className="text-sm text-red-500">
                  {form.formState.errors.category_id.message}
                </p>
              )}
            </div>
            <div className="grid gap-2">
              <Select
                {...form.register("priority")}
                onValueChange={(value) =>
                  form.setValue("priority", value as TicketPriority)
                }
                defaultValue={form.getValues("priority")}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select priority" />
                </SelectTrigger>
                <SelectContent>
                  {Object.values(TicketPriority).map((priority) => (
                    <SelectItem key={priority} value={priority}>
                      {priority.charAt(0).toUpperCase() + priority.slice(1)}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {form.formState.errors.priority && (
                <p className="text-sm text-red-500">
                  {form.formState.errors.priority.message}
                </p>
              )}
            </div>
            <div className="grid gap-2">
              <Select
                {...form.register("status")}
                onValueChange={(value) =>
                  form.setValue("status", value as TicketStatus)
                }
                defaultValue={form.getValues("status")}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select status" />
                </SelectTrigger>
                <SelectContent>
                  {Object.values(TicketStatus).map((status) => (
                    <SelectItem key={status} value={status}>
                      {status
                        .split("_")
                        .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
                        .join(" ")}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {form.formState.errors.status && (
                <p className="text-sm text-red-500">
                  {form.formState.errors.status.message}
                </p>
              )}
            </div>
            <div className="grid gap-2">
              <Textarea
                {...form.register("comment")}
                placeholder="Add a comment (optional)"
                className="min-h-[100px]"
              />
            </div>
          </div>
          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => onOpenChange(false)}
            >
              Cancel
            </Button>
            <Button type="submit" disabled={isUpdatingTicket}>
              {isUpdatingTicket ? "Updating..." : "Update"}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
