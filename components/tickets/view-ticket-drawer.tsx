"use client"

import { useState, useEffect } from "react"
import {
  <PERSON>er,
  Drawer<PERSON>ontent,
  <PERSON>er<PERSON>es<PERSON>,
  Drawer<PERSON>eader,
  <PERSON>er<PERSON>it<PERSON>,
} from "@/components/ui/drawer"
import { Ta<PERSON>, <PERSON>bs<PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { But<PERSON> } from "@/components/ui/button"
import { Textarea } from "@/components/ui/textarea"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Separator } from "@/components/ui/separator"
import { PriorityBadge } from "./priority-badge"
import { StatusBadge } from "./status-badge"
import { EscalateTicketDialog } from "./escalate-ticket-dialog";
import { useTickets } from "@/lib/hooks/useTickets"
import { useToast } from "@/components/ui/use-toast"
import {
  AlertDialog,
  AlertDialogAction,
  AlertD<PERSON>ogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { format } from "date-fns"
import { CalendarIcon, Edit, Trash2, RefreshCw, AlertTriangle } from "lucide-react"
import { Calendar } from "@/components/ui/calendar"
import { CategorySelect } from "@/components/tickets/category-select"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"

import type { Category, Ticket, TicketComment, TicketStatus, TicketPriority } from "@/lib/api/types"
import { useCategories } from "@/lib/hooks/useCategories"

interface ViewTicketDrawerProps {
  ticket: Ticket
  open: boolean
  onOpenChange: (open: boolean) => void
}

// TicketCommentItem component to display a single comment
interface TicketCommentItemProps {
  comment: TicketComment;
}

function TicketCommentItem({ comment }: TicketCommentItemProps) {
  const formattedDate = new Date(comment.created_at).toLocaleString();

  return (
    <div className="border rounded-md p-4 mb-4">
      <div className="flex items-center gap-2 mb-2">
        <Avatar className="h-8 w-8">
          <AvatarImage
            src={`https://api.dicebear.com/7.x/initials/svg?seed=${comment.user?.first_name} ${comment.user?.last_name}`}
          />
          <AvatarFallback>
            {comment.user ? `${comment.user.first_name[0]}${comment.user.last_name[0]}` : 'U'}
          </AvatarFallback>
        </Avatar>
        <div>
          <div className="font-medium">
            {comment.user ? `${comment.user.first_name} ${comment.user.last_name}` : 'Unknown User'}
          </div>
          <div className="text-xs text-muted-foreground">{formattedDate}</div>
        </div>
        {comment.is_internal && (
          <span className="ml-auto text-xs bg-yellow-100 text-yellow-800 px-2 py-1 rounded-full">
            Internal
          </span>
        )}
      </div>
      <p className="text-sm">{comment.content}</p>
    </div>
  );
}

// TicketCommentForm component for adding new comments
interface TicketCommentFormProps {
  ticketId: string;
  onCommentAdded: () => void;
}

function TicketCommentForm({ ticketId, onCommentAdded }: TicketCommentFormProps) {
  const [comment, setComment] = useState('');
  const [isInternal, setIsInternal] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { addTicketComment } = useTickets();
  const { toast } = useToast();

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!comment.trim()) return;

    setIsSubmitting(true);
    addTicketComment(
      {
        ticketId,
        data: {
          content: comment,
          is_internal: isInternal
        }
      },
      {
        onSuccess: () => {
          setComment('');
          setIsInternal(false);
          onCommentAdded();
          toast({
            title: 'Comment added',
            description: 'Your comment has been added successfully',
          });
        },
        onError: (error) => {
          toast({
            title: 'Error',
            description: 'Failed to add comment. Please try again.',
            variant: 'destructive',
          });
          console.error('Failed to add comment:', error);
        },
        onSettled: () => {
          setIsSubmitting(false);
        }
      }
    );
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div>
        <Textarea
          placeholder="Add a comment..."
          value={comment}
          onChange={(e) => setComment(e.target.value)}
          className="min-h-[100px]"
        />
      </div>
      <div className="flex items-center justify-between">
        <label className="flex items-center space-x-2 text-sm">
          <input
            type="checkbox"
            checked={isInternal}
            onChange={(e) => setIsInternal(e.target.checked)}
            className="rounded"
          />
          <span>Internal note (only visible to staff)</span>
        </label>
        <Button type="submit" disabled={isSubmitting || !comment.trim()}>
          {isSubmitting ? 'Adding...' : 'Add Comment'}
        </Button>
      </div>
    </form>
  );
}

// UpdateTicketDialog component for editing ticket details
interface UpdateTicketDialogProps {
  ticket: Ticket;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onTicketUpdated: () => void;
}

function UpdateTicketDialog({ ticket, open, onOpenChange, onTicketUpdated }: UpdateTicketDialogProps) {
  const [description, setDescription] = useState(ticket.description);
  const [priority, setPriority] = useState(ticket.priority);
  const [status, setStatus] = useState(ticket.status);
  const [categoryId, setCategoryId] = useState(ticket.category_id);
  const [dueDate, setDueDate] = useState<Date | undefined>(
    ticket.due_date ? new Date(ticket.due_date) : undefined
  );
  const [isSubmitting, setIsSubmitting] = useState(false);

  const { updateTicket } = useTickets();
  const { toast } = useToast();

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!description.trim()) return;

    setIsSubmitting(true);
    updateTicket(
      {
        id: ticket.id,
        data: {
          description,
          priority: priority.toLowerCase() as TicketPriority,
          status: status.toLowerCase() as TicketStatus,
          category_id: categoryId,
          due_date: dueDate ? dueDate.toISOString() : undefined,
          // Keep the same contact and assignment
          contact_id: ticket.contact_id,
          assigned_to: ticket.assigned_to,
        },
      },
      {
        onSuccess: () => {
          onOpenChange(false);
          onTicketUpdated();
          toast({
            title: 'Ticket updated',
            description: 'The ticket has been updated successfully',
          });
        },
        onError: (error) => {
          toast({
            title: 'Error',
            description: 'Failed to update ticket. Please try again.',
            variant: 'destructive',
          });
          console.error('Failed to update ticket:', error);
        },
        onSettled: () => {
          setIsSubmitting(false);
        },
      }
    );
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[525px]">
        <DialogHeader>
          <DialogTitle>Edit Ticket</DialogTitle>
          <DialogDescription>Update the ticket details below.</DialogDescription>
        </DialogHeader>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="description">Description</Label>
            <Textarea
              id="description"
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              className="min-h-[100px]"
              required
            />
          </div>
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="priority">Priority</Label>
              <Select value={priority} onValueChange={setPriority}>
                <SelectTrigger>
                  <SelectValue placeholder="Select priority" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="low">Low</SelectItem>
                  <SelectItem value="medium">Medium</SelectItem>
                  <SelectItem value="high">High</SelectItem>
                  <SelectItem value="urgent">Urgent</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label htmlFor="status">Status</Label>
              <Select value={status} onValueChange={setStatus}>
                <SelectTrigger>
                  <SelectValue placeholder="Select status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="open">Open</SelectItem>
                  <SelectItem value="in_progress">In Progress</SelectItem>
                  <SelectItem value="on_hold">On Hold</SelectItem>
                  <SelectItem value="resolved">Resolved</SelectItem>
                  <SelectItem value="closed">Closed</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
          <div className="space-y-2">
            <Label htmlFor="category">Category</Label>
            <CategorySelect
              value={categoryId}
              onChange={setCategoryId}
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="dueDate">Due Date</Label>
            <Popover>
              <PopoverTrigger asChild>
                <Button
                  variant="outline"
                  className="w-full justify-start text-left font-normal"
                >
                  {dueDate ? (
                    format(dueDate, "PPP")
                  ) : (
                    <span>Pick a date</span>
                  )}
                  <CalendarIcon className="ml-auto size-4 opacity-50" />
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0">
                <Calendar
                  mode="single"
                  selected={dueDate}
                  onSelect={setDueDate}
                  initialFocus
                />
              </PopoverContent>
            </Popover>
          </div>
          <DialogFooter>
            <Button type="button" variant="outline" onClick={() => onOpenChange(false)}>
              Cancel
            </Button>
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? 'Updating...' : 'Update Ticket'}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}

export function ViewTicketDrawer({ ticket, open, onOpenChange }: ViewTicketDrawerProps) {
  const { data: categories = [] } = useCategories();
  const categoryMap = new Map(categories.map((category: Category) => [category.id, category.name]));
  // State for ticket data and UI
  const [currentTicket, setCurrentTicket] = useState<Ticket>(ticket);
  const [activeTab, setActiveTab] = useState('details');
  const [comments, setComments] = useState<TicketComment[]>([]);
  const [isLoadingComments, setIsLoadingComments] = useState(false);
  const [isUpdateDialogOpen, setIsUpdateDialogOpen] = useState(false);
  const [isEscalateDialogOpen, setIsEscalateDialogOpen] = useState(false);

  const { refetch: refetchTicket } = useTickets().getTicketById(ticket.id);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);

  // Hooks
  const {
    getTicketById,
    getTicketComments,
    changeTicketStatus,
    deleteTicket
  } = useTickets();
  const { toast } = useToast();

  // Fetch ticket data and comments
  const fetchTicketData = () => {
    try {
      // Use the query result directly
      const ticketQuery = getTicketById(currentTicket.id);
      if (ticketQuery.data && !ticketQuery.isLoading) {
        setCurrentTicket(ticketQuery.data);
      }
    } catch (error) {
      console.error('Error fetching ticket data:', error);
      toast({
        title: 'Error',
        description: 'Failed to fetch ticket data. Please try again.',
        variant: 'destructive',
      });
    }
  };

  const fetchComments = async () => {
    try {
      setIsLoadingComments(true);
      const commentsData = await getTicketComments(currentTicket.id);

      if (commentsData) {
        // Handle different response structures with type assertions
        if ('comments' in commentsData && Array.isArray(commentsData.comments)) {
          setComments(commentsData.comments as TicketComment[]);
        } else if ('data' in commentsData && Array.isArray(commentsData.data)) {
          setComments(commentsData.data as TicketComment[]);
        } else {
          // If no recognized structure, set empty array
          setComments([]);
        }
      }
    } catch (error) {
      console.error('Error fetching comments:', error);
      toast({
        title: 'Error',
        description: 'Failed to fetch comments. Please try again.',
        variant: 'destructive',
      });
      setComments([]);
    } finally {
      setIsLoadingComments(false);
    }
  };

  // Effect to load comments when tab changes
  useEffect(() => {
    if (activeTab === 'comments') {
      fetchComments();
    }
  }, [activeTab, currentTicket.id]);

  // Handle status change
  const handleStatusChange = (newStatus: string) => {
    changeTicketStatus(
      { id: currentTicket.id, status: newStatus },
      {
        onSuccess: () => {
          fetchTicketData();
          toast({
            title: 'Status updated',
            description: `Ticket status changed to ${newStatus}`,
          });
        },
        onError: (error) => {
          toast({
            title: 'Error',
            description: 'Failed to update status. Please try again.',
            variant: 'destructive',
          });
          console.error('Failed to update status:', error);
        },
      }
    );
  };

  // Handle ticket deletion
  const handleDeleteTicket = () => {
    deleteTicket(currentTicket.id, {
      onSuccess: () => {
        onOpenChange(false);
        toast({
          title: 'Ticket deleted',
          description: 'The ticket has been deleted successfully',
        });
      },
      onError: (error) => {
        toast({
          title: 'Error',
          description: 'Failed to delete ticket. Please try again.',
          variant: 'destructive',
        });
        console.error('Failed to delete ticket:', error);
      },
    });
  };

  return (
    <>
      <Drawer open={open} onOpenChange={onOpenChange}>
        <DrawerContent>
          <DrawerHeader className="flex justify-between items-start">
            <div>
              <DrawerTitle>Ticket #{currentTicket.id.substring(0, 8)}</DrawerTitle>
            </div>
            <div className="flex space-x-6">
              <Select
                value={currentTicket.status}
                onValueChange={handleStatusChange}
              >
                <SelectTrigger className="w-[140px]">
                  <SelectValue placeholder="Change status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="open">Open</SelectItem>
                  <SelectItem value="in_progress">In Progress</SelectItem>
                  <SelectItem value="on_hold">On Hold</SelectItem>
                  <SelectItem value="resolved">Resolved</SelectItem>
                  <SelectItem value="closed">Closed</SelectItem>
                </SelectContent>
              </Select>
              <Button variant="outline" size="icon" onClick={() => setIsUpdateDialogOpen(true)}>
                <Edit className="size-4" />
              </Button>
              <Button className="px-[46px]" variant="outline" size="icon" onClick={() => setIsEscalateDialogOpen(true)}>
                Escalate
              </Button>
              <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
                <AlertDialogTrigger asChild>
                  <Button variant="outline" size="icon">
                    <Trash2 className="size-4 text-red-500" />
                  </Button>
                </AlertDialogTrigger>
                <AlertDialogContent>
                  <AlertDialogHeader>
                    <AlertDialogTitle>Are you sure?</AlertDialogTitle>
                    <AlertDialogDescription>
                      This action cannot be undone. This will permanently delete the ticket
                      and all associated data.
                    </AlertDialogDescription>
                  </AlertDialogHeader>
                  <AlertDialogFooter>
                    <AlertDialogCancel>Cancel</AlertDialogCancel>
                    <AlertDialogAction onClick={handleDeleteTicket} className="bg-red-500 hover:bg-red-600">
                      Delete
                    </AlertDialogAction>
                  </AlertDialogFooter>
                </AlertDialogContent>
              </AlertDialog>
            </div>
          </DrawerHeader>

          <div className="p-4">
            <Tabs defaultValue="details" onValueChange={setActiveTab}>
              <TabsList className="grid w-full grid-cols-3">
                <TabsTrigger value="details">Details</TabsTrigger>
                <TabsTrigger value="history">History</TabsTrigger>
                <TabsTrigger value="comments">Comments</TabsTrigger>
              </TabsList>

              <TabsContent value="details" className="space-y-4">
                <div className="grid gap-4 py-4">
                  <div className="flex items-center gap-2">
                    <PriorityBadge priority={currentTicket.priority as any} />
                    <StatusBadge status={currentTicket.status as any} />
                  </div>

                  <div>
                    <h4 className="text-sm font-medium mb-1">Description</h4>
                    <p className="text-sm text-muted-foreground">{currentTicket.description}</p>
                  </div>

                  <div>
                    <h4 className="text-sm font-medium mb-1">Category</h4>
                    <p className="text-sm text-muted-foreground">{categoryMap.get(currentTicket.category_id) || <span>Unknown</span>}</p>
                  </div>

                  <div>
                    <h4 className="text-sm font-medium mb-1">Due Date</h4>
                    <p className="text-sm text-muted-foreground">
                      {currentTicket.due_date
                        ? new Date(currentTicket.due_date).toLocaleDateString()
                        : 'Not set'}
                    </p>
                  </div>

                  <div>
                    <h4 className="text-sm font-medium mb-1">Customer</h4>
                    <div className="flex items-center gap-2">
                      <Avatar className="h-8 w-8">
                        <AvatarImage src={`https://api.dicebear.com/7.x/initials/svg?seed=${currentTicket.contact.first_name} ${currentTicket.contact.last_name}`} />
                        <AvatarFallback>{`${currentTicket.contact.first_name[0]}${currentTicket.contact.last_name[0]}`}</AvatarFallback>
                      </Avatar>
                      <div>
                        <div className="font-medium">{`${currentTicket.contact.first_name} ${currentTicket.contact.last_name}`}</div>
                        <div className="text-sm text-muted-foreground">{currentTicket.contact.email}</div>
                        {currentTicket.contact.phone && (
                          <div className="text-sm text-muted-foreground">{currentTicket.contact.phone}</div>
                        )}
                      </div>
                    </div>
                  </div>

                  {currentTicket.assignedUser && (
                    <div>
                      <h4 className="text-sm font-medium mb-1">Assigned To</h4>
                      <div className="flex items-center gap-2">
                        <Avatar className="h-8 w-8">
                          <AvatarImage src={`https://api.dicebear.com/7.x/initials/svg?seed=${currentTicket.assignedUser.first_name} ${currentTicket.assignedUser.last_name}`} />
                          <AvatarFallback>{`${currentTicket.assignedUser.first_name[0]}${currentTicket.assignedUser.last_name[0]}`}</AvatarFallback>
                        </Avatar>
                        <div>
                          <div className="font-medium">{`${currentTicket.assignedUser.first_name} ${currentTicket.assignedUser.last_name}`}</div>
                        </div>
                      </div>
                    </div>
                  )}

                  {currentTicket.escalation_details && (
                    <div className="border border-amber-200 bg-amber-50 p-3 rounded-md">
                      <h4 className="text-sm font-medium mb-1 flex items-center text-amber-800">
                        <AlertTriangle className="size-4 mr-1" />
                        Escalation Details
                      </h4>
                      <div className="space-y-2">
                        <div className="flex justify-between">
                          <span className="text-sm text-amber-800">Level:</span>
                          <span className="text-sm font-medium text-amber-900">{currentTicket.escalation_details.level}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-sm text-amber-800">Date:</span>
                          <span className="text-sm font-medium text-amber-900">
                            {new Date(currentTicket.escalation_details.at).toLocaleString()}
                          </span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-sm text-amber-800">Escalated by:</span>
                          <span className="text-sm font-medium text-amber-900">
                            {`${currentTicket.escalation_details.by.first_name} ${currentTicket.escalation_details.by.last_name}`}
                          </span>
                        </div>
                        <div>
                          <span className="text-sm text-amber-800">Reason:</span>
                          <p className="text-sm font-medium text-amber-900 mt-1">
                            {currentTicket.escalation_details.reason}
                          </p>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </TabsContent>

              <TabsContent value="history">
                <div className="py-4">
                  <div className="flex justify-between items-center mb-4">
                    <h3 className="text-lg font-medium">Ticket History</h3>
                    <Button variant="outline" size="sm" onClick={fetchTicketData}>
                      <RefreshCw className="size-4 mr-2" />
                      Refresh
                    </Button>
                  </div>
                  <div className="space-y-4">
                    <div className="border rounded-md p-4">
                      <div className="flex items-center gap-2 mb-2">
                        <span className="text-sm font-medium">Created</span>
                        <span className="text-xs text-muted-foreground">
                          {new Date(currentTicket.created_at).toLocaleString()}
                        </span>
                      </div>
                    </div>
                    <div className="border rounded-md p-4">
                      <div className="flex items-center gap-2 mb-2">
                        <span className="text-sm font-medium">Last Updated</span>
                        <span className="text-xs text-muted-foreground">
                          {new Date(currentTicket.updated_at).toLocaleString()}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </TabsContent>

              <TabsContent value="comments">
                <div className="py-4">
                  <div className="flex justify-between items-center mb-4">
                    <h3 className="text-lg font-medium">Comments</h3>
                    <Button variant="outline" size="sm" onClick={fetchComments}>
                      <RefreshCw className="size-4 mr-2" />
                      Refresh
                    </Button>
                  </div>

                  <div className="space-y-4">
                    <TicketCommentForm
                      ticketId={currentTicket.id}
                      onCommentAdded={fetchComments}
                    />

                    <Separator className="my-4" />

                    {isLoadingComments ? (
                      <div className="text-center py-4">
                        <p className="text-sm text-muted-foreground">Loading comments...</p>
                      </div>
                    ) : comments.length > 0 ? (
                      <div className="space-y-4">
                        {comments.map((comment) => (
                          <TicketCommentItem key={comment.id} comment={comment} />
                        ))}
                      </div>
                    ) : (
                      <div className="text-center py-4">
                        <p className="text-sm text-muted-foreground">No comments yet</p>
                      </div>
                    )}
                  </div>
                </div>
              </TabsContent>
            </Tabs>
          </div>
        </DrawerContent>
      </Drawer>

      {/* Edit Ticket Dialog */}
      <UpdateTicketDialog
        ticket={ticket}
        open={isUpdateDialogOpen}
        onOpenChange={setIsUpdateDialogOpen}
        onTicketUpdated={() => refetchTicket()}
      />
      <EscalateTicketDialog
        ticketId={ticket.id}
        open={isEscalateDialogOpen}
        onOpenChange={setIsEscalateDialogOpen}
      />
    </>
  )
}