import { But<PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
// import { Input } from "@/components/ui/input"; // Unused import
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { useTickets } from "@/lib/hooks/useTickets";
import { EscalateTicketRequest, TicketPriority } from "@/lib/api/types";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { toast } from "sonner";
import { z } from "zod";
import { Loader2 } from "lucide-react";

const escalateTicketSchema = z.object({
  reason: z.string().min(1, "Reason is required"),
  priority: z.nativeEnum(TicketPriority),
});

type EscalateTicketFormValues = z.infer<typeof escalateTicketSchema>;

interface EscalateTicketDialogProps {
  ticketId: string;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export function EscalateTicketDialog({
  ticketId,
  open,
  onOpenChange,
}: EscalateTicketDialogProps) {
  const { escalateTicket, isEscalatingTicket } = useTickets();

  const form = useForm<EscalateTicketFormValues>({
    resolver: zodResolver(escalateTicketSchema),
    defaultValues: {
      priority: TicketPriority.URGENT,
    },
  });

  const onSubmit = (data: EscalateTicketFormValues) => {
    if (!data.reason) {
      toast.error("Please provide a reason for escalation");
      return;
    }

    escalateTicket(
      {
        id: ticketId,
        data: data as EscalateTicketRequest,
      },
      {
        onSuccess: () => {
          toast.success("Ticket escalated successfully");
          onOpenChange(false);
          form.reset();
        },
        onError: (error) => {
          console.error("Failed to escalate ticket:", error);
          toast.error(error instanceof Error ? error.message : "Failed to escalate ticket");
        }
      }
    );
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <form onSubmit={form.handleSubmit(onSubmit)}>
          <DialogHeader>
            <DialogTitle>Escalate Ticket</DialogTitle>
            <DialogDescription>
              Provide a reason for escalating this ticket and set the new priority.
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <Textarea
                {...form.register("reason")}
                placeholder="Enter reason for escalation"
                className="min-h-[100px]"
              />
              {form.formState.errors.reason && (
                <p className="text-sm text-red-500">
                  {form.formState.errors.reason.message}
                </p>
              )}
            </div>
            <div className="grid gap-2">
              <Select
                {...form.register("priority")}
                onValueChange={(value) =>
                  form.setValue("priority", value as TicketPriority)
                }
                defaultValue={form.getValues("priority")}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select priority" />
                </SelectTrigger>
                <SelectContent>
                  {Object.values(TicketPriority).map((priority) => (
                    <SelectItem key={priority} value={priority}>
                      {priority.charAt(0).toUpperCase() + priority.slice(1)}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {form.formState.errors.priority && (
                <p className="text-sm text-red-500">
                  {form.formState.errors.priority.message}
                </p>
              )}
            </div>
          </div>
          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => onOpenChange(false)}
            >
              Cancel
            </Button>
            <Button type="submit" disabled={isEscalatingTicket}>
              {isEscalatingTicket && <Loader2 className="mr-2 size-4 animate-spin" />}
              Escalate
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
