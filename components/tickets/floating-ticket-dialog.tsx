"use client";

import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { format } from "date-fns";
import { X, GripVertical, CheckCircle } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Calendar } from "@/components/ui/calendar";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { CalendarIcon } from "lucide-react";
import { useToast } from "@/components/ui/use-toast";
import { useTickets } from "@/lib/hooks/useTickets";
import { useContacts } from "@/lib/hooks/useContacts";
import { useCategories } from "@/lib/hooks/useCategories";
import { useChannels } from "@/lib/hooks/useChannels";
import { useProducts } from "@/lib/hooks/useProducts";
import { createTicketSchema, type CreateTicketFormValues } from "@/lib/validations/tickets";
import { cn } from "@/lib/utils";
import { Draggable } from "@/components/ui/draggable";

interface FloatingTicketDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  initialContactId?: string;
  initialChannelType?: string; // "phone", "email", "chat", etc.
  initialProductId?: string;
  onCreateSuccess?: () => void;
  createAnother?: boolean;
}

export function FloatingTicketDialog({
  open,
  onOpenChange,
  initialContactId,
  initialChannelType,
  initialProductId,
  onCreateSuccess,
  createAnother = false
}: FloatingTicketDialogProps) {
  const { toast } = useToast();
  const { contacts } = useContacts();
  const { createTicket, isCreatingTicket } = useTickets();
  const { data: categories = [], isLoading: isLoadingCategories } = useCategories();
  const { data: channels = [], isLoading: isLoadingChannels } = useChannels();
  const { data: products = [], isLoading: isLoadingProducts } = useProducts();
  const [isCreateAnother, setIsCreateAnother] = useState(false);

  // Find the channel ID based on the channel type
  const findChannelIdByType = (type: string): string => {
    const channel = channels.find(c => c.type.toLowerCase() === type.toLowerCase());
    return channel?.id || "";
  };

  const form = useForm<CreateTicketFormValues>({
    resolver: zodResolver(createTicketSchema),
    defaultValues: {
      description: "",
      priority: "MEDIUM",
      status: "OPEN",
      category_id: "",
      contact_id: initialContactId || "",
      channel_id: initialChannelType ? findChannelIdByType(initialChannelType) : "",
      product_id: initialProductId || "",
      due_date: new Date(),
    },
  });

  // Update form values when initial values change or dialog opens
  useEffect(() => {
    if (open) {
      if (initialContactId) {
        form.setValue('contact_id', initialContactId);
      }
      if (initialChannelType && channels.length > 0) {
        const channelId = findChannelIdByType(initialChannelType);
        if (channelId) {
          form.setValue('channel_id', channelId);
        }
      }
      if (initialProductId) {
        form.setValue('product_id', initialProductId);
      }
    }
  }, [open, initialContactId, initialChannelType, initialProductId, channels, form, findChannelIdByType]);

  // Reset form when dialog is closed
  useEffect(() => {
    if (!open) {
      // Wait a bit before resetting to avoid UI flicker
      setTimeout(() => {
        form.reset({
          description: "",
          priority: "MEDIUM",
          status: "OPEN",
          category_id: "",
          contact_id: "",
          channel_id: "",
          product_id: "",
          due_date: new Date(),
        });
      }, 300);
    }
  }, [open, form]);

  const onSubmit = async (data: CreateTicketFormValues) => {
    // Prevent duplicate submissions
    if (isCreatingTicket) {
      console.log('Submission already in progress, ignoring duplicate submission');
      return;
    }

    try {
      // Convert status and priority to lowercase to match API expectations
      const formattedData = {
        ...data,
        // Convert uppercase enum values to lowercase
        status: data.status.toLowerCase(),
        priority: data.priority.toLowerCase(),
        // Convert Date object to ISO string for the API
        due_date: data.due_date.toISOString()
      };

      console.log('Submitting ticket data:', formattedData);

      // Use the regular mutation (removed the duplicate direct API call)
      createTicket(formattedData, {
        onSuccess: (response) => {
          console.log('Ticket created successfully, response:', response);

          // Get the ticket ID from the response
          const ticketId = typeof response === 'object' && response !== null
            ? (response.id || (response as any).data?.id || 'Unknown')
            : 'Unknown';

          // Show success toast notification
          toast({
            title: "Ticket Created",
            description: `Ticket #${ticketId} has been created successfully.`,
            variant: "default",
            duration: 5000,
          });

          // Use setTimeout to ensure the UI updates before closing
          setTimeout(() => {
            // If isCreateAnother is true, just reset the form but keep the dialog open
            if (isCreateAnother) {
              // Keep contact and product information for the next ticket
              const contactId = form.getValues('contact_id');
              const productId = form.getValues('product_id');
              const channelId = form.getValues('channel_id');

              // Reset the form with a slight delay to avoid UI flicker
              setTimeout(() => {
                form.reset({
                  description: "",
                  priority: "MEDIUM",
                  status: "OPEN",
                  category_id: "",
                  contact_id: contactId,
                  channel_id: channelId,
                  product_id: productId,
                  due_date: new Date(),
                });
              }, 100);

              // Call the success callback if provided
              if (onCreateSuccess) {
                onCreateSuccess();
              }
            } else {
              // Close the dialog after successful ticket creation
              onOpenChange(false);

              // Reset the form with a slight delay to avoid UI flicker
              setTimeout(() => {
                form.reset({
                  description: "",
                  priority: "MEDIUM",
                  status: "OPEN",
                  category_id: "",
                  contact_id: initialContactId || "",
                  channel_id: initialChannelType ? findChannelIdByType(initialChannelType) : "",
                  product_id: initialProductId || "",
                  due_date: new Date(),
                });
              }, 100);

              // Call the success callback if provided
              if (onCreateSuccess) {
                onCreateSuccess();
              }
            }
          }, 500);
        },
        onError: (error) => {
          console.error('Error creating ticket:', error);

          // Show error toast notification
          toast({
            title: "Error",
            description: "Failed to create ticket. Please try again.",
            variant: "destructive",
            duration: 5000,
          });
        }
      });
    } catch (error) {
      console.error('Failed to create ticket:', error);

      // Show error toast notification
      toast({
        title: "Error",
        description: "Failed to create ticket. Please try again.",
        variant: "destructive",
        duration: 5000,
      });
    }
  };

  if (!open) return null;

  return (
    <Draggable initialPosition={{ x: typeof window !== 'undefined' ? window.innerWidth - 550 : 50, y: 20 }}>
      <div className="bg-card border rounded-lg shadow-lg w-[500px] max-h-[90vh] overflow-auto">
        <div className="flex items-center justify-between p-4 border-b drag-handle cursor-grab">
          <div className="flex items-center gap-2">
            <GripVertical className="h-5 w-5 text-muted-foreground" />
            <h2 className="text-lg font-semibold">Create Ticket</h2>
          </div>
          <Button
            variant="ghost"
            size="icon"
            onClick={() => onOpenChange(false)}
            className="h-8 w-8"
          >
            <X className="size-4" />
          </Button>
        </div>

        {/* Add a style tag to ensure select dropdowns appear above the modal */}
        <style jsx global>{`
          .select-content {
            z-index: 300 !important;
          }
          .popover-content {
            z-index: 300 !important;
          }
          [data-radix-popper-content-wrapper] {
            z-index: 300 !important;
          }
        `}</style>

        <div className="p-4">
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
              <FormField
                control={form.control}
                name="description"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Description</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Enter ticket description"
                        className="min-h-[100px]"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="grid grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="priority"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Priority</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select priority" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="LOW">Low</SelectItem>
                          <SelectItem value="MEDIUM">Medium</SelectItem>
                          <SelectItem value="HIGH">High</SelectItem>
                          <SelectItem value="URGENT">Urgent</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="category_id"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Category</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select category" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {isLoadingCategories ? (
                            <SelectItem value="" disabled>Loading categories...</SelectItem>
                          ) : categories.length > 0 ? (
                            categories.map((category) => (
                              <SelectItem key={category.id} value={category.id}>
                                {category.name}
                              </SelectItem>
                            ))
                          ) : (
                            <SelectItem value="" disabled>No categories found</SelectItem>
                          )}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="contact_id"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Contact</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select contact" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {contacts.map((contact) => (
                            <SelectItem key={contact.id} value={contact.id}>
                              {contact.first_name} {contact.last_name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="channel_id"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Channel</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select channel" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {channels.map((channel) => (
                            <SelectItem key={channel.id} value={channel.id}>
                              {channel.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="product_id"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Product</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select product" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {products.map((product) => (
                            <SelectItem key={product.id} value={product.id}>
                              {product.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={form.control}
                name="due_date"
                render={({ field }) => (
                  <FormItem className="flex flex-col">
                    <FormLabel>Due Date</FormLabel>
                    <Popover>
                      <PopoverTrigger asChild>
                        <FormControl>
                          <Button
                            variant={"outline"}
                            className={cn(
                              "w-full pl-3 text-left font-normal",
                              !field.value && "text-muted-foreground"
                            )}
                          >
                            {field.value ? (
                              format(field.value, "PPP")
                            ) : (
                              <span>Pick a date</span>
                            )}
                            <CalendarIcon className="ml-auto size-4 opacity-50" />
                          </Button>
                        </FormControl>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0" align="start">
                        <Calendar
                          mode="single"
                          selected={field.value}
                          onSelect={field.onChange}
                          disabled={(date) =>
                            date < new Date()
                          }
                          initialFocus
                        />
                      </PopoverContent>
                    </Popover>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="flex justify-end gap-4 pt-4">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => onOpenChange(false)}
                >
                  Cancel
                </Button>
                {createAnother && (
                  <Button
                    type="submit"
                    variant="secondary"
                    disabled={isCreatingTicket}
                    onClick={() => setIsCreateAnother(true)}
                  >
                    {isCreatingTicket ? 'Creating...' : 'Create & Add Another'}
                  </Button>
                )}
                <Button
                  type="submit"
                  disabled={isCreatingTicket}
                  onClick={() => setIsCreateAnother(false)}
                >
                  {isCreatingTicket ? 'Creating...' : 'Create Ticket'}
                </Button>
              </div>
            </form>
          </Form>
        </div>
      </div>
    </Draggable>
  );
}
