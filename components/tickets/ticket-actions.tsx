import { Button } from "@/components/ui/button"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { MoreHorizontal, Eye, UserPlus, AlertTriangle } from "lucide-react"
import type { Ticket } from "@/lib/api/types"

interface TicketActionsProps {
  ticket: Ticket
  onView: () => void
  onAssign: () => void
  onEscalate: () => void
}

export function TicketActions({ ticket, onView, onAssign, onEscalate }: TicketActionsProps) {
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" className="h-8 w-8 p-0">
          <span className="sr-only">Open menu</span>
          <MoreHorizontal className="size-4" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuLabel>Actions</DropdownMenuLabel>
        <DropdownMenuItem onClick={onView}>
          <Eye className="mr-2 size-4" />
          View details
        </DropdownMenuItem>
        <DropdownMenuItem onClick={onAssign}>
          <UserPlus className="mr-2 size-4" />
          Assign ticket
        </DropdownMenuItem>
        <DropdownMenuSeparator />
        <DropdownMenuItem onClick={onEscalate}>
          <AlertTriangle className="mr-2 size-4" />
          Escalate ticket
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  )
}