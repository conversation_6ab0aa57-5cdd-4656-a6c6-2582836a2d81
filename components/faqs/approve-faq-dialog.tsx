"use client"

import { useState } from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { z } from "zod"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { Textarea } from "@/components/ui/textarea"
import { FAQ, FAQStatus } from "@/lib/api/types"

const approveSchema = z.object({
  notes: z.string().optional(),
})

const rejectSchema = z.object({
  reason: z.string().min(1, "Rejection reason is required"),
})

type ApproveFormValues = z.infer<typeof approveSchema>
type RejectFormValues = z.infer<typeof rejectSchema>

interface ApproveFAQDialogProps {
  faq: FAQ
  open: boolean
  onOpenChange: (open: boolean) => void
  onApprove: (faqId: string, notes?: string) => void
  onReject: (faqId: string, reason: string) => void
}

export function ApproveFAQDialog({
  faq,
  open,
  onOpenChange,
  onApprove,
  onReject,
}: ApproveFAQDialogProps) {
  const [mode, setMode] = useState<"approve" | "reject">("approve")

  const approveForm = useForm<ApproveFormValues>({
    resolver: zodResolver(approveSchema),
    defaultValues: {
      notes: "",
    },
  })

  const rejectForm = useForm<RejectFormValues>({
    resolver: zodResolver(rejectSchema),
    defaultValues: {
      reason: "",
    },
  })

  const handleApprove = (data: ApproveFormValues) => {
    onApprove(faq.id, data.notes)
    onOpenChange(false)
  }

  const handleReject = (data: RejectFormValues) => {
    onReject(faq.id, data.reason)
    onOpenChange(false)
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>
            {mode === "approve" ? "Approve FAQ" : "Reject FAQ"}
          </DialogTitle>
          <DialogDescription>
            {mode === "approve"
              ? "Approve this FAQ to make it visible to all users."
              : "Provide a reason for rejecting this FAQ."}
          </DialogDescription>
        </DialogHeader>

        <div className="flex space-x-2 mb-4">
          <Button
            variant={mode === "approve" ? "default" : "outline"}
            onClick={() => setMode("approve")}
            className="flex-1"
          >
            Approve
          </Button>
          <Button
            variant={mode === "reject" ? "default" : "outline"}
            onClick={() => setMode("reject")}
            className="flex-1"
          >
            Reject
          </Button>
        </div>

        {mode === "approve" ? (
          <Form {...approveForm}>
            <form onSubmit={approveForm.handleSubmit(handleApprove)} className="space-y-4">
              <FormField
                control={approveForm.control}
                name="notes"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Notes (Optional)</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Add any notes about this approval"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <DialogFooter>
                <Button type="button" variant="outline" onClick={() => onOpenChange(false)}>
                  Cancel
                </Button>
                <Button type="submit">Approve FAQ</Button>
              </DialogFooter>
            </form>
          </Form>
        ) : (
          <Form {...rejectForm}>
            <form onSubmit={rejectForm.handleSubmit(handleReject)} className="space-y-4">
              <FormField
                control={rejectForm.control}
                name="reason"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Rejection Reason</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Explain why this FAQ is being rejected"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <DialogFooter>
                <Button type="button" variant="outline" onClick={() => onOpenChange(false)}>
                  Cancel
                </Button>
                <Button type="submit" variant="destructive">Reject FAQ</Button>
              </DialogFooter>
            </form>
          </Form>
        )}
      </DialogContent>
    </Dialog>
  )
}
