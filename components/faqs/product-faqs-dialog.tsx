"use client";

import { useState, useEffect } from "react";
import { X, Search, ChevronDown, ChevronUp } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { useFAQs } from "@/lib/hooks/useFAQs";
import { useProducts } from "@/lib/hooks/useProducts";
import { FAQ } from "@/lib/api/types";
import { Draggable } from "@/components/ui/draggable";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";

interface ProductFAQsDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  productId?: string;
  productName?: string;
}

export function ProductFAQsDialog({
  open,
  onOpenChange,
  productId,
  productName,
}: ProductFAQsDialogProps) {
  const { faqs, isLoading } = useFAQs();
  const { data: products = [] } = useProducts();
  const [searchTerm, setSearchTerm] = useState("");
  const [expandedFaqs, setExpandedFaqs] = useState<Record<string, boolean>>({});
  const [minimized, setMinimized] = useState(false);

  // Filter FAQs by product and search term
  const filteredFAQs = faqs
    .filter((faq) => {
      // Only show published FAQs
      if (!faq.is_published) return false;

      // Filter by product if provided
      if (productId && faq.product_id !== productId) return false;

      // Filter by search term
      if (
        searchTerm &&
        !faq.question.toLowerCase().includes(searchTerm.toLowerCase()) &&
        !faq.answer.toLowerCase().includes(searchTerm.toLowerCase())
      ) {
        return false;
      }

      return true;
    })
    .sort((a, b) => {
      // Sort by question alphabetically
      return a.question.localeCompare(b.question);
    });

  // Toggle FAQ expansion
  const toggleFaq = (faqId: string) => {
    setExpandedFaqs((prev) => ({
      ...prev,
      [faqId]: !prev[faqId],
    }));
  };

  // Get product name from ID if not provided
  const getProductName = () => {
    if (productName) return productName;
    if (productId) {
      const product = products.find((p) => p.id === productId);
      return product?.name || "Product";
    }
    return "All Products";
  };

  return (
    <Draggable initialPosition={{ x: 100, y: 100 }}>
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="sm:max-w-lg w-full max-w-[95vw] p-0 overflow-hidden">
          <DialogHeader className="px-6 pt-6 pb-2">
            <DialogTitle className="text-center text-lg font-bold">
              FAQs - {getProductName()}
            </DialogTitle>
            <Button
              variant="ghost"
              size="icon"
              className="absolute top-2 right-2 z-10"
              onClick={() => onOpenChange(false)}
              aria-label="Close"
            >
              <X className="h-5 w-5" />
            </Button>
            <Button
              variant="ghost"
              size="icon"
              className="absolute top-2 right-10 z-10"
              onClick={() => setMinimized((m) => !m)}
              aria-label="Minimize"
            >
              {minimized ? (
                <ChevronUp className="h-5 w-5" />
              ) : (
                <ChevronDown className="h-5 w-5" />
              )}
            </Button>
          </DialogHeader>
          {!minimized && (
            <div className="space-y-4 px-6 pb-6">
              {/* Search input */}
              <div className="relative">
                <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search FAQs..."
                  className="pl-8"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
              {/* FAQ list */}
              {isLoading ? (
                <div className="flex items-center justify-center h-40">
                  <p className="text-muted-foreground">Loading FAQs...</p>
                </div>
              ) : filteredFAQs.length === 0 ? (
                <div className="flex items-center justify-center h-40">
                  <p className="text-muted-foreground">
                    {searchTerm
                      ? "No FAQs match your search"
                      : `No FAQs available for ${getProductName()}`}
                  </p>
                </div>
              ) : (
                <ScrollArea className="h-[400px] pr-2">
                  <Accordion type="single" collapsible className="w-full">
                    {filteredFAQs.map((faq) => (
                      <AccordionItem
                        key={faq.id}
                        value={faq.id}
                        className="border rounded-lg mb-2 bg-background"
                      >
                        <AccordionTrigger className="px-4 py-3 text-left text-base font-medium flex items-center justify-between">
                          <span>{faq.question}</span>
                          {faq.category?.name && (
                            <Badge
                              variant="outline"
                              className="ml-2 text-xs whitespace-nowrap"
                            >
                              {faq.category.name}
                            </Badge>
                          )}
                        </AccordionTrigger>
                        <AccordionContent className="px-4 pb-4 pt-2 bg-muted rounded-b-lg">
                          <div
                            className="prose max-w-none text-sm mb-2"
                            dangerouslySetInnerHTML={{ __html: faq.answer }}
                          />
                          {faq.product && (
                            <div className="text-xs text-muted-foreground mt-2">
                              Product: {faq.product.name}
                            </div>
                          )}
                        </AccordionContent>
                      </AccordionItem>
                    ))}
                  </Accordion>
                </ScrollArea>
              )}
              <div className="flex justify-end pt-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => onOpenChange(false)}
                >
                  Close
                </Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </Draggable>
  );
}
