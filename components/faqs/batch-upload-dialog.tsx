"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { FileUpload } from "@/components/ui/file-upload"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { parseFAQExcel, generateFAQTemplate } from "@/lib/utils/excel-parser"
import { CreateFAQRequest, Category, Product } from "@/lib/api/types"
import { Loader2, AlertCircle, CheckCircle2, Download } from "lucide-react"
import { Progress } from "@/components/ui/progress"
import { useCategories } from "@/lib/hooks/useCategories"
import { useProducts } from "@/lib/hooks/useProducts"

interface BatchUploadDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onUpload: (data: CreateFAQRequest[]) => void
  isUploading: boolean
}

export function BatchUploadDialog({
  open,
  onOpenChange,
  onUpload,
  isUploading
}: BatchUploadDialogProps) {
  const [file, setFile] = useState<File | null>(null)
  const [parseResult, setParseResult] = useState<{
    data: CreateFAQRequest[]
    errors: Record<string, string>
    totalRows: number
    validRows: number
  } | null>(null)
  const [isParsing, setIsParsing] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [isGeneratingTemplate, setIsGeneratingTemplate] = useState(false)

  // Fetch categories and products for the template
  const { data: categories = [] } = useCategories()
  const { data: products = [] } = useProducts()

  const handleFileSelect = async (selectedFile: File) => {
    setFile(selectedFile)
    setError(null)
    setParseResult(null)

    try {
      setIsParsing(true)
      const result = await parseFAQExcel(selectedFile)
      setParseResult(result)

      if (result.validRows === 0) {
        setError("No valid FAQs found in the file. Please check the template format.")
      }
    } catch (err: any) {
      setError(err.message || "Failed to parse file")
    } finally {
      setIsParsing(false)
    }
  }

  const handleDownloadTemplate = () => {
    setIsGeneratingTemplate(true)

    try {
      // Generate template with categories and products
      const templateBlob = generateFAQTemplate(categories, products)
      const url = URL.createObjectURL(templateBlob)

      const a = document.createElement('a')
      a.href = url
      a.download = 'faqs_template.xlsx'
      document.body.appendChild(a)
      a.click()

      // Clean up
      document.body.removeChild(a)
      URL.revokeObjectURL(url)
    } catch (err) {
      console.error('Error generating template:', err)
      setError('Failed to generate template')
    } finally {
      setIsGeneratingTemplate(false)
    }
  }

  const handleUpload = () => {
    if (parseResult && parseResult.data.length > 0) {
      onUpload(parseResult.data)
    }
  }

  const hasErrors = parseResult && Object.keys(parseResult.errors).length > 0
  const validPercentage = parseResult
    ? Math.round((parseResult.validRows / parseResult.totalRows) * 100)
    : 0

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Batch Upload FAQs</DialogTitle>
          <DialogDescription>
            Upload an Excel file with multiple FAQs to create them in batch.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4 py-4">
          <div className="flex justify-end">
            <Button
              variant="outline"
              size="sm"
              onClick={handleDownloadTemplate}
              className="text-xs"
              disabled={isGeneratingTemplate}
            >
              {isGeneratingTemplate ? (
                <>
                  <Loader2 className="h-3 w-3 mr-1 animate-spin" />
                  Generating...
                </>
              ) : (
                <>
                  <Download className="h-3 w-3 mr-1" />
                  Download Template
                </>
              )}
            </Button>
          </div>

          <FileUpload
            onFileSelect={handleFileSelect}
            acceptedFileTypes=".xlsx,.xls"
            maxSize={5 * 1024 * 1024} // 5MB
          />

          {isParsing && (
            <div className="text-center py-2">
              <Loader2 className="h-5 w-5 animate-spin mx-auto mb-2" />
              <p className="text-sm text-muted-foreground">Parsing file...</p>
            </div>
          )}

          {error && (
            <Alert variant="destructive">
              <AlertCircle className="size-4" />
              <AlertTitle>Error</AlertTitle>
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {parseResult && !isParsing && (
            <div className="space-y-3">
              <div className="space-y-1">
                <div className="flex justify-between text-sm">
                  <span>Valid FAQs: {parseResult.validRows} of {parseResult.totalRows}</span>
                  <span>{validPercentage}%</span>
                </div>
                <Progress value={validPercentage} className="h-2" />
              </div>

              {hasErrors && (
                <Alert variant="destructive" className="text-xs">
                  <AlertCircle className="size-4" />
                  <AlertTitle>Validation Errors</AlertTitle>
                  <AlertDescription className="max-h-32 overflow-y-auto">
                    <ul className="list-disc pl-4 space-y-1 mt-2">
                      {Object.entries(parseResult.errors).map(([row, error]) => (
                        <li key={row}>
                          <strong>{row}:</strong> {error}
                        </li>
                      ))}
                    </ul>
                  </AlertDescription>
                </Alert>
              )}

              {parseResult.validRows > 0 && (
                <Alert variant="default" className="bg-primary/10 border-primary/20">
                  <CheckCircle2 className="size-4 text-primary" />
                  <AlertTitle>Ready to Upload</AlertTitle>
                  <AlertDescription>
                    {parseResult.validRows} FAQs will be created.
                  </AlertDescription>
                </Alert>
              )}
            </div>
          )}
        </div>

        <DialogFooter className="flex space-x-2 justify-end">
          <Button
            variant="outline"
            onClick={() => onOpenChange(false)}
            disabled={isUploading}
          >
            Cancel
          </Button>
          <Button
            type="submit"
            onClick={handleUpload}
            disabled={
              !parseResult ||
              parseResult.validRows === 0 ||
              isUploading
            }
          >
            {isUploading ? (
              <>
                <Loader2 className="mr-2 size-4 animate-spin" />
                Uploading...
              </>
            ) : (
              'Upload FAQs'
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
