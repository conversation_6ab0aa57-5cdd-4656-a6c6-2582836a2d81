"use client";

import React, { useState, useEffect, useRef } from 'react';
import { motion, PanInfo } from 'framer-motion';
import { 
  Search, 
  X, 
  Bookmark, 
  BookmarkCheck, 
  ChevronDown, 
  ChevronUp, 
  Copy, 
  ExternalLink,
  Maximize2,
  Minimize2,
  GripH<PERSON>zontal
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from '@/components/ui/collapsible';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { FAQModalData, DraggableModalPosition, DraggableModalConstraints } from '@/lib/types/dashboard';

interface DraggableFAQModalProps {
  isOpen: boolean;
  onClose: () => void;
  productId?: string;
  initialPosition?: DraggableModalPosition;
  onPositionChange?: (position: DraggableModalPosition) => void;
}

// Mock FAQ data - replace with actual API call
const mockFAQs: FAQModalData[] = [
  {
    id: '1',
    question: 'How do I troubleshoot internet connectivity issues?',
    answer: 'First, check if all cables are properly connected. Then restart your modem and router by unplugging them for 30 seconds. If the issue persists, check for service outages in your area.',
    category: 'Technical Support',
    productId: 'prod-fiber',
    tags: ['connectivity', 'troubleshooting', 'internet'],
    isBookmarked: false,
  },
  {
    id: '2',
    question: 'What are the available internet speed packages?',
    answer: 'We offer several packages: Basic (10 Mbps) - $29.99/month, Standard (50 Mbps) - $49.99/month, Premium (100 Mbps) - $79.99/month, and Ultra (500 Mbps) - $129.99/month.',
    category: 'Billing & Plans',
    productId: 'prod-fiber',
    tags: ['packages', 'pricing', 'speed'],
    isBookmarked: true,
  },
  {
    id: '3',
    question: 'How can I change my billing address?',
    answer: 'You can update your billing address by logging into your account portal, navigating to Account Settings > Billing Information, and updating your address details. Changes take effect on your next billing cycle.',
    category: 'Account Management',
    productId: 'prod-fiber',
    tags: ['billing', 'address', 'account'],
    isBookmarked: false,
  },
  {
    id: '4',
    question: 'What should I do if my service is down?',
    answer: 'Check our service status page first. If there are no reported outages, restart your equipment. If the problem persists, contact our technical support team at 1-800-SUPPORT.',
    category: 'Technical Support',
    productId: 'prod-fiber',
    tags: ['outage', 'service', 'support'],
    isBookmarked: false,
  },
];

export function DraggableFAQModal({ 
  isOpen, 
  onClose, 
  productId,
  initialPosition = { x: 100, y: 100 },
  onPositionChange 
}: DraggableFAQModalProps) {
  const [position, setPosition] = useState<DraggableModalPosition>(initialPosition);
  const [isMaximized, setIsMaximized] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [bookmarkedFAQs, setBookmarkedFAQs] = useState<Set<string>>(new Set());
  const [expandedFAQs, setExpandedFAQs] = useState<Set<string>>(new Set());
  
  const modalRef = useRef<HTMLDivElement>(null);
  const dragConstraints = useRef<DraggableModalConstraints>({
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  });

  // Update drag constraints based on viewport
  useEffect(() => {
    const updateConstraints = () => {
      const modalWidth = isMaximized ? window.innerWidth - 40 : 400;
      const modalHeight = isMaximized ? window.innerHeight - 40 : 600;
      
      dragConstraints.current = {
        top: 20,
        left: 20,
        right: window.innerWidth - modalWidth - 20,
        bottom: window.innerHeight - modalHeight - 20,
      };
    };

    updateConstraints();
    window.addEventListener('resize', updateConstraints);
    return () => window.removeEventListener('resize', updateConstraints);
  }, [isMaximized]);

  // Handle drag end
  const handleDragEnd = (event: MouseEvent | TouchEvent | PointerEvent, info: PanInfo) => {
    const newPosition = {
      x: Math.max(dragConstraints.current.left, Math.min(position.x + info.offset.x, dragConstraints.current.right)),
      y: Math.max(dragConstraints.current.top, Math.min(position.y + info.offset.y, dragConstraints.current.bottom)),
    };
    
    setPosition(newPosition);
    onPositionChange?.(newPosition);
  };

  // Filter FAQs based on search and category
  const filteredFAQs = mockFAQs.filter(faq => {
    const matchesSearch = faq.question.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         faq.answer.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         faq.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()));
    
    const matchesCategory = selectedCategory === 'all' || faq.category === selectedCategory;
    const matchesProduct = !productId || faq.productId === productId;
    
    return matchesSearch && matchesCategory && matchesProduct;
  });

  // Get unique categories
  const categories = ['all', ...Array.from(new Set(mockFAQs.map(faq => faq.category)))];

  // Toggle bookmark
  const toggleBookmark = (faqId: string) => {
    setBookmarkedFAQs(prev => {
      const newSet = new Set(prev);
      if (newSet.has(faqId)) {
        newSet.delete(faqId);
      } else {
        newSet.add(faqId);
      }
      return newSet;
    });
  };

  // Toggle FAQ expansion
  const toggleExpansion = (faqId: string) => {
    setExpandedFAQs(prev => {
      const newSet = new Set(prev);
      if (newSet.has(faqId)) {
        newSet.delete(faqId);
      } else {
        newSet.add(faqId);
      }
      return newSet;
    });
  };

  // Copy FAQ content
  const copyFAQContent = (faq: FAQModalData) => {
    const content = `Q: ${faq.question}\nA: ${faq.answer}`;
    navigator.clipboard.writeText(content);
    // You could show a toast notification here
  };

  if (!isOpen) return null;

  return (
    <motion.div
      ref={modalRef}
      drag
      dragMomentum={false}
      dragConstraints={dragConstraints.current}
      onDragEnd={handleDragEnd}
      initial={{ 
        x: position.x, 
        y: position.y,
        scale: 0.9,
        opacity: 0 
      }}
      animate={{ 
        x: position.x, 
        y: position.y,
        scale: 1,
        opacity: 1 
      }}
      exit={{ 
        scale: 0.9,
        opacity: 0 
      }}
      className={`fixed z-50 bg-background border rounded-lg shadow-2xl ${
        isMaximized 
          ? 'w-[calc(100vw-40px)] h-[calc(100vh-40px)]' 
          : 'w-[400px] h-[600px]'
      }`}
      style={{
        left: position.x,
        top: position.y,
      }}
    >
      {/* Header with drag handle */}
      <div className="flex items-center justify-between p-4 border-b cursor-move">
        <div className="flex items-center gap-2">
          <GripHorizontal className="h-4 w-4 text-muted-foreground" />
          <h3 className="font-semibold">Product FAQs</h3>
          {productId && (
            <Badge variant="outline" className="text-xs">
              {productId.replace('prod-', '')}
            </Badge>
          )}
        </div>
        
        <div className="flex items-center gap-1">
          <Button
            variant="ghost"
            size="icon"
            onClick={() => setIsMaximized(!isMaximized)}
            className="h-8 w-8"
          >
            {isMaximized ? (
              <Minimize2 className="h-4 w-4" />
            ) : (
              <Maximize2 className="h-4 w-4" />
            )}
          </Button>
          <Button
            variant="ghost"
            size="icon"
            onClick={onClose}
            className="h-8 w-8"
          >
            <X className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {/* Search and filters */}
      <div className="p-4 space-y-3 border-b">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search FAQs..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10"
          />
        </div>
        
        <div className="flex gap-2 flex-wrap">
          {categories.map(category => (
            <Button
              key={category}
              variant={selectedCategory === category ? "default" : "outline"}
              size="sm"
              onClick={() => setSelectedCategory(category)}
              className="text-xs"
            >
              {category === 'all' ? 'All' : category}
            </Button>
          ))}
        </div>
      </div>

      {/* FAQ List */}
      <ScrollArea className={`${isMaximized ? 'h-[calc(100vh-200px)]' : 'h-[400px]'} p-4`}>
        <div className="space-y-3">
          {filteredFAQs.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              <Search className="h-8 w-8 mx-auto mb-2 opacity-50" />
              <p>No FAQs found matching your criteria</p>
            </div>
          ) : (
            filteredFAQs.map((faq) => (
              <Card key={faq.id} className="transition-all hover:shadow-md">
                <Collapsible
                  open={expandedFAQs.has(faq.id)}
                  onOpenChange={() => toggleExpansion(faq.id)}
                >
                  <CollapsibleTrigger asChild>
                    <CardHeader className="cursor-pointer hover:bg-muted/50 transition-colors">
                      <div className="flex items-start justify-between gap-2">
                        <CardTitle className="text-sm font-medium text-left">
                          {faq.question}
                        </CardTitle>
                        <div className="flex items-center gap-1 shrink-0">
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={(e) => {
                              e.stopPropagation();
                              toggleBookmark(faq.id);
                            }}
                            className="h-6 w-6"
                          >
                            {bookmarkedFAQs.has(faq.id) ? (
                              <BookmarkCheck className="h-3 w-3 text-primary" />
                            ) : (
                              <Bookmark className="h-3 w-3" />
                            )}
                          </Button>
                          {expandedFAQs.has(faq.id) ? (
                            <ChevronUp className="h-4 w-4" />
                          ) : (
                            <ChevronDown className="h-4 w-4" />
                          )}
                        </div>
                      </div>
                      
                      <div className="flex gap-1 flex-wrap">
                        <Badge variant="secondary" className="text-xs">
                          {faq.category}
                        </Badge>
                        {faq.tags.slice(0, 2).map(tag => (
                          <Badge key={tag} variant="outline" className="text-xs">
                            {tag}
                          </Badge>
                        ))}
                      </div>
                    </CardHeader>
                  </CollapsibleTrigger>
                  
                  <CollapsibleContent>
                    <CardContent className="pt-0">
                      <Separator className="mb-3" />
                      <p className="text-sm text-muted-foreground leading-relaxed mb-3">
                        {faq.answer}
                      </p>
                      
                      <div className="flex items-center gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => copyFAQContent(faq)}
                          className="text-xs"
                        >
                          <Copy className="h-3 w-3 mr-1" />
                          Copy
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          className="text-xs"
                        >
                          <ExternalLink className="h-3 w-3 mr-1" />
                          Details
                        </Button>
                      </div>
                    </CardContent>
                  </CollapsibleContent>
                </Collapsible>
              </Card>
            ))
          )}
        </div>
      </ScrollArea>

      {/* Footer */}
      <div className="p-4 border-t bg-muted/30">
        <div className="flex items-center justify-between text-xs text-muted-foreground">
          <span>{filteredFAQs.length} FAQ(s) found</span>
          <span>{bookmarkedFAQs.size} bookmarked</span>
        </div>
      </div>
    </motion.div>
  );
}

// Hook to manage FAQ modal state
export function useDraggableFAQModal() {
  const [isOpen, setIsOpen] = useState(false);
  const [position, setPosition] = useState<DraggableModalPosition>({ x: 100, y: 100 });

  const openModal = (initialPos?: DraggableModalPosition) => {
    if (initialPos) setPosition(initialPos);
    setIsOpen(true);
  };

  const closeModal = () => setIsOpen(false);

  return {
    isOpen,
    position,
    openModal,
    closeModal,
    setPosition,
  };
}