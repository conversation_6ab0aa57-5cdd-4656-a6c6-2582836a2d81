"use client";

import { useState, useEffect } from "react";
import { X, Search, ChevronDown, ChevronUp, ExternalLink, GripVertical } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import { useFAQs } from "@/lib/hooks/useFAQs";
import { useProducts } from "@/lib/hooks/useProducts";
import { FAQ } from "@/lib/api/types";
import { Draggable } from "@/components/ui/draggable";

interface FloatingFAQsBoxProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  productId?: string;
  productName?: string;
}

export function FloatingFAQsBox({
  open,
  onOpenChange,
  productId,
  productName,
}: FloatingFAQsBoxProps) {
  const { faqs, isLoading } = useFAQs();
  const { data: products = [] } = useProducts();
  const [searchTerm, setSearchTerm] = useState("");
  const [expandedFaqs, setExpandedFaqs] = useState<Record<string, boolean>>({});

  // Filter FAQs by product and search term
  const filteredFAQs = faqs
    .filter((faq) => {
      // Only show published FAQs
      if (!faq.is_published) return false;
      
      // Filter by product if provided
      if (productId && faq.product_id !== productId) return false;
      
      // Filter by search term
      if (
        searchTerm &&
        !faq.question.toLowerCase().includes(searchTerm.toLowerCase()) &&
        !faq.answer.toLowerCase().includes(searchTerm.toLowerCase())
      ) {
        return false;
      }
      
      return true;
    })
    .sort((a, b) => {
      // Sort by question alphabetically
      return a.question.localeCompare(b.question);
    });

  // Toggle FAQ expansion
  const toggleFaq = (faqId: string) => {
    setExpandedFaqs((prev) => ({
      ...prev,
      [faqId]: !prev[faqId],
    }));
  };

  // Get product name from ID if not provided
  const getProductName = () => {
    if (productName) return productName;
    if (productId) {
      const product = products.find((p) => p.id === productId);
      return product?.name || "Product";
    }
    return "All Products";
  };

  if (!open) return null;

  return (
    <Draggable initialPosition={{ x: 20, y: 20 }}>
      <div className="bg-card border rounded-lg shadow-lg w-[400px] max-h-[80vh] overflow-hidden">
        <div className="flex items-center justify-between p-4 border-b drag-handle cursor-grab">
          <div className="flex items-center gap-2">
            <GripVertical className="h-5 w-5 text-muted-foreground" />
            <h2 className="text-lg font-semibold">FAQs - {getProductName()}</h2>
          </div>
          <Button
            variant="ghost"
            size="icon"
            onClick={() => onOpenChange(false)}
            className="h-8 w-8"
          >
            <X className="size-4" />
          </Button>
        </div>
        
        {/* Add a style tag to ensure dropdowns appear above the box */}
        <style jsx global>{`
          .select-content {
            z-index: 300 !important;
          }
          .popover-content {
            z-index: 300 !important;
          }
          [data-radix-popper-content-wrapper] {
            z-index: 300 !important;
          }
        `}</style>
        
        <div className="p-4 space-y-4">
          {/* Search input */}
          <div className="relative">
            <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search FAQs..."
              className="pl-8"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
          
          {/* FAQ list */}
          {isLoading ? (
            <div className="flex items-center justify-center h-40">
              <p className="text-muted-foreground">Loading FAQs...</p>
            </div>
          ) : filteredFAQs.length === 0 ? (
            <div className="flex items-center justify-center h-40">
              <p className="text-muted-foreground">
                {searchTerm
                  ? "No FAQs match your search"
                  : `No FAQs available for ${getProductName()}`}
              </p>
            </div>
          ) : (
            <ScrollArea className="h-[400px] pr-4">
              <div className="space-y-2">
                {filteredFAQs.map((faq) => (
                  <div key={faq.id} className="border rounded-lg p-3">
                    <div
                      className="flex items-start justify-between cursor-pointer"
                      onClick={() => toggleFaq(faq.id)}
                    >
                      <h3 className="font-medium text-sm">{faq.question}</h3>
                      <Button variant="ghost" size="sm" className="h-6 w-6 p-0">
                        {expandedFaqs[faq.id] ? (
                          <ChevronUp className="h-4 w-4" />
                        ) : (
                          <ChevronDown className="h-4 w-4" />
                        )}
                      </Button>
                    </div>
                    
                    {expandedFaqs[faq.id] && (
                      <div className="mt-2">
                        <Separator className="my-2" />
                        <div className="text-sm mt-2">{faq.answer}</div>
                        {faq.category && (
                          <div className="mt-2 flex items-center gap-1">
                            <Badge variant="outline" className="text-xs">
                              {faq.category.name}
                            </Badge>
                          </div>
                        )}
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </ScrollArea>
          )}
        </div>
      </div>
    </Draggable>
  );
}
