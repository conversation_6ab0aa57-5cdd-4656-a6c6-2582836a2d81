'use client'

import { useEffect, useState } from 'react'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { <PERSON>ertTriangle, ShieldAlert } from 'lucide-react'
import Link from 'next/link'
import { useRouter } from 'next/navigation'
import { logError } from '@/lib/utils/error-logger'

interface ErrorBoundaryProps {
  children: React.ReactNode
}

export function ErrorBoundary({ children }: ErrorBoundaryProps) {
  const [error, setError] = useState<Error | null>(null)
  const [errorInfo, setErrorInfo] = useState<React.ErrorInfo | null>(null)
  const router = useRouter()

  useEffect(() => {
    // Add global error handler for uncaught errors
    const handleError = (event: ErrorEvent) => {
      console.error('Global error caught:', event.error)
      setError(event.error)
      
      // Log the error
      logError({
        message: event.error.message,
        stack: event.error.stack,
        type: 'uncaught',
        location: window.location.href
      })
      
      event.preventDefault()
    }

    // Add global handler for unhandled promise rejections
    const handleRejection = (event: PromiseRejectionEvent) => {
      console.error('Unhandled promise rejection:', event.reason)
      
      const error = event.reason instanceof Error 
        ? event.reason 
        : new Error(String(event.reason))
      
      setError(error)
      
      // Log the error
      logError({
        message: String(event.reason),
        stack: error.stack,
        type: 'unhandled-promise',
        location: window.location.href
      })
      
      event.preventDefault()
    }

    // Add event listeners
    window.addEventListener('error', handleError)
    window.addEventListener('unhandledrejection', handleRejection)

    // Clean up
    return () => {
      window.removeEventListener('error', handleError)
      window.removeEventListener('unhandledrejection', handleRejection)
    }
  }, [])

  // If there's an error, render the error UI
  if (error) {
    // Check if it's a permission error
    const isPermissionError = 
      error.name === 'ForbiddenError' || 
      error.message.includes('permission') || 
      error.message.includes('403') || 
      error.message.includes('Forbidden')

    return (
      <div className="container relative min-h-screen flex-col items-center justify-center grid lg:max-w-none lg:grid-cols-1 lg:px-0">
        <Card className="mx-auto w-full max-w-md">
          <CardHeader className="space-y-1 flex flex-col items-center">
            <div className="w-12 h-12 rounded-full bg-destructive/10 flex items-center justify-center mb-4">
              {isPermissionError ? (
                <ShieldAlert className="w-6 h-6 text-destructive" />
              ) : (
                <AlertTriangle className="w-6 h-6 text-destructive" />
              )}
            </div>
            <CardTitle className="text-2xl text-center">
              {isPermissionError ? 'Access Denied' : 'Something went wrong'}
            </CardTitle>
            <CardDescription className="text-center">
              {isPermissionError 
                ? 'You don\'t have permission to access this resource.' 
                : 'An error occurred while processing your request.'}
            </CardDescription>
            {error.message && !isPermissionError && (
              <div className="mt-2 text-sm text-muted-foreground text-center">
                {error.message}
              </div>
            )}
          </CardHeader>
          <CardContent className="flex justify-center gap-2">
            <Button 
              variant="outline" 
              onClick={() => {
                setError(null)
                setErrorInfo(null)
              }}
            >
              Try Again
            </Button>
            <Button asChild>
              <Link href="/dashboard">
                Return to Dashboard
              </Link>
            </Button>
          </CardContent>
        </Card>
      </div>
    )
  }

  // If there's no error, render the children
  return <>{children}</>
}

// This component can be used as a wrapper for specific components
export function withErrorBoundary<P extends object>(
  Component: React.ComponentType<P>
): React.FC<P> {
  return function WithErrorBoundary(props: P) {
    return (
      <ErrorBoundary>
        <Component {...props} />
      </ErrorBoundary>
    )
  }
}
