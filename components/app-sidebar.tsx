"use client"

import {
  BarChart3,
  Headphones,
  LayoutDashboard,
  LogOut,
  Building,
  Settings,
  MessagesSquare,
  Ticket,
  Users,
  Menu,
  Moon,
  Sun,
  Layers2,
  ChevronDown,
  ChevronRight,
  AlertTriangle,
  HelpCircle,
  FileText,
  Home,
  Phone,
  History,
  FileAudio
} from "lucide-react"
import { usePathname } from "next/navigation"
import Link from "next/link"
import { useTheme } from "next-themes"
import { useState, useEffect, useMemo, useCallback } from "react"
import { Button } from "@/components/ui/button"
import { useAuth } from "@/lib/hooks"
import { Role } from "@/lib/api/types"
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarTrigger,
  SidebarSeparator,
  SidebarGroupLabel,
} from "@/components/ui/sidebar"

interface MenuItem {
  title: string;
  icon: React.ComponentType<{ className?: string }>;
  href?: string;
  show: boolean;
  children?: MenuItem[];
  type?: 'item' | 'submenu' | 'group';
}

const getMenuItems = (role: Role | null | undefined): MenuItem[] => {
  if (!role) return [
    {
      title: "Dashboard",
      icon: LayoutDashboard,
      href: "/dashboard",
      show: true,
      type: 'item' as const
    }
  ];

  // Helper function to check if role has specific permission code
  const hasPermissionCode = (code: string): boolean => {
    if (role.name === 'platform_owner') return true;
    const hasPermission = role.permissions?.some(p => p.code === code) ?? false;
    console.log(`🔑 Checking permission ${code} for role ${role.name}:`, {
      hasPermission,
      availablePermissions: role.permissions?.map(p => p.code)
    });
    return hasPermission;
  };

  // Helper to check if user has any of the given permissions
  const hasAnyPermission = (codes: string[]): boolean => {
    return codes.some(code => hasPermissionCode(code));
  };

  return [
    // Dashboard Group
    {
      title: "Main",
      icon: Home,
      show: true,
      type: 'group' as const,
      children: [
        {
          title: "Dashboard",
          icon: LayoutDashboard,
          href: "/dashboard",
          show: true,
          type: 'item' as const
        },
        // if role.name == 'agent'
        ...(role.name === 'agent' ? [{
          title: "My Tickets",
          icon: Ticket,
          href: "/dashboard/my-tickets",
          show: true,
          type: 'item' as const
        }] : []),
      ]
    },

    // Ticket Management Group
    {
      title: "Ticket Management",
      icon: Ticket,
      show: hasAnyPermission(['tickets:read', 'tickets:create']),
      type: 'group' as const,
      children: [
        // Only show general tickets to non-agents (supervisors, admins, platform owners)
        ...(role.name !== 'agent' ? [{
          title: "Tickets",
          icon: Ticket,
          href: "/dashboard/tickets",
          show: hasAnyPermission(['tickets:read', 'tickets:create']),
          type: 'item' as const
        }] : []),
        // Only show escalations to supervisors and above
        ...(role.name === 'supervisor' || role.name === 'admin' || role.name === 'platform_owner' ? [{
          title: "Escalations",
          icon: AlertTriangle,
          href: "/dashboard/tickets/escalations",
          show: hasAnyPermission(['tickets:read', 'ticket:escalate']),
          type: 'item' as const
        }] : []),
        {
          title: "FAQs",
          icon: HelpCircle,
          href: "/dashboard/tickets/faqs",
          show: hasAnyPermission(['faq:view', 'faq:create']),
          type: 'item' as const
        }
      ]
    },

    // Customer Management Group
    {
      title: "Customer Management",
      icon: Users,
      show: hasAnyPermission(['user:view', 'tickets:read']),
      type: 'group' as const,
      children: [
        {
          title: "Contacts",
          icon: Headphones,
          href: "/dashboard/contacts",
          show: hasPermissionCode('tickets:read'),
          type: 'item' as const
        },
        {
          title: "Users",
          icon: Users,
          href: "/dashboard/users",
          show: hasPermissionCode('user:view'),
          type: 'item' as const
        }
      ]
    },

    // Configuration Group
    {
      title: "Configuration",
      icon: Settings,
      show: hasAnyPermission(['product:view', 'channel:view']),
      type: 'group' as const,
      children: [
        {
          title: "Categories",
          icon: Layers2,
          href: "/dashboard/categories",
          show: hasAnyPermission(['product:view']),
          type: 'item' as const
        },
        {
          title: "Channels",
          icon: MessagesSquare,
          href: "/dashboard/channels",
          show: hasAnyPermission(['channel:view']),
          type: 'item' as const
        },
        {
          title: "Products",
          icon: Building,
          href: "/dashboard/products",
          show: hasAnyPermission(['product:view']),
          type: 'item' as const
        }
      ]
    },

    // Reports Group
    {
      title: "Reports",
      icon: BarChart3,
      show: hasAnyPermission(['report:view', 'analytics:view']),
      type: 'group' as const,
      children: [
        {
          title: "Performance Analytics",
          icon: BarChart3,
          href: "/dashboard/reports",
          show: hasAnyPermission(['report:view']),
          type: 'item' as const
        },
        {
          title: "Call Reports",
          icon: Phone,
          href: "/dashboard/reports",
          show: hasAnyPermission(['report:view']),
          type: 'item' as const
        }
      ]
    },

    // Inbound Calls Group
    {
      title: "Inbound Calls",
      icon: Phone,
      show: hasAnyPermission(['tickets:read', 'tickets:create']),
      type: 'group' as const,
      children: [
        {
          title: "Call Center",
          icon: Phone,
          href: "/calls",
          show: hasAnyPermission(['tickets:read', 'tickets:create']),
          type: 'item' as const
        },
        {
          title: "Call History",
          icon: History,
          href: "/calls?tab=history",
          show: hasAnyPermission(['tickets:read']),
          type: 'item' as const
        },
        {
          title: "Recordings",
          icon: FileAudio,
          href: "/calls?tab=recordings",
          show: hasAnyPermission(['tickets:read']),
          type: 'item' as const
        }
      ]
    },

    // Settings (standalone item)
    ...(role.name === 'platform_owner' ? [{
      title: "Settings",
      icon: Settings,
      href: "/dashboard/settings",
      show: hasAnyPermission([
        'role:view',
        'department:view',
        'system:settings:view'
      ]),
      type: 'item' as const
    }] : []),
  ];
};

export function AppSidebar() {
  const pathname = usePathname()
  const { setTheme, theme } = useTheme()
  const { user } = useAuth()
  const [expandedGroups, setExpandedGroups] = useState<Record<string, boolean>>({})

  // Memoize menu items to prevent unnecessary recalculations
  const menuItems = useMemo(() => getMenuItems(user?.role), [user?.role])

  // Toggle group expansion (memoized to prevent unnecessary recreations)
  const toggleGroup = useCallback((groupTitle: string) => {
    setExpandedGroups(prev => ({
      ...prev,
      [groupTitle]: !prev[groupTitle]
    }))
  }, [])

  // Initialize expanded state for all groups (default to expanded)
  // Only run once on component mount
  useEffect(() => {
    const initialState: Record<string, boolean> = {};
    menuItems.forEach((item: MenuItem) => {
      if (item.type === 'group' && item.title) {
        initialState[item.title] = true;
      }
    });
    setExpandedGroups(initialState);
  }, []);

  // Render a menu item
  const renderMenuItem = useCallback((item: MenuItem) => {
    if (!item.show) return null;

    // For regular menu items
    if (item.type === 'item' && item.href) {
      return (
        <SidebarMenuItem key={item.href}>
          <SidebarMenuButton asChild isActive={pathname === item.href} tooltip={item.title}>
            <Link href={item.href}>
              <item.icon className="h-5 w-5" />
              <span>{item.title}</span>
            </Link>
          </SidebarMenuButton>
        </SidebarMenuItem>
      );
    }

    // For groups with children
    if (item.type === 'group' && item.children && item.children.length > 0) {
      const visibleChildren = item.children.filter(child => child.show);
      if (visibleChildren.length === 0) return null;

      const isExpanded = expandedGroups[item.title] ?? true;

      return (
        <div key={item.title} className="mb-2">
          <SidebarGroupLabel
            className="flex cursor-pointer items-center justify-between px-2 py-1.5 hover:bg-sidebar-accent/50 rounded-md"
            onClick={() => toggleGroup(item.title)}
          >
            <div className="flex items-center gap-2">
              <item.icon className="size-4" />
              <span>{item.title}</span>
            </div>
            {isExpanded ? (
              <ChevronDown className="size-4" />
            ) : (
              <ChevronRight className="size-4" />
            )}
          </SidebarGroupLabel>

          {isExpanded && (
            <SidebarMenu className="mt-1 pl-2">
              {visibleChildren.map((child) => (
                <SidebarMenuItem key={child.href || `${child.title}-${Math.random()}`}>
                  {child.type === 'item' && child.href && (
                    <SidebarMenuButton asChild isActive={pathname === child.href} tooltip={child.title}>
                      <Link href={child.href}>
                        <child.icon className="h-5 w-5" />
                        <span>{child.title}</span>
                      </Link>
                    </SidebarMenuButton>
                  )}
                </SidebarMenuItem>
              ))}
            </SidebarMenu>
          )}
        </div>
      );
    }

    return null;
  }, [pathname, expandedGroups, toggleGroup]);

  return (
    <Sidebar variant="floating" className="border-r">
      <SidebarHeader className="flex flex-col gap-0 py-4">
        <div className="flex items-center px-4">
          <div className="flex items-center gap-2 font-semibold text-xl">
            <Headphones className="h-6 w-6 text-primary" />
            <span className="font-bold">CallCenter</span>
          </div>
          <SidebarTrigger className="ml-auto md:hidden" />
        </div>
      </SidebarHeader>
      <SidebarSeparator />
      <SidebarContent>
        {menuItems.map((item: MenuItem) => renderMenuItem(item))}
      </SidebarContent>
      <SidebarFooter className="mt-auto">
        <SidebarSeparator />
        <div className="p-4">
          <div className="flex items-center gap-4 mb-4">
            <Button variant="outline" size="icon" onClick={() => setTheme(theme === "dark" ? "light" : "dark")}>
              <Sun className="size-4 rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0" />
              <Moon className="absolute size-4 rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100" />
              <span className="sr-only">Toggle theme</span>
            </Button>
            <Button variant="outline" size="icon">
              <Menu className="size-4" />
            </Button>
          </div>
        </div>
      </SidebarFooter>
    </Sidebar>
  )
}

