"use client"

import { useTheme } from "next-themes"
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  CartesianGrid,
  Legend,
  Line,
  LineChart,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  YAxis,
  Cell,
  Pie,
  PieChart,
} from "recharts"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Ta<PERSON>, <PERSON><PERSON><PERSON><PERSON>nt, Tabs<PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { Progress } from "@/components/ui/progress"
import { Badge } from "@/components/ui/badge"

// Sample data for call volume trends
const callVolumeTrendData = {
  daily: [
    { name: "<PERSON>", inbound: 120, outbound: 80, abandoned: 12 },
    { name: "<PERSON><PERSON>", inbound: 140, outbound: 90, abandoned: 15 },
    { name: "Wed", inbound: 160, outbound: 100, abandoned: 18 },
    { name: "Thu", inbound: 180, outbound: 110, abandoned: 20 },
    { name: "<PERSON><PERSON>", inbound: 150, outbound: 95, abandoned: 16 },
    { name: "<PERSON><PERSON>", inbound: 100, outbound: 60, abandoned: 10 },
    { name: "<PERSON>", inbound: 80, outbound: 40, abandoned: 8 },
  ],
  weekly: [
    { name: "Week 1", inbound: 850, outbound: 550, abandoned: 85 },
    { name: "Week 2", inbound: 900, outbound: 600, abandoned: 90 },
    { name: "Week 3", inbound: 950, outbound: 650, abandoned: 95 },
    { name: "Week 4", inbound: 1000, outbound: 700, abandoned: 100 },
  ],
  monthly: [
    { name: "Jan", inbound: 3500, outbound: 2200, abandoned: 350 },
    { name: "Feb", inbound: 3800, outbound: 2400, abandoned: 380 },
    { name: "Mar", inbound: 4100, outbound: 2600, abandoned: 410 },
    { name: "Apr", inbound: 3900, outbound: 2500, abandoned: 390 },
    { name: "May", inbound: 4200, outbound: 2700, abandoned: 420 },
    { name: "Jun", inbound: 4500, outbound: 2900, abandoned: 450 },
  ],
}

// Sample data for abandoned call rate
const abandonedCallData = [
  { name: "8-9 AM", rate: 8.2 },
  { name: "9-10 AM", rate: 7.5 },
  { name: "10-11 AM", rate: 6.8 },
  { name: "11-12 PM", rate: 9.2 },
  { name: "12-1 PM", rate: 10.5 },
  { name: "1-2 PM", rate: 8.7 },
  { name: "2-3 PM", rate: 7.9 },
  { name: "3-4 PM", rate: 8.3 },
  { name: "4-5 PM", rate: 9.1 },
]

// Sample data for average handle time
const handleTimeData = [
  { name: "Account", avgTime: 4.2, target: 5.0 },
  { name: "Billing", avgTime: 6.8, target: 6.0 },
  { name: "Network", avgTime: 8.5, target: 7.0 },
  { name: "Service", avgTime: 3.5, target: 4.0 },
  { name: "Technical", avgTime: 7.2, target: 8.0 },
]

// Sample data for first call resolution
const firstCallResolutionData = [
  { name: "Account", rate: 85 },
  { name: "Billing", rate: 78 },
  { name: "Network", rate: 65 },
  { name: "Service", rate: 92 },
  { name: "Technical", rate: 72 },
]

// Sample data for SLA compliance
const slaComplianceData = [
  { name: "Response Time", compliance: 94, target: 95 },
  { name: "Resolution Time", compliance: 87, target: 90 },
  { name: "First Call Resolution", compliance: 82, target: 85 },
  { name: "Customer Satisfaction", compliance: 91, target: 90 },
  { name: "Call Quality", compliance: 88, target: 85 },
]

export function AdminReports() {
  const { theme } = useTheme()
  const isDark = theme === "dark"

  return (
    <div className="space-y-6">
      {/* Call Volume Trends */}
      <Card>
        <CardHeader>
          <CardTitle>Call Volume Trends</CardTitle>
          <CardDescription>Analysis of call volume patterns over time</CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="daily" className="space-y-4">
            <TabsList>
              <TabsTrigger value="daily">Daily</TabsTrigger>
              <TabsTrigger value="weekly">Weekly</TabsTrigger>
              <TabsTrigger value="monthly">Monthly</TabsTrigger>
            </TabsList>
            
            <TabsContent value="daily" className="space-y-4">
              <ResponsiveContainer width="100%" height={300}>
                <BarChart data={callVolumeTrendData.daily} margin={{ top: 10, right: 10, left: 0, bottom: 0 }}>
                  <CartesianGrid strokeDasharray="3 3" stroke={isDark ? "#333" : "#eee"} />
                  <XAxis dataKey="name" stroke={isDark ? "#888" : "#888"} fontSize={12} tickLine={false} axisLine={false} />
                  <YAxis stroke={isDark ? "#888" : "#888"} fontSize={12} tickLine={false} axisLine={false} />
                  <Tooltip
                    contentStyle={{
                      backgroundColor: isDark ? "#1f2937" : "#fff",
                      border: `1px solid ${isDark ? "#374151" : "#e5e7eb"}`,
                      borderRadius: "6px",
                      color: isDark ? "#e5e7eb" : "#1f2937",
                    }}
                  />
                  <Legend />
                  <Bar dataKey="inbound" name="Inbound Calls" fill="#3b82f6" radius={[4, 4, 0, 0]} />
                  <Bar dataKey="outbound" name="Outbound Calls" fill="#10b981" radius={[4, 4, 0, 0]} />
                  <Bar dataKey="abandoned" name="Abandoned Calls" fill="#ef4444" radius={[4, 4, 0, 0]} />
                </BarChart>
              </ResponsiveContainer>
            </TabsContent>
            
            <TabsContent value="weekly" className="space-y-4">
              <ResponsiveContainer width="100%" height={300}>
                <BarChart data={callVolumeTrendData.weekly} margin={{ top: 10, right: 10, left: 0, bottom: 0 }}>
                  <CartesianGrid strokeDasharray="3 3" stroke={isDark ? "#333" : "#eee"} />
                  <XAxis dataKey="name" stroke={isDark ? "#888" : "#888"} fontSize={12} tickLine={false} axisLine={false} />
                  <YAxis stroke={isDark ? "#888" : "#888"} fontSize={12} tickLine={false} axisLine={false} />
                  <Tooltip
                    contentStyle={{
                      backgroundColor: isDark ? "#1f2937" : "#fff",
                      border: `1px solid ${isDark ? "#374151" : "#e5e7eb"}`,
                      borderRadius: "6px",
                      color: isDark ? "#e5e7eb" : "#1f2937",
                    }}
                  />
                  <Legend />
                  <Bar dataKey="inbound" name="Inbound Calls" fill="#3b82f6" radius={[4, 4, 0, 0]} />
                  <Bar dataKey="outbound" name="Outbound Calls" fill="#10b981" radius={[4, 4, 0, 0]} />
                  <Bar dataKey="abandoned" name="Abandoned Calls" fill="#ef4444" radius={[4, 4, 0, 0]} />
                </BarChart>
              </ResponsiveContainer>
            </TabsContent>
            
            <TabsContent value="monthly" className="space-y-4">
              <ResponsiveContainer width="100%" height={300}>
                <BarChart data={callVolumeTrendData.monthly} margin={{ top: 10, right: 10, left: 0, bottom: 0 }}>
                  <CartesianGrid strokeDasharray="3 3" stroke={isDark ? "#333" : "#eee"} />
                  <XAxis dataKey="name" stroke={isDark ? "#888" : "#888"} fontSize={12} tickLine={false} axisLine={false} />
                  <YAxis stroke={isDark ? "#888" : "#888"} fontSize={12} tickLine={false} axisLine={false} />
                  <Tooltip
                    contentStyle={{
                      backgroundColor: isDark ? "#1f2937" : "#fff",
                      border: `1px solid ${isDark ? "#374151" : "#e5e7eb"}`,
                      borderRadius: "6px",
                      color: isDark ? "#e5e7eb" : "#1f2937",
                    }}
                  />
                  <Legend />
                  <Bar dataKey="inbound" name="Inbound Calls" fill="#3b82f6" radius={[4, 4, 0, 0]} />
                  <Bar dataKey="outbound" name="Outbound Calls" fill="#10b981" radius={[4, 4, 0, 0]} />
                  <Bar dataKey="abandoned" name="Abandoned Calls" fill="#ef4444" radius={[4, 4, 0, 0]} />
                </BarChart>
              </ResponsiveContainer>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>

      {/* Abandoned Call Rate */}
      <Card>
        <CardHeader>
          <CardTitle>Abandoned Call Rate</CardTitle>
          <CardDescription>Percentage of calls abandoned before being answered</CardDescription>
        </CardHeader>
        <CardContent>
          <ResponsiveContainer width="100%" height={300}>
            <LineChart data={abandonedCallData} margin={{ top: 10, right: 10, left: 0, bottom: 0 }}>
              <CartesianGrid strokeDasharray="3 3" stroke={isDark ? "#333" : "#eee"} />
              <XAxis dataKey="name" stroke={isDark ? "#888" : "#888"} fontSize={12} tickLine={false} axisLine={false} />
              <YAxis 
                stroke={isDark ? "#888" : "#888"} 
                fontSize={12} 
                tickLine={false} 
                axisLine={false}
                domain={[0, 15]}
                tickFormatter={(value) => `${value}%`}
              />
              <Tooltip
                contentStyle={{
                  backgroundColor: isDark ? "#1f2937" : "#fff",
                  border: `1px solid ${isDark ? "#374151" : "#e5e7eb"}`,
                  borderRadius: "6px",
                  color: isDark ? "#e5e7eb" : "#1f2937",
                }}
                formatter={(value) => [`${value}%`, 'Abandoned Rate']}
              />
              <Line type="monotone" dataKey="rate" name="Abandoned Rate" stroke="#ef4444" strokeWidth={2} dot={{ r: 4 }} />
            </LineChart>
          </ResponsiveContainer>
        </CardContent>
      </Card>

      {/* Average Handle Time */}
      <Card>
        <CardHeader>
          <CardTitle>Average Handle Time</CardTitle>
          <CardDescription>Average time spent handling calls by category (minutes)</CardDescription>
        </CardHeader>
        <CardContent>
          <ResponsiveContainer width="100%" height={300}>
            <BarChart data={handleTimeData} margin={{ top: 10, right: 10, left: 0, bottom: 0 }}>
              <CartesianGrid strokeDasharray="3 3" stroke={isDark ? "#333" : "#eee"} />
              <XAxis dataKey="name" stroke={isDark ? "#888" : "#888"} fontSize={12} tickLine={false} axisLine={false} />
              <YAxis stroke={isDark ? "#888" : "#888"} fontSize={12} tickLine={false} axisLine={false} />
              <Tooltip
                contentStyle={{
                  backgroundColor: isDark ? "#1f2937" : "#fff",
                  border: `1px solid ${isDark ? "#374151" : "#e5e7eb"}`,
                  borderRadius: "6px",
                  color: isDark ? "#e5e7eb" : "#1f2937",
                }}
              />
              <Legend />
              <Bar dataKey="avgTime" name="Average Time (min)" fill="#3b82f6" radius={[4, 4, 0, 0]} />
              <Line type="monotone" dataKey="target" name="Target Time" stroke="#f59e0b" strokeWidth={2} dot={false} />
            </BarChart>
          </ResponsiveContainer>
        </CardContent>
      </Card>

      {/* First Call Resolution */}
      <Card>
        <CardHeader>
          <CardTitle>First Call Resolution</CardTitle>
          <CardDescription>Percentage of calls resolved on first contact</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-6">
            {firstCallResolutionData.map((item) => (
              <div key={item.name} className="space-y-2">
                <div className="flex items-center justify-between">
                  <div className="font-medium">{item.name}</div>
                  <div className="text-sm text-muted-foreground">
                    {item.rate}%
                  </div>
                </div>
                <Progress value={item.rate} className="h-2" />
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* SLA Compliance */}
      <Card>
        <CardHeader>
          <CardTitle>SLA Compliance</CardTitle>
          <CardDescription>Service Level Agreement compliance metrics</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-6">
            {slaComplianceData.map((item) => (
              <div key={item.name} className="space-y-2">
                <div className="flex items-center justify-between">
                  <div className="font-medium">{item.name}</div>
                  <div className="flex items-center gap-2">
                    <div className="text-sm text-muted-foreground">
                      {item.compliance}% / {item.target}% target
                    </div>
                    <Badge variant={item.compliance >= item.target ? "success" : "destructive"}>
                      {item.compliance >= item.target ? "Meeting SLA" : "Below SLA"}
                    </Badge>
                  </div>
                </div>
                <div className="relative pt-1">
                  <div className="overflow-hidden h-2 text-xs flex rounded bg-muted">
                    <div
                      style={{ width: `${item.compliance}%` }}
                      className={`shadow-none flex flex-col text-center whitespace-nowrap text-white justify-center ${
                        item.compliance >= item.target ? "bg-green-500" : "bg-red-500"
                      }`}
                    ></div>
                    <div
                      style={{ 
                        position: 'absolute', 
                        left: `${item.target}%`, 
                        height: '10px', 
                        width: '2px',
                        top: '4px',
                        backgroundColor: isDark ? '#fff' : '#000'
                      }}
                    ></div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
