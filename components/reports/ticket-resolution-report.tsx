"use client"

import { useTheme } from "next-themes"
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  CartesianGrid,
  Legend,
  Line,
  LineChart,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  YAxis,
} from "recharts"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"

const resolutionTimeData = [
  { name: "Account", avgTime: 4.2, firstResponseTime: 0.5 },
  { name: "Billing", avgTime: 6.8, firstResponseTime: 0.8 },
  { name: "Network", avgTime: 8.5, firstResponseTime: 0.3 },
  { name: "Service", avgTime: 3.5, firstResponseTime: 0.6 },
  { name: "Technical", avgTime: 7.2, firstResponseTime: 0.4 },
]

const resolutionTrendData = [
  { name: "Week 1", avgTime: 6.5, ticketsResolved: 120, reopened: 8 },
  { name: "Week 2", avgTime: 6.2, ticketsResolved: 135, reopened: 10 },
  { name: "Week 3", avgTime: 5.8, ticketsResolved: 142, reopened: 7 },
  { name: "Week 4", avgTime: 5.5, ticketsResolved: 150, reopened: 6 },
]

const resolutionRateData = [
  { category: "Account", resolved: 85, total: 100 },
  { category: "Billing", resolved: 92, total: 100 },
  { category: "Network", resolved: 78, total: 100 },
  { category: "Service", resolved: 95, total: 100 },
  { category: "Technical", resolved: 82, total: 100 },
]

export function TicketResolutionReport() {
  const { theme } = useTheme()
  const isDark = theme === "dark"

  return (
    <div className="space-y-4">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-base">Resolution Time by Category</CardTitle>
            <CardDescription>Average time to resolve tickets (hours)</CardDescription>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={resolutionTimeData} margin={{ top: 10, right: 10, left: 0, bottom: 0 }}>
                <CartesianGrid strokeDasharray="3 3" stroke={isDark ? "#333" : "#eee"} />
                <XAxis
                  dataKey="name"
                  stroke={isDark ? "#888" : "#888"}
                  fontSize={12}
                  tickLine={false}
                  axisLine={false}
                />
                <YAxis stroke={isDark ? "#888" : "#888"} fontSize={12} tickLine={false} axisLine={false} />
                <Tooltip
                  contentStyle={{
                    backgroundColor: isDark ? "#1f2937" : "#fff",
                    border: `1px solid ${isDark ? "#374151" : "#e5e7eb"}`,
                    borderRadius: "6px",
                    color: isDark ? "#e5e7eb" : "#1f2937",
                  }}
                />
                <Legend />
                <Bar dataKey="avgTime" fill="#3b82f6" radius={[4, 4, 0, 0]} name="Avg Resolution Time (h)" />
                <Bar dataKey="firstResponseTime" fill="#10b981" radius={[4, 4, 0, 0]} name="First Response Time (h)" />
              </BarChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-base">Resolution Trends</CardTitle>
            <CardDescription>Weekly trends in resolution time and volume</CardDescription>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <LineChart data={resolutionTrendData} margin={{ top: 10, right: 10, left: 0, bottom: 0 }}>
                <CartesianGrid strokeDasharray="3 3" stroke={isDark ? "#333" : "#eee"} />
                <XAxis
                  dataKey="name"
                  stroke={isDark ? "#888" : "#888"}
                  fontSize={12}
                  tickLine={false}
                  axisLine={false}
                />
                <YAxis
                  yAxisId="left"
                  stroke={isDark ? "#888" : "#888"}
                  fontSize={12}
                  tickLine={false}
                  axisLine={false}
                />
                <YAxis
                  yAxisId="right"
                  orientation="right"
                  stroke={isDark ? "#888" : "#888"}
                  fontSize={12}
                  tickLine={false}
                  axisLine={false}
                />
                <Tooltip
                  contentStyle={{
                    backgroundColor: isDark ? "#1f2937" : "#fff",
                    border: `1px solid ${isDark ? "#374151" : "#e5e7eb"}`,
                    borderRadius: "6px",
                    color: isDark ? "#e5e7eb" : "#1f2937",
                  }}
                />
                <Legend />
                <Line
                  yAxisId="left"
                  type="monotone"
                  dataKey="avgTime"
                  stroke="#f59e0b"
                  name="Avg Resolution Time (h)"
                  strokeWidth={2}
                  dot={{ r: 3 }}
                  activeDot={{ r: 5 }}
                />
                <Line
                  yAxisId="right"
                  type="monotone"
                  dataKey="ticketsResolved"
                  stroke="#3b82f6"
                  name="Tickets Resolved"
                  strokeWidth={2}
                  dot={{ r: 3 }}
                  activeDot={{ r: 5 }}
                />
                <Line
                  yAxisId="right"
                  type="monotone"
                  dataKey="reopened"
                  stroke="#ef4444"
                  name="Tickets Reopened"
                  strokeWidth={2}
                  dot={{ r: 3 }}
                  activeDot={{ r: 5 }}
                />
              </LineChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Resolution Rate by Category</CardTitle>
          <CardDescription>Percentage of tickets resolved by category</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-6">
            {resolutionRateData.map((item) => (
              <div key={item.category} className="space-y-2">
                <div className="flex items-center justify-between">
                  <div className="font-medium">{item.category}</div>
                  <div className="text-sm text-muted-foreground">
                    {item.resolved}/{item.total} ({Math.round((item.resolved / item.total) * 100)}%)
                  </div>
                </div>
                <Progress value={(item.resolved / item.total) * 100} className="h-2" />
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

