"use client"

import { useTheme } from "next-themes"
import {
  <PERSON>,
  <PERSON><PERSON>hart,
  CartesianGrid,
  Legend,
  Line,
  LineChart,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  YAxis,
  Cell,
  Pie,
  PieChart,
} from "recharts"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"

// Sample data for agent availability
const agentAvailabilityData = [
  { id: 1, name: "<PERSON>", avatar: "/placeholder.svg", initials: "<PERSON><PERSON>", status: "Available", statusSince: "09:15 AM" },
  { id: 2, name: "<PERSON>", avatar: "/placeholder.svg", initials: "<PERSON>", status: "On Call", statusSince: "10:30 AM" },
  { id: 3, name: "<PERSON><PERSON>", avatar: "/placeholder.svg", initials: "<PERSON>", status: "Break", statusSince: "11:45 AM" },
  { id: 4, name: "<PERSON> O<PERSON>eng", avatar: "/placeholder.svg", initials: "DO", status: "Available", statusSince: "08:00 AM" },
  { id: 5, name: "Emma <PERSON>", avatar: "/placeholder.svg", initials: "EW", status: "Lunch", statusSince: "12:00 PM" },
  { id: 6, name: "<PERSON> <PERSON>", avatar: "/placeholder.svg", initials: "CR", status: "Available", statusSince: "09:30 AM" },
]

// Sample data for call transfers and escalations
const transferEscalationData = [
  { name: "Sarah", transfers: 12, escalations: 5 },
  { name: "Michael", transfers: 8, escalations: 3 },
  { name: "Aisha", transfers: 15, escalations: 7 },
  { name: "David", transfers: 10, escalations: 4 },
  { name: "Emma", transfers: 7, escalations: 2 },
  { name: "Carlos", transfers: 14, escalations: 6 },
]

// Sample data for call handling time
const callHandlingTimeData = [
  { name: "Sarah", avgTime: 4.2, target: 5.0 },
  { name: "Michael", avgTime: 6.8, target: 6.0 },
  { name: "Aisha", avgTime: 3.5, target: 5.0 },
  { name: "David", avgTime: 5.2, target: 5.0 },
  { name: "Emma", avgTime: 4.8, target: 5.0 },
  { name: "Carlos", avgTime: 5.5, target: 5.0 },
]

// Sample data for call abandonment
const callAbandonmentData = [
  { hour: "8-9 AM", total: 45, abandoned: 4, rate: 8.9 },
  { hour: "9-10 AM", total: 62, abandoned: 5, rate: 8.1 },
  { hour: "10-11 AM", total: 78, abandoned: 6, rate: 7.7 },
  { hour: "11-12 PM", total: 85, abandoned: 8, rate: 9.4 },
  { hour: "12-1 PM", total: 70, abandoned: 7, rate: 10.0 },
  { hour: "1-2 PM", total: 65, abandoned: 5, rate: 7.7 },
  { hour: "2-3 PM", total: 72, abandoned: 6, rate: 8.3 },
  { hour: "3-4 PM", total: 80, abandoned: 7, rate: 8.8 },
  { hour: "4-5 PM", total: 68, abandoned: 6, rate: 8.8 },
]

// Sample data for average response time
const responseTimeData = [
  { name: "Sarah", avgResponse: 45, target: 60 },
  { name: "Michael", avgResponse: 52, target: 60 },
  { name: "Aisha", avgResponse: 38, target: 60 },
  { name: "David", avgResponse: 65, target: 60 },
  { name: "Emma", avgResponse: 48, target: 60 },
  { name: "Carlos", avgResponse: 55, target: 60 },
]

export function SupervisorReports() {
  const { theme } = useTheme()
  const isDark = theme === "dark"

  // Function to get status badge variant
  const getStatusVariant = (status: string) => {
    switch (status) {
      case "Available":
        return "success"
      case "On Call":
        return "default"
      case "Break":
      case "Lunch":
        return "secondary"
      default:
        return "outline"
    }
  }

  return (
    <div className="space-y-6">
      {/* Agent Availability Status */}
      <Card>
        <CardHeader>
          <CardTitle>Agent Availability Status</CardTitle>
          <CardDescription>Current status of all agents</CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Agent</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Since</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {agentAvailabilityData.map((agent) => (
                <TableRow key={agent.id}>
                  <TableCell>
                    <div className="flex items-center gap-3">
                      <Avatar>
                        <AvatarImage src={agent.avatar} alt={agent.name} />
                        <AvatarFallback>{agent.initials}</AvatarFallback>
                      </Avatar>
                      <div>{agent.name}</div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge variant={getStatusVariant(agent.status) as any}>{agent.status}</Badge>
                  </TableCell>
                  <TableCell>{agent.statusSince}</TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* Call Transfers and Escalations */}
      <Card>
        <CardHeader>
          <CardTitle>Call Transfers and Escalations</CardTitle>
          <CardDescription>Number of transfers and escalations by agent</CardDescription>
        </CardHeader>
        <CardContent>
          <ResponsiveContainer width="100%" height={300}>
            <BarChart data={transferEscalationData} margin={{ top: 10, right: 10, left: 0, bottom: 0 }}>
              <CartesianGrid strokeDasharray="3 3" stroke={isDark ? "#333" : "#eee"} />
              <XAxis dataKey="name" stroke={isDark ? "#888" : "#888"} fontSize={12} tickLine={false} axisLine={false} />
              <YAxis stroke={isDark ? "#888" : "#888"} fontSize={12} tickLine={false} axisLine={false} />
              <Tooltip
                contentStyle={{
                  backgroundColor: isDark ? "#1f2937" : "#fff",
                  border: `1px solid ${isDark ? "#374151" : "#e5e7eb"}`,
                  borderRadius: "6px",
                  color: isDark ? "#e5e7eb" : "#1f2937",
                }}
              />
              <Legend />
              <Bar dataKey="transfers" name="Transfers" fill="#3b82f6" radius={[4, 4, 0, 0]} />
              <Bar dataKey="escalations" name="Escalations" fill="#f59e0b" radius={[4, 4, 0, 0]} />
            </BarChart>
          </ResponsiveContainer>
        </CardContent>
      </Card>

      {/* Call Handling Time */}
      <Card>
        <CardHeader>
          <CardTitle>Call Handling Time</CardTitle>
          <CardDescription>Average time spent handling calls by agent (minutes)</CardDescription>
        </CardHeader>
        <CardContent>
          <ResponsiveContainer width="100%" height={300}>
            <BarChart data={callHandlingTimeData} margin={{ top: 10, right: 10, left: 0, bottom: 0 }}>
              <CartesianGrid strokeDasharray="3 3" stroke={isDark ? "#333" : "#eee"} />
              <XAxis dataKey="name" stroke={isDark ? "#888" : "#888"} fontSize={12} tickLine={false} axisLine={false} />
              <YAxis stroke={isDark ? "#888" : "#888"} fontSize={12} tickLine={false} axisLine={false} />
              <Tooltip
                contentStyle={{
                  backgroundColor: isDark ? "#1f2937" : "#fff",
                  border: `1px solid ${isDark ? "#374151" : "#e5e7eb"}`,
                  borderRadius: "6px",
                  color: isDark ? "#e5e7eb" : "#1f2937",
                }}
              />
              <Legend />
              <Bar dataKey="avgTime" name="Average Time (min)" fill="#3b82f6" radius={[4, 4, 0, 0]} />
              <Line type="monotone" dataKey="target" name="Target Time" stroke="#f59e0b" strokeWidth={2} dot={false} />
            </BarChart>
          </ResponsiveContainer>
        </CardContent>
      </Card>

      {/* Call Abandonment Report */}
      <Card>
        <CardHeader>
          <CardTitle>Call Abandonment Report</CardTitle>
          <CardDescription>Hourly breakdown of abandoned calls</CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Time Period</TableHead>
                <TableHead>Total Calls</TableHead>
                <TableHead>Abandoned</TableHead>
                <TableHead>Abandonment Rate</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {callAbandonmentData.map((item) => (
                <TableRow key={item.hour}>
                  <TableCell>{item.hour}</TableCell>
                  <TableCell>{item.total}</TableCell>
                  <TableCell>{item.abandoned}</TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <Progress value={item.rate} className="w-[60px]" />
                      <span>{item.rate.toFixed(1)}%</span>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* Average Response Time */}
      <Card>
        <CardHeader>
          <CardTitle>Average Response Time</CardTitle>
          <CardDescription>Average time to respond to calls by agent (seconds)</CardDescription>
        </CardHeader>
        <CardContent>
          <ResponsiveContainer width="100%" height={300}>
            <BarChart data={responseTimeData} margin={{ top: 10, right: 10, left: 0, bottom: 0 }}>
              <CartesianGrid strokeDasharray="3 3" stroke={isDark ? "#333" : "#eee"} />
              <XAxis dataKey="name" stroke={isDark ? "#888" : "#888"} fontSize={12} tickLine={false} axisLine={false} />
              <YAxis stroke={isDark ? "#888" : "#888"} fontSize={12} tickLine={false} axisLine={false} />
              <Tooltip
                contentStyle={{
                  backgroundColor: isDark ? "#1f2937" : "#fff",
                  border: `1px solid ${isDark ? "#374151" : "#e5e7eb"}`,
                  borderRadius: "6px",
                  color: isDark ? "#e5e7eb" : "#1f2937",
                }}
              />
              <Legend />
              <Bar dataKey="avgResponse" name="Response Time (sec)" fill="#3b82f6" radius={[4, 4, 0, 0]} />
              <Line type="monotone" dataKey="target" name="Target Time" stroke="#f59e0b" strokeWidth={2} dot={false} />
            </BarChart>
          </ResponsiveContainer>
        </CardContent>
      </Card>
    </div>
  )
}
