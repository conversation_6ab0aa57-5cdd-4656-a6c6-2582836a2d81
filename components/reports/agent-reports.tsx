"use client"

import { useTheme } from "next-themes"
import { Card, Card<PERSON>ontent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { Clock, PhoneCall, User } from "lucide-react"

// Sample data for live call queue
const liveCallQueueData = [
  { id: "CQ-001", waitTime: 45, position: 1, product: "Internet Service", priority: "High" },
  { id: "CQ-002", waitTime: 120, position: 2, product: "Mobile Plan", priority: "Medium" },
  { id: "CQ-003", waitTime: 180, position: 3, product: "TV Subscription", priority: "Low" },
  { id: "CQ-004", waitTime: 210, position: 4, product: "Billing Issue", priority: "Medium" },
  { id: "CQ-005", waitTime: 240, position: 5, product: "Technical Support", priority: "High" },
]

// Sample data for queue statistics
const queueStatistics = {
  totalCalls: 12,
  avgWaitTime: 185,
  longestWait: 240,
  availableAgents: 8,
  busyAgents: 15,
}

export function AgentReports() {
  const { theme } = useTheme()
  const isDark = theme === "dark"

  // Function to format wait time
  const formatWaitTime = (seconds: number) => {
    const minutes = Math.floor(seconds / 60)
    const remainingSeconds = seconds % 60
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`
  }

  // Function to get priority badge variant
  const getPriorityVariant = (priority: string) => {
    switch (priority) {
      case "High":
        return "destructive"
      case "Medium":
        return "default"
      case "Low":
        return "secondary"
      default:
        return "outline"
    }
  }

  return (
    <div className="space-y-6">
      {/* Live Call Queue Status */}
      <Card>
        <CardHeader>
          <CardTitle>Live Call Queue Status</CardTitle>
          <CardDescription>Real-time view of calls waiting in the queue</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Calls in Queue</CardTitle>
                <PhoneCall className="size-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{queueStatistics.totalCalls}</div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Avg. Wait Time</CardTitle>
                <Clock className="size-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{formatWaitTime(queueStatistics.avgWaitTime)}</div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Longest Wait</CardTitle>
                <Clock className="size-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{formatWaitTime(queueStatistics.longestWait)}</div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Available Agents</CardTitle>
                <User className="size-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{queueStatistics.availableAgents}/{queueStatistics.availableAgents + queueStatistics.busyAgents}</div>
              </CardContent>
            </Card>
          </div>

          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Queue Position</TableHead>
                <TableHead>Call ID</TableHead>
                <TableHead>Wait Time</TableHead>
                <TableHead>Product</TableHead>
                <TableHead>Priority</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {liveCallQueueData.map((call) => (
                <TableRow key={call.id}>
                  <TableCell>{call.position}</TableCell>
                  <TableCell>{call.id}</TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <Clock className="size-4 text-muted-foreground" />
                      {formatWaitTime(call.waitTime)}
                    </div>
                  </TableCell>
                  <TableCell>{call.product}</TableCell>
                  <TableCell>
                    <Badge variant={getPriorityVariant(call.priority) as any}>{call.priority}</Badge>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>

          <div className="mt-6">
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <div className="text-sm font-medium">Queue Load</div>
                <div className="text-sm text-muted-foreground">
                  {Math.round((queueStatistics.totalCalls / (queueStatistics.availableAgents * 2)) * 100)}%
                </div>
              </div>
              <Progress 
                value={Math.round((queueStatistics.totalCalls / (queueStatistics.availableAgents * 2)) * 100)} 
                className="h-2" 
              />
              <div className="text-xs text-muted-foreground">
                Based on available agents and current call volume
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Personal Performance Summary */}
      <Card>
        <CardHeader>
          <CardTitle>Your Performance Today</CardTitle>
          <CardDescription>Summary of your call handling metrics</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <div className="text-sm font-medium">Calls Handled</div>
              <div className="text-2xl font-bold">24</div>
              <div className="text-xs text-muted-foreground">+3 from yesterday</div>
            </div>
            <div className="space-y-2">
              <div className="text-sm font-medium">Avg. Handle Time</div>
              <div className="text-2xl font-bold">4:35</div>
              <div className="text-xs text-muted-foreground">-0:15 from yesterday</div>
            </div>
            <div className="space-y-2">
              <div className="text-sm font-medium">Customer Satisfaction</div>
              <div className="text-2xl font-bold">92%</div>
              <div className="text-xs text-muted-foreground">+2% from yesterday</div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
