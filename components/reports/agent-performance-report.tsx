"use client"

import { useTheme } from "next-themes"
import { <PERSON>, <PERSON><PERSON><PERSON>, CartesianGrid, Cell, Pie, <PERSON><PERSON><PERSON>, ResponsiveContainer, Tooltip, XAxis, YAxis } from "recharts"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"

const agentPerformanceData = [
  { name: "<PERSON>", ticketsResolved: 42, callsHandled: 156, avgCallTime: 210, satisfaction: 98 },
  { name: "<PERSON>", ticketsResolved: 38, callsHandled: 142, avgCallTime: 195, satisfaction: 95 },
  { name: "<PERSON><PERSON>", ticketsResolved: 35, callsHandled: 128, avgCallTime: 225, satisfaction: 97 },
  { name: "<PERSON>", ticketsResolved: 33, callsHandled: 120, avgCallTime: 240, satisfaction: 94 },
  { name: "<PERSON>", ticketsResolved: 30, callsHandled: 115, avgCallTime: 180, satisfaction: 96 },
  { name: "<PERSON>", ticketsResolved: 28, callsHandled: 105, avgCallTime: 195, satisfaction: 93 },
  { name: "Priya S.", ticketsResolved: 25, callsHandled: 98, avgCallTime: 210, satisfaction: 92 },
  { name: "Robert M.", ticketsResolved: 22, callsHandled: 85, avgCallTime: 240, satisfaction: 90 },
]

const satisfactionData = [
  { name: "Very Satisfied", value: 65 },
  { name: "Satisfied", value: 25 },
  { name: "Neutral", value: 7 },
  { name: "Dissatisfied", value: 3 },
]

const COLORS = ["#10b981", "#3b82f6", "#f59e0b", "#ef4444"]

export function AgentPerformanceReport() {
  const { theme } = useTheme()
  const isDark = theme === "dark"

  return (
    <div className="space-y-4">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-base">Tickets Resolved by Agent</CardTitle>
            <CardDescription>Number of tickets resolved this month</CardDescription>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart
                data={agentPerformanceData}
                layout="vertical"
                margin={{ top: 10, right: 10, left: 70, bottom: 0 }}
              >
                <CartesianGrid strokeDasharray="3 3" stroke={isDark ? "#333" : "#eee"} />
                <XAxis
                  type="number"
                  stroke={isDark ? "#888" : "#888"}
                  fontSize={12}
                  tickLine={false}
                  axisLine={false}
                />
                <YAxis
                  type="category"
                  dataKey="name"
                  stroke={isDark ? "#888" : "#888"}
                  fontSize={12}
                  tickLine={false}
                  axisLine={false}
                />
                <Tooltip
                  contentStyle={{
                    backgroundColor: isDark ? "#1f2937" : "#fff",
                    border: `1px solid ${isDark ? "#374151" : "#e5e7eb"}`,
                    borderRadius: "6px",
                    color: isDark ? "#e5e7eb" : "#1f2937",
                  }}
                />
                <Bar dataKey="ticketsResolved" fill="#3b82f6" radius={[0, 4, 4, 0]} />
              </BarChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-base">Customer Satisfaction</CardTitle>
            <CardDescription>Overall satisfaction ratings</CardDescription>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={satisfactionData}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  outerRadius={100}
                  fill="#8884d8"
                  dataKey="value"
                  label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                >
                  {satisfactionData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                  ))}
                </Pie>
                <Tooltip
                  contentStyle={{
                    backgroundColor: isDark ? "#1f2937" : "#fff",
                    border: `1px solid ${isDark ? "#374151" : "#e5e7eb"}`,
                    borderRadius: "6px",
                    color: isDark ? "#e5e7eb" : "#1f2937",
                  }}
                />
              </PieChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Agent Performance Details</CardTitle>
          <CardDescription>Detailed performance metrics for all agents</CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Agent Name</TableHead>
                <TableHead>Tickets Resolved</TableHead>
                <TableHead>Calls Handled</TableHead>
                <TableHead>Avg Call Time</TableHead>
                <TableHead>Satisfaction</TableHead>
                <TableHead>Performance</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {agentPerformanceData.map((agent) => (
                <TableRow key={agent.name}>
                  <TableCell className="font-medium">{agent.name}</TableCell>
                  <TableCell>{agent.ticketsResolved}</TableCell>
                  <TableCell>{agent.callsHandled}</TableCell>
                  <TableCell>{agent.avgCallTime}s</TableCell>
                  <TableCell>{agent.satisfaction}%</TableCell>
                  <TableCell>
                    <PerformanceBadge score={agent.satisfaction} />
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  )
}

function PerformanceBadge({ score }: { score: number }) {
  let variant = "secondary"
  let label = "Average"

  if (score >= 95) {
    variant = "success"
    label = "Excellent"
  } else if (score >= 90) {
    variant = "default"
    label = "Good"
  } else if (score < 85) {
    variant = "destructive"
    label = "Needs Improvement"
  }

  return <Badge variant={variant as any}>{label}</Badge>
}

