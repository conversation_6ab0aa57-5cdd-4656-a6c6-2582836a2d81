"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { useTheme } from "next-themes"
import { <PERSON><PERSON><PERSON>, <PERSON>, Cell, ResponsiveContainer, <PERSON><PERSON><PERSON>, Legend } from "recharts"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { DatePickerWithRange } from "@/components/ui/date-range-picker"
import { addDays } from "date-fns"

// Sample data for FCR
const initialData = [
  { name: "Resolved First Contact", value: 78, color: "#22c55e" },
  { name: "Escalated", value: 22, color: "#ef4444" },
]

export function FirstContactResolutionChart() {
  const router = useRouter()
  const { theme } = useTheme()
  const isDark = theme === "dark"
  const [data, setData] = useState(initialData)
  const [date, setDate] = useState({
    from: addDays(new Date(), -30),
    to: new Date(),
  })

  // Handle date range change
  const handleDateChange = (newDate: { from: Date; to: Date | undefined }) => {
    if (newDate.from && newDate.to) {
      setDate(newDate)
      // In a real app, you would fetch new data based on the date range
      console.log("Fetching FCR data for date range:", newDate)
      
      // Simulate data change for demo purposes
      const daysInRange = Math.floor((newDate.to.getTime() - newDate.from.getTime()) / (1000 * 60 * 60 * 24))
      const randomFactor = daysInRange > 15 ? 0.8 : 0.9
      
      setData([
        { name: "Resolved First Contact", value: Math.floor(70 + Math.random() * 15), color: "#22c55e" },
        { name: "Escalated", value: Math.floor(15 + Math.random() * 15), color: "#ef4444" },
      ])
    }
  }

  // Handle pie segment click
  const handlePieClick = (data: any, index: number) => {
    // Redirect to tickets page with appropriate filter
    if (index === 0) {
      // Resolved on first contact
      router.push("/dashboard/tickets?status=resolved&escalated=false")
    } else {
      // Escalated tickets
      router.push("/dashboard/tickets?escalated=true")
    }
  }

  // Custom tooltip
  const CustomTooltip = ({ active, payload }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-card border rounded-md shadow-sm p-2 text-sm">
          <p className="font-medium">{payload[0].name}</p>
          <p className="text-sm">
            <span className="font-medium">{payload[0].value}%</span> of tickets
          </p>
          <p className="text-xs text-muted-foreground mt-1">Click to view tickets</p>
        </div>
      );
    }
    return null;
  };

  // Calculate total
  const total = data.reduce((sum, item) => sum + item.value, 0)

  return (
    <Card>
      <CardHeader>
        <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
          <div>
            <CardTitle>First Contact Resolution</CardTitle>
            <CardDescription>Percentage of tickets resolved on first contact</CardDescription>
          </div>
          <DatePickerWithRange date={date} setDate={handleDateChange} />
        </div>
      </CardHeader>
      <CardContent>
        <div className="h-[300px] flex flex-col items-center justify-center">
          <ResponsiveContainer width="100%" height="100%">
            <PieChart>
              <Pie
                data={data}
                cx="50%"
                cy="50%"
                innerRadius={60}
                outerRadius={100}
                paddingAngle={2}
                dataKey="value"
                onClick={handlePieClick}
                cursor="pointer"
                label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                labelLine={false}
              >
                {data.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={entry.color} />
                ))}
              </Pie>
              <Tooltip content={<CustomTooltip />} />
              <Legend 
                verticalAlign="bottom" 
                height={36} 
                iconType="circle"
                iconSize={8}
                wrapperStyle={{ paddingTop: "10px" }}
              />
            </PieChart>
          </ResponsiveContainer>
          <div className="text-center mt-4">
            <p className="text-2xl font-bold">{data[0].value}%</p>
            <p className="text-sm text-muted-foreground">First Contact Resolution Rate</p>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
