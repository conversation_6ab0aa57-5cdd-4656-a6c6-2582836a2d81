"use client"

import { useTheme } from "next-themes"
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  CartesianGrid,
  Legend,
  Line,
  <PERSON>Chart,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  YAxis,
} from "recharts"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Ta<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs"

const dailyData = [
  { name: "May 1", inbound: 65, outbound: 48, abandoned: 5, avgDuration: 180 },
  { name: "May 2", inbound: 59, outbound: 38, abandoned: 3, avgDuration: 195 },
  { name: "May 3", inbound: 80, outbound: 43, abandoned: 8, avgDuration: 170 },
  { name: "May 4", inbound: 81, outbound: 55, abandoned: 7, avgDuration: 185 },
  { name: "May 5", inbound: 56, outbound: 36, abandoned: 4, avgDuration: 210 },
  { name: "May 6", inbound: 55, outbound: 27, abandoned: 3, avgDuration: 200 },
  { name: "May 7", inbound: 40, outbound: 32, abandoned: 2, avgDuration: 190 },
  { name: "May 8", inbound: 75, outbound: 61, abandoned: 6, avgDuration: 175 },
  { name: "May 9", inbound: 82, outbound: 55, abandoned: 9, avgDuration: 165 },
  { name: "May 10", inbound: 72, outbound: 60, abandoned: 5, avgDuration: 180 },
  { name: "May 11", inbound: 88, outbound: 68, abandoned: 7, avgDuration: 195 },
  { name: "May 12", inbound: 74, outbound: 52, abandoned: 6, avgDuration: 185 },
  { name: "May 13", inbound: 67, outbound: 45, abandoned: 4, avgDuration: 200 },
  { name: "May 14", inbound: 89, outbound: 71, abandoned: 8, avgDuration: 175 },
]

const hourlyData = [
  { name: "8 AM", inbound: 15, outbound: 8, abandoned: 1, avgDuration: 170 },
  { name: "9 AM", inbound: 25, outbound: 12, abandoned: 2, avgDuration: 180 },
  { name: "10 AM", inbound: 35, outbound: 18, abandoned: 3, avgDuration: 190 },
  { name: "11 AM", inbound: 40, outbound: 22, abandoned: 4, avgDuration: 185 },
  { name: "12 PM", inbound: 30, outbound: 15, abandoned: 2, avgDuration: 175 },
  { name: "1 PM", inbound: 25, outbound: 12, abandoned: 1, avgDuration: 165 },
  { name: "2 PM", inbound: 35, outbound: 18, abandoned: 3, avgDuration: 180 },
  { name: "3 PM", inbound: 45, outbound: 25, abandoned: 4, avgDuration: 195 },
  { name: "4 PM", inbound: 40, outbound: 20, abandoned: 3, avgDuration: 190 },
  { name: "5 PM", inbound: 30, outbound: 15, abandoned: 2, avgDuration: 185 },
]

const weeklyData = [
  { name: "Week 1", inbound: 350, outbound: 250, abandoned: 25, avgDuration: 185 },
  { name: "Week 2", inbound: 400, outbound: 300, abandoned: 30, avgDuration: 190 },
  { name: "Week 3", inbound: 450, outbound: 320, abandoned: 35, avgDuration: 180 },
  { name: "Week 4", inbound: 420, outbound: 310, abandoned: 32, avgDuration: 195 },
]

export function CallVolumeReport() {
  const { theme } = useTheme()
  const isDark = theme === "dark"

  return (
    <Tabs defaultValue="daily" className="space-y-4">
      <TabsList>
        <TabsTrigger value="daily">Daily</TabsTrigger>
        <TabsTrigger value="hourly">Hourly</TabsTrigger>
        <TabsTrigger value="weekly">Weekly</TabsTrigger>
      </TabsList>

      <TabsContent value="daily" className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-base">Call Volume by Type</CardTitle>
              <CardDescription>Daily inbound vs outbound calls</CardDescription>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <BarChart data={dailyData} margin={{ top: 10, right: 10, left: 0, bottom: 0 }}>
                  <CartesianGrid strokeDasharray="3 3" stroke={isDark ? "#333" : "#eee"} />
                  <XAxis
                    dataKey="name"
                    stroke={isDark ? "#888" : "#888"}
                    fontSize={12}
                    tickLine={false}
                    axisLine={false}
                  />
                  <YAxis stroke={isDark ? "#888" : "#888"} fontSize={12} tickLine={false} axisLine={false} />
                  <Tooltip
                    contentStyle={{
                      backgroundColor: isDark ? "#1f2937" : "#fff",
                      border: `1px solid ${isDark ? "#374151" : "#e5e7eb"}`,
                      borderRadius: "6px",
                      color: isDark ? "#e5e7eb" : "#1f2937",
                    }}
                  />
                  <Legend />
                  <Bar dataKey="inbound" fill="#3b82f6" radius={[4, 4, 0, 0]} name="Inbound" />
                  <Bar dataKey="outbound" fill="#10b981" radius={[4, 4, 0, 0]} name="Outbound" />
                </BarChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-base">Call Duration & Abandoned</CardTitle>
              <CardDescription>Average call duration and abandoned calls</CardDescription>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <LineChart data={dailyData} margin={{ top: 10, right: 10, left: 0, bottom: 0 }}>
                  <CartesianGrid strokeDasharray="3 3" stroke={isDark ? "#333" : "#eee"} />
                  <XAxis
                    dataKey="name"
                    stroke={isDark ? "#888" : "#888"}
                    fontSize={12}
                    tickLine={false}
                    axisLine={false}
                  />
                  <YAxis
                    yAxisId="left"
                    stroke={isDark ? "#888" : "#888"}
                    fontSize={12}
                    tickLine={false}
                    axisLine={false}
                  />
                  <YAxis
                    yAxisId="right"
                    orientation="right"
                    stroke={isDark ? "#888" : "#888"}
                    fontSize={12}
                    tickLine={false}
                    axisLine={false}
                  />
                  <Tooltip
                    contentStyle={{
                      backgroundColor: isDark ? "#1f2937" : "#fff",
                      border: `1px solid ${isDark ? "#374151" : "#e5e7eb"}`,
                      borderRadius: "6px",
                      color: isDark ? "#e5e7eb" : "#1f2937",
                    }}
                  />
                  <Legend />
                  <Line
                    yAxisId="left"
                    type="monotone"
                    dataKey="avgDuration"
                    stroke="#f59e0b"
                    name="Avg Duration (sec)"
                    strokeWidth={2}
                    dot={{ r: 3 }}
                    activeDot={{ r: 5 }}
                  />
                  <Line
                    yAxisId="right"
                    type="monotone"
                    dataKey="abandoned"
                    stroke="#ef4444"
                    name="Abandoned Calls"
                    strokeWidth={2}
                    dot={{ r: 3 }}
                    activeDot={{ r: 5 }}
                  />
                </LineChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </div>
      </TabsContent>

      <TabsContent value="hourly" className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-base">Hourly Call Volume</CardTitle>
              <CardDescription>Call distribution throughout the day</CardDescription>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <BarChart data={hourlyData} margin={{ top: 10, right: 10, left: 0, bottom: 0 }}>
                  <CartesianGrid strokeDasharray="3 3" stroke={isDark ? "#333" : "#eee"} />
                  <XAxis
                    dataKey="name"
                    stroke={isDark ? "#888" : "#888"}
                    fontSize={12}
                    tickLine={false}
                    axisLine={false}
                  />
                  <YAxis stroke={isDark ? "#888" : "#888"} fontSize={12} tickLine={false} axisLine={false} />
                  <Tooltip
                    contentStyle={{
                      backgroundColor: isDark ? "#1f2937" : "#fff",
                      border: `1px solid ${isDark ? "#374151" : "#e5e7eb"}`,
                      borderRadius: "6px",
                      color: isDark ? "#e5e7eb" : "#1f2937",
                    }}
                  />
                  <Legend />
                  <Bar dataKey="inbound" fill="#3b82f6" radius={[4, 4, 0, 0]} name="Inbound" />
                  <Bar dataKey="outbound" fill="#10b981" radius={[4, 4, 0, 0]} name="Outbound" />
                </BarChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-base">Hourly Metrics</CardTitle>
              <CardDescription>Call duration and abandoned by hour</CardDescription>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <LineChart data={hourlyData} margin={{ top: 10, right: 10, left: 0, bottom: 0 }}>
                  <CartesianGrid strokeDasharray="3 3" stroke={isDark ? "#333" : "#eee"} />
                  <XAxis
                    dataKey="name"
                    stroke={isDark ? "#888" : "#888"}
                    fontSize={12}
                    tickLine={false}
                    axisLine={false}
                  />
                  <YAxis
                    yAxisId="left"
                    stroke={isDark ? "#888" : "#888"}
                    fontSize={12}
                    tickLine={false}
                    axisLine={false}
                  />
                  <YAxis
                    yAxisId="right"
                    orientation="right"
                    stroke={isDark ? "#888" : "#888"}
                    fontSize={12}
                    tickLine={false}
                    axisLine={false}
                  />
                  <Tooltip
                    contentStyle={{
                      backgroundColor: isDark ? "#1f2937" : "#fff",
                      border: `1px solid ${isDark ? "#374151" : "#e5e7eb"}`,
                      borderRadius: "6px",
                      color: isDark ? "#e5e7eb" : "#1f2937",
                    }}
                  />
                  <Legend />
                  <Line
                    yAxisId="left"
                    type="monotone"
                    dataKey="avgDuration"
                    stroke="#f59e0b"
                    name="Avg Duration (sec)"
                    strokeWidth={2}
                    dot={{ r: 3 }}
                    activeDot={{ r: 5 }}
                  />
                  <Line
                    yAxisId="right"
                    type="monotone"
                    dataKey="abandoned"
                    stroke="#ef4444"
                    name="Abandoned Calls"
                    strokeWidth={2}
                    dot={{ r: 3 }}
                    activeDot={{ r: 5 }}
                  />
                </LineChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </div>
      </TabsContent>

      <TabsContent value="weekly" className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-base">Weekly Call Volume</CardTitle>
              <CardDescription>Call trends by week</CardDescription>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <BarChart data={weeklyData} margin={{ top: 10, right: 10, left: 0, bottom: 0 }}>
                  <CartesianGrid strokeDasharray="3 3" stroke={isDark ? "#333" : "#eee"} />
                  <XAxis
                    dataKey="name"
                    stroke={isDark ? "#888" : "#888"}
                    fontSize={12}
                    tickLine={false}
                    axisLine={false}
                  />
                  <YAxis stroke={isDark ? "#888" : "#888"} fontSize={12} tickLine={false} axisLine={false} />
                  <Tooltip
                    contentStyle={{
                      backgroundColor: isDark ? "#1f2937" : "#fff",
                      border: `1px solid ${isDark ? "#374151" : "#e5e7eb"}`,
                      borderRadius: "6px",
                      color: isDark ? "#e5e7eb" : "#1f2937",
                    }}
                  />
                  <Legend />
                  <Bar dataKey="inbound" fill="#3b82f6" radius={[4, 4, 0, 0]} name="Inbound" />
                  <Bar dataKey="outbound" fill="#10b981" radius={[4, 4, 0, 0]} name="Outbound" />
                </BarChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-base">Weekly Metrics</CardTitle>
              <CardDescription>Call duration and abandoned by week</CardDescription>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <LineChart data={weeklyData} margin={{ top: 10, right: 10, left: 0, bottom: 0 }}>
                  <CartesianGrid strokeDasharray="3 3" stroke={isDark ? "#333" : "#eee"} />
                  <XAxis
                    dataKey="name"
                    stroke={isDark ? "#888" : "#888"}
                    fontSize={12}
                    tickLine={false}
                    axisLine={false}
                  />
                  <YAxis
                    yAxisId="left"
                    stroke={isDark ? "#888" : "#888"}
                    fontSize={12}
                    tickLine={false}
                    axisLine={false}
                  />
                  <YAxis
                    yAxisId="right"
                    orientation="right"
                    stroke={isDark ? "#888" : "#888"}
                    fontSize={12}
                    tickLine={false}
                    axisLine={false}
                  />
                  <Tooltip
                    contentStyle={{
                      backgroundColor: isDark ? "#1f2937" : "#fff",
                      border: `1px solid ${isDark ? "#374151" : "#e5e7eb"}`,
                      borderRadius: "6px",
                      color: isDark ? "#e5e7eb" : "#1f2937",
                    }}
                  />
                  <Legend />
                  <Line
                    yAxisId="left"
                    type="monotone"
                    dataKey="avgDuration"
                    stroke="#f59e0b"
                    name="Avg Duration (sec)"
                    strokeWidth={2}
                    dot={{ r: 3 }}
                    activeDot={{ r: 5 }}
                  />
                  <Line
                    yAxisId="right"
                    type="monotone"
                    dataKey="abandoned"
                    stroke="#ef4444"
                    name="Abandoned Calls"
                    strokeWidth={2}
                    dot={{ r: 3 }}
                    activeDot={{ r: 5 }}
                  />
                </LineChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </div>
      </TabsContent>
    </Tabs>
  )
}

