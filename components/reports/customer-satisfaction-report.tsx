"use client"

import { useTheme } from "next-themes"
import { Bar, <PERSON><PERSON>hart, CartesianGrid, Cell, Pie, <PERSON><PERSON>, ResponsiveContainer, Tooltip, XAxis, YAxis } from "recharts"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"

const satisfactionTrendData = [
  { name: "<PERSON>", satisfaction: 92 },
  { name: "Feb", satisfaction: 93 },
  { name: "<PERSON>", satisfaction: 91 },
  { name: "Apr", satisfaction: 94 },
  { name: "May", satisfaction: 95 },
]

const satisfactionByServiceData = [
  { name: "Technical Support", satisfaction: 92 },
  { name: "Billing Support", satisfaction: 88 },
  { name: "Account Management", satisfaction: 95 },
  { name: "Product Inquiries", satisfaction: 90 },
  { name: "<PERSON><PERSON><PERSON><PERSON>", satisfaction: 82 },
]

const feedbackData = [
  { name: "Very Satisfied", value: 65 },
  { name: "Satisfied", value: 25 },
  { name: "Neutral", value: 7 },
  { name: "Dissatisfied", value: 3 },
]

const COLORS = ["#10b981", "#3b82f6", "#f59e0b", "#ef4444"]

const recentFeedback = [
  {
    id: 1,
    customer: "Jane Smith",
    date: "2023-05-15",
    rating: 5,
    comment: "Agent was very helpful and resolved my issue quickly.",
    category: "Technical Support",
  },
  {
    id: 2,
    customer: "John Doe",
    date: "2023-05-14",
    rating: 4,
    comment: "Good service but took a bit longer than expected.",
    category: "Billing Support",
  },
  {
    id: 3,
    customer: "Michael Johnson",
    date: "2023-05-15",
    rating: 5,
    comment: "Excellent service! The agent was knowledgeable and patient.",
    category: "Account Management",
  },
  {
    id: 4,
    customer: "Sarah Williams",
    date: "2023-05-13",
    rating: 3,
    comment: "Average service. Had to explain my issue multiple times.",
    category: "Product Inquiries",
  },
  {
    id: 5,
    customer: "Robert Brown",
    date: "2023-05-14",
    rating: 2,
    comment: "Long wait time and issue still not fully resolved.",
    category: "Complaints",
  },
]

export function CustomerSatisfactionReport() {
  const { theme } = useTheme()
  const isDark = theme === "dark"

  return (
    <div className="space-y-4">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-base">Satisfaction Trend</CardTitle>
            <CardDescription>Monthly customer satisfaction scores</CardDescription>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={satisfactionTrendData} margin={{ top: 10, right: 10, left: 0, bottom: 0 }}>
                <CartesianGrid strokeDasharray="3 3" stroke={isDark ? "#333" : "#eee"} />
                <XAxis
                  dataKey="name"
                  stroke={isDark ? "#888" : "#888"}
                  fontSize={12}
                  tickLine={false}
                  axisLine={false}
                />
                <YAxis
                  stroke={isDark ? "#888" : "#888"}
                  fontSize={12}
                  tickLine={false}
                  axisLine={false}
                  domain={[80, 100]}
                />
                <Tooltip
                  contentStyle={{
                    backgroundColor: isDark ? "#1f2937" : "#fff",
                    border: `1px solid ${isDark ? "#374151" : "#e5e7eb"}`,
                    borderRadius: "6px",
                    color: isDark ? "#e5e7eb" : "#1f2937",
                  }}
                  formatter={(value) => [`${value}%`, "Satisfaction"]}
                />
                <Bar dataKey="satisfaction" fill="#10b981" radius={[4, 4, 0, 0]} />
              </BarChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-base">Feedback Distribution</CardTitle>
            <CardDescription>Customer feedback by category</CardDescription>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={feedbackData}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  outerRadius={100}
                  fill="#8884d8"
                  dataKey="value"
                  label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                >
                  {feedbackData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                  ))}
                </Pie>
                <Tooltip
                  contentStyle={{
                    backgroundColor: isDark ? "#1f2937" : "#fff",
                    border: `1px solid ${isDark ? "#374151" : "#e5e7eb"}`,
                    borderRadius: "6px",
                    color: isDark ? "#e5e7eb" : "#1f2937",
                  }}
                />
              </PieChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Satisfaction by Service Type</CardTitle>
          <CardDescription>Customer satisfaction scores by service category</CardDescription>
        </CardHeader>
        <CardContent>
          <ResponsiveContainer width="100%" height={300}>
            <BarChart
              data={satisfactionByServiceData}
              layout="vertical"
              margin={{ top: 10, right: 10, left: 120, bottom: 0 }}
            >
              <CartesianGrid strokeDasharray="3 3" stroke={isDark ? "#333" : "#eee"} />
              <XAxis
                type="number"
                stroke={isDark ? "#888" : "#888"}
                fontSize={12}
                tickLine={false}
                axisLine={false}
                domain={[80, 100]}
              />
              <YAxis
                type="category"
                dataKey="name"
                stroke={isDark ? "#888" : "#888"}
                fontSize={12}
                tickLine={false}
                axisLine={false}
              />
              <Tooltip
                contentStyle={{
                  backgroundColor: isDark ? "#1f2937" : "#fff",
                  border: `1px solid ${isDark ? "#374151" : "#e5e7eb"}`,
                  borderRadius: "6px",
                  color: isDark ? "#e5e7eb" : "#1f2937",
                }}
                formatter={(value) => [`${value}%`, "Satisfaction"]}
              />
              <Bar dataKey="satisfaction" fill="#3b82f6" radius={[0, 4, 4, 0]} />
            </BarChart>
          </ResponsiveContainer>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Recent Customer Feedback</CardTitle>
          <CardDescription>Latest feedback from customers</CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Customer</TableHead>
                <TableHead>Category</TableHead>
                <TableHead>Rating</TableHead>
                <TableHead>Date</TableHead>
                <TableHead>Comment</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {recentFeedback.map((feedback) => (
                <TableRow key={feedback.id}>
                  <TableCell className="font-medium">{feedback.customer}</TableCell>
                  <TableCell>{feedback.category}</TableCell>
                  <TableCell>
                    <RatingBadge rating={feedback.rating} />
                  </TableCell>
                  <TableCell>{formatDate(feedback.date)}</TableCell>
                  <TableCell className="max-w-[300px] truncate">{feedback.comment}</TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  )
}

function RatingBadge({ rating }: { rating: number }) {
  let variant = "secondary"
  const label = `${rating} ★`

  if (rating >= 5) {
    variant = "success"
  } else if (rating >= 4) {
    variant = "default"
  } else if (rating >= 3) {
    variant = "warning"
  } else {
    variant = "destructive"
  }

  return <Badge variant={variant as any}>{label}</Badge>
}

function formatDate(dateString: string) {
  const date = new Date(dateString)
  return new Intl.DateTimeFormat("en-US", {
    month: "short",
    day: "numeric",
    year: "numeric",
  }).format(date)
}

