"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, <PERSON><PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card";
import { ApiErrorDisplay, ErrorBoundary, LoadingErrorState } from "@/components/error";
import { useErrorHandling } from "@/lib/hooks/useErrorHandling";
import { useApiErrorHandler } from "@/lib/hooks/useApiErrorHandler";

// Simulated API calls that will fail
const simulateNetworkError = () => {
  throw new Error("Network error: Failed to fetch data from server");
};

const simulatePermissionError = () => {
  const error = new Error("You don't have permission to access this resource");
  (error as any).status = 403;
  throw error;
};

const simulateNotFoundError = () => {
  const error = new Error("Resource not found");
  (error as any).status = 404;
  throw error;
};

const simulateServerError = () => {
  const error = new Error("Internal server error");
  (error as any).status = 500;
  throw error;
};

export function ErrorHandlingExample() {
  const { error, handleError, clearError } = useErrorHandling();
  const { 
    error: apiError, 
    handleApiError, 
    clearError: clearApiError 
  } = useApiErrorHandler();
  
  const [showLoadingError, setShowLoadingError] = useState(false);
  
  return (
    <div className="space-y-8 p-6">
      <Card>
        <CardHeader>
          <CardTitle>Error Handling Examples</CardTitle>
          <CardDescription>
            Examples of different error handling components and techniques
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Global Error Modal Example */}
          <div className="space-y-2">
            <h3 className="text-lg font-medium">Global Error Modal</h3>
            <p className="text-sm text-muted-foreground">
              This will trigger the global error modal that appears on top of the page
            </p>
            <div className="flex gap-2">
              <Button 
                variant="outline" 
                onClick={() => handleError(new Error("This is a global error message"))}
              >
                Show Global Error
              </Button>
            </div>
          </div>
          
          {/* API Error Display Example */}
          <div className="space-y-2">
            <h3 className="text-lg font-medium">API Error Display</h3>
            <p className="text-sm text-muted-foreground">
              Different types of API errors displayed inline
            </p>
            <div className="flex flex-wrap gap-2">
              <Button 
                variant="outline" 
                onClick={() => {
                  try {
                    simulateNetworkError();
                  } catch (error) {
                    handleApiError(error);
                  }
                }}
              >
                Network Error
              </Button>
              <Button 
                variant="outline" 
                onClick={() => {
                  try {
                    simulatePermissionError();
                  } catch (error) {
                    handleApiError(error);
                  }
                }}
              >
                Permission Error
              </Button>
              <Button 
                variant="outline" 
                onClick={() => {
                  try {
                    simulateNotFoundError();
                  } catch (error) {
                    handleApiError(error);
                  }
                }}
              >
                Not Found Error
              </Button>
              <Button 
                variant="outline" 
                onClick={() => {
                  try {
                    simulateServerError();
                  } catch (error) {
                    handleApiError(error);
                  }
                }}
              >
                Server Error
              </Button>
            </div>
            
            {apiError && (
              <div className="mt-4">
                <ApiErrorDisplay 
                  error={apiError} 
                  onRetry={clearApiError} 
                />
              </div>
            )}
          </div>
          
          {/* Loading Error State Example */}
          <div className="space-y-2">
            <h3 className="text-lg font-medium">Loading Error State</h3>
            <p className="text-sm text-muted-foreground">
              Error state for when data fails to load
            </p>
            <div className="flex gap-2">
              <Button 
                variant="outline" 
                onClick={() => setShowLoadingError(true)}
              >
                Show Loading Error
              </Button>
              {showLoadingError && (
                <Button 
                  variant="outline" 
                  onClick={() => setShowLoadingError(false)}
                >
                  Hide Loading Error
                </Button>
              )}
            </div>
            
            {showLoadingError && (
              <div className="mt-4">
                <LoadingErrorState 
                  title="Failed to load users" 
                  description="There was a problem loading the user list. Please try again."
                  error={new Error("Network request failed: timeout")}
                  onRetry={() => setShowLoadingError(false)}
                />
              </div>
            )}
          </div>
          
          {/* Error Boundary Example */}
          <div className="space-y-2">
            <h3 className="text-lg font-medium">Error Boundary</h3>
            <p className="text-sm text-muted-foreground">
              Component that catches errors in its children
            </p>
            <ErrorBoundary>
              <div className="p-4 border rounded-md">
                <p className="mb-2">This component is wrapped in an ErrorBoundary</p>
                <Button 
                  variant="outline" 
                  onClick={() => {
                    throw new Error("Error thrown from inside ErrorBoundary");
                  }}
                >
                  Throw Error
                </Button>
              </div>
            </ErrorBoundary>
          </div>
        </CardContent>
        <CardFooter>
          <p className="text-sm text-muted-foreground">
            Use these components to provide better error feedback to users
          </p>
        </CardFooter>
      </Card>
    </div>
  );
}
