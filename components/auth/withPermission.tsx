"use client";

import { useRouter } from "next/navigation";
import { useEffect } from "react";
import { useAuth } from "@/lib/hooks";
import { hasPermission, PERMISSION_CODES } from "@/lib/utils/roles";
import { UserRole } from "@/lib/api/types";

export function withPermission(
  WrappedComponent: React.ComponentType,
  requiredPermission: string
) {
  return function PermissionWrapper(props: any) {
    const router = useRouter();
    const { user } = useAuth();
    
    console.log('WithPermission HOC:', {
      user,
      userRole: user?.role?.name,
      requiredPermission
    });
    
    // Allow Platform Owner to access everything
    if (user?.role?.name === 'Platform Owner') {
      console.log('Platform Owner access granted');
      return <WrappedComponent {...props} />;
    }
    
    useEffect(() => {
      if (!user?.role) {
        console.log('No role found, redirecting to dashboard');
        router.push("/dashboard");
        return;
      }
      
      // Allow Platform Owner to access everything
      if (user.role.name === 'Platform Owner') {
        console.log('Platform Owner access granted in useEffect');
        return;
      }
      
      if (!hasPermission(user.role, requiredPermission as keyof typeof PERMISSION_CODES)) {
        console.log('Permission denied, redirecting to dashboard');
        router.push("/dashboard");
      }
    }, [user, router]);

    return <WrappedComponent {...props} />;
  };
}
