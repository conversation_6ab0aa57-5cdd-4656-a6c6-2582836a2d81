"use client";

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useAuth } from '@/lib/hooks/useAuth';
import { callMetricsService } from '@/lib/services/call-metrics-service';
import { toast } from '@/components/ui/use-toast';
import { Trash2, BarChart3, RefreshCw } from 'lucide-react';

/**
 * Development component for generating sample call metrics data
 * This should only be visible in development or for testing purposes
 */
export function CallMetricsGenerator() {
  const { user } = useAuth();
  const [numberOfCalls, setNumberOfCalls] = useState(10);
  const [isGenerating, setIsGenerating] = useState(false);

  const handleGenerateSampleData = async () => {
    if (!user?.id) {
      toast({
        title: 'Error',
        description: 'User not authenticated',
        variant: 'destructive',
      });
      return;
    }

    setIsGenerating(true);
    try {
      callMetricsService.generateSampleData(user.id, numberOfCalls);
      toast({
        title: 'Success',
        description: `Generated ${numberOfCalls} sample call metrics`,
      });
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to generate sample data',
        variant: 'destructive',
      });
    } finally {
      setIsGenerating(false);
    }
  };

  const handleClearAllMetrics = () => {
    try {
      callMetricsService.clearAllMetrics();
      toast({
        title: 'Success',
        description: 'All call metrics cleared',
      });
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to clear metrics',
        variant: 'destructive',
      });
    }
  };

  const handleClearUserMetrics = () => {
    if (!user?.id) return;
    
    try {
      callMetricsService.clearAgentMetrics(user.id);
      toast({
        title: 'Success',
        description: 'Your call metrics cleared',
      });
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to clear your metrics',
        variant: 'destructive',
      });
    }
  };

  // Only show in development environment
  if (process.env.NODE_ENV === 'production') {
    return null;
  }

  return (
    <Card className="border-dashed border-orange-200 bg-orange-50/50">
      <CardHeader>
        <CardTitle className="flex items-center gap-2 text-orange-700">
          <BarChart3 className="h-5 w-5" />
          Call Metrics Generator (Development)
        </CardTitle>
        <CardDescription className="text-orange-600">
          Generate sample call data for testing dashboard metrics and role-based access
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="space-y-2">
            <Label htmlFor="numberOfCalls">Number of Calls</Label>
            <Input
              id="numberOfCalls"
              type="number"
              min="1"
              max="100"
              value={numberOfCalls}
              onChange={(e) => setNumberOfCalls(parseInt(e.target.value) || 10)}
              className="w-full"
            />
          </div>
          
          <div className="flex items-end">
            <Button 
              onClick={handleGenerateSampleData}
              disabled={isGenerating || !user?.id}
              className="w-full"
            >
              {isGenerating ? (
                <>
                  <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                  Generating...
                </>
              ) : (
                <>
                  <BarChart3 className="h-4 w-4 mr-2" />
                  Generate Sample Data
                </>
              )}
            </Button>
          </div>

          <div className="flex items-end gap-2">
            <Button 
              variant="outline"
              onClick={handleClearUserMetrics}
              disabled={!user?.id}
              className="flex-1"
            >
              <Trash2 className="h-4 w-4 mr-2" />
              Clear My Data
            </Button>
            <Button 
              variant="destructive"
              onClick={handleClearAllMetrics}
              className="flex-1"
            >
              <Trash2 className="h-4 w-4 mr-2" />
              Clear All
            </Button>
          </div>
        </div>

        <div className="text-xs text-orange-600 bg-orange-100 p-3 rounded-md">
          <strong>Note:</strong> This component is only visible in development mode. 
          Generated data will be stored in browser localStorage and used to populate 
          dashboard metrics. Use "Clear My Data" to remove your personal metrics or 
          "Clear All" to reset all stored metrics.
        </div>
      </CardContent>
    </Card>
  );
}
