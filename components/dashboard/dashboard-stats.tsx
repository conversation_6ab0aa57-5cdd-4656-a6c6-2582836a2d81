import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { ArrowUpRight, CheckCircle2, Clock, PhoneCall, Users, Target, TrendingUp } from "lucide-react"
import { useDashboardPermissions } from "@/lib/hooks/useDashboardPermissions"
import { useCallMetrics } from "@/lib/stores/call-metrics-store"
import { useAuth } from "@/lib/hooks/useAuth"

export function DashboardStats() {
  const permissions = useDashboardPermissions();
  const { user } = useAuth();
  const callMetricsStore = useCallMetrics();

  // Get metrics based on user permissions with fallback
  const metrics = callMetricsStore.getDashboardSummary ?
    callMetricsStore.getDashboardSummary(
      permissions.dataScope === 'own' ? user?.id : undefined
    ) : {
      totalCallsToday: 0,
      totalCallsThisWeek: 0,
      totalCallsThisMonth: 0,
      averageHandleTime: 0,
      resolutionRate: 0,
      customerSatisfaction: 0,
      escalationRate: 0,
      answeredCalls: 0,
      missedCalls: 0,
    };

  // Format time in hours and minutes
  const formatTime = (seconds: number): string => {
    if (seconds < 60) return `${seconds}s`;
    const minutes = Math.floor(seconds / 60);
    if (minutes < 60) return `${minutes}m`;
    const hours = Math.floor(minutes / 60);
    const remainingMinutes = minutes % 60;
    return remainingMinutes > 0 ? `${hours}h ${remainingMinutes}m` : `${hours}h`;
  };

  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
      {/* Calls Today - Always visible */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">
            {permissions.dataScope === 'own' ? 'My Calls Today' : 'Total Calls Today'}
          </CardTitle>
          <PhoneCall className="size-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{metrics.totalCallsToday}</div>
          {permissions.showSensitiveMetrics && (
            <p className="text-xs text-muted-foreground mt-1">
              {metrics.answeredCalls} answered, {metrics.missedCalls} missed
            </p>
          )}
        </CardContent>
      </Card>

      {/* Active Agents - Only for supervisors and above */}
      {permissions.canViewSystemStats ? (
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Agents</CardTitle>
            <Users className="size-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">18/24</div>
            <p className="text-xs text-muted-foreground mt-1">75% of agents online</p>
          </CardContent>
        </Card>
      ) : (
        // For agents, show their performance target instead
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Daily Target</CardTitle>
            <Target className="size-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{Math.round((metrics.totalCallsToday / 40) * 100)}%</div>
            <p className="text-xs text-muted-foreground mt-1">{metrics.totalCallsToday}/40 calls</p>
          </CardContent>
        </Card>
      )}

      {/* Average Handle Time - Always visible but different context */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">
            {permissions.dataScope === 'own' ? 'My Avg. Handle Time' : 'Avg. Handle Time'}
          </CardTitle>
          <Clock className="size-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{formatTime(metrics.averageHandleTime)}</div>
          {permissions.dataScope === 'own' && (
            <p className="text-xs text-muted-foreground mt-1">Target: 5m</p>
          )}
        </CardContent>
      </Card>

      {/* Resolution Rate - Always visible */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">
            {permissions.dataScope === 'own' ? 'My Resolution Rate' : 'Resolution Rate'}
          </CardTitle>
          <CheckCircle2 className="size-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{metrics.resolutionRate}%</div>
          {permissions.showSensitiveMetrics && (
            <p className="text-xs text-muted-foreground mt-1">
              {metrics.escalationRate}% escalated
            </p>
          )}
        </CardContent>
      </Card>
    </div>
  )
}

