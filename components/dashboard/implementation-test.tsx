"use client";

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { useAuth } from '@/lib/hooks/useAuth';
import { useDashboardPermissions } from '@/lib/hooks/useDashboardPermissions';
import { useCallMetrics } from '@/lib/stores/call-metrics-store';
import { callMetricsService } from '@/lib/services/call-metrics-service';
import { CheckCircle2, XCircle, AlertTriangle, TestTube, User, Shield, BarChart3 } from 'lucide-react';

interface TestResult {
  name: string;
  status: 'pass' | 'fail' | 'warning';
  message: string;
  details?: string;
}

export function ImplementationTest() {
  const { user } = useAuth();
  const permissions = useDashboardPermissions();
  const callMetricsStore = useCallMetrics();
  const [testResults, setTestResults] = useState<TestResult[]>([]);
  const [isRunning, setIsRunning] = useState(false);

  const runTests = async () => {
    setIsRunning(true);
    const results: TestResult[] = [];

    // Test 1: User Authentication
    results.push({
      name: 'User Authentication',
      status: user ? 'pass' : 'fail',
      message: user ? `Authenticated as ${user.email}` : 'User not authenticated',
      details: user ? `Role: ${user.role?.name}, Permission Level: ${user.role?.permission_level}` : undefined
    });

    // Test 2: Role-Based Permissions
    const roleTests = [
      { name: 'Can View Own Metrics', value: permissions.canViewOwnMetrics },
      { name: 'Can View Team Metrics', value: permissions.canViewTeamMetrics },
      { name: 'Can View System Metrics', value: permissions.canViewSystemMetrics },
      { name: 'Can View Agent Performance', value: permissions.canViewAllAgentPerformance },
      { name: 'Can View Advanced Analytics', value: permissions.canViewAdvancedAnalytics },
    ];

    roleTests.forEach(test => {
      results.push({
        name: `Permission: ${test.name}`,
        status: 'pass',
        message: test.value ? 'Granted' : 'Denied',
        details: `Data Scope: ${permissions.dataScope}, Max Agents: ${permissions.maxAgentsToShow}`
      });
    });

    // Test 3: Call Metrics Storage
    const metricsCount = callMetricsStore.callMetrics.length;
    results.push({
      name: 'Call Metrics Storage',
      status: metricsCount > 0 ? 'pass' : 'warning',
      message: `${metricsCount} call metrics stored`,
      details: metricsCount === 0 ? 'No call metrics found. Generate sample data to test.' : undefined
    });

    // Test 4: Dashboard Data Integration
    const dashboardSummary = callMetricsStore.getDashboardSummary ?
      callMetricsStore.getDashboardSummary(
        permissions.dataScope === 'own' ? user?.id : undefined
      ) : {
        totalCallsToday: 0,
        resolutionRate: 0,
        averageHandleTime: 0
      };
    
    results.push({
      name: 'Dashboard Data Integration',
      status: dashboardSummary.totalCallsToday >= 0 ? 'pass' : 'fail',
      message: `Dashboard showing ${dashboardSummary.totalCallsToday} calls today`,
      details: `Resolution Rate: ${dashboardSummary.resolutionRate}%, Handle Time: ${dashboardSummary.averageHandleTime}s`
    });

    // Test 5: Role-Based Data Filtering
    const userMetrics = user?.id ? callMetricsService.getAgentMetrics(user.id) : null;
    const systemMetrics = callMetricsService.getSystemMetrics();
    
    if (permissions.dataScope === 'own') {
      results.push({
        name: 'Agent Data Filtering',
        status: userMetrics ? 'pass' : 'warning',
        message: 'Agent can only see own data',
        details: userMetrics ? `${userMetrics.callMetrics.length} personal call records` : 'No personal data found'
      });
    } else {
      results.push({
        name: 'Supervisor Data Access',
        status: systemMetrics ? 'pass' : 'warning',
        message: 'Supervisor can see system-wide data',
        details: `${systemMetrics.callMetrics.length} total call records across all agents`
      });
    }

    // Test 6: Local Storage Persistence
    try {
      const storedData = localStorage.getItem('call-metrics-storage');
      results.push({
        name: 'Local Storage Persistence',
        status: storedData ? 'pass' : 'warning',
        message: storedData ? 'Data persisted in localStorage' : 'No persisted data found',
        details: storedData ? `Storage size: ${(storedData.length / 1024).toFixed(2)} KB` : undefined
      });
    } catch (error) {
      results.push({
        name: 'Local Storage Persistence',
        status: 'fail',
        message: 'localStorage access failed',
        details: error instanceof Error ? error.message : 'Unknown error'
      });
    }

    // Test 7: Component Visibility
    const visibilityTests = [
      { name: 'System Stats', visible: permissions.canViewSystemStats },
      { name: 'Call Volume Analytics', visible: permissions.canViewCallVolumeAnalytics },
      { name: 'Agent Comparisons', visible: permissions.canViewAgentComparisons },
      { name: 'Advanced Analytics', visible: permissions.canViewAdvancedAnalytics },
    ];

    visibilityTests.forEach(test => {
      results.push({
        name: `Component Visibility: ${test.name}`,
        status: 'pass',
        message: test.visible ? 'Visible' : 'Hidden',
        details: `Based on role: ${user?.role?.name}`
      });
    });

    setTestResults(results);
    setIsRunning(false);
  };

  const getStatusIcon = (status: TestResult['status']) => {
    switch (status) {
      case 'pass':
        return <CheckCircle2 className="h-4 w-4 text-green-500" />;
      case 'fail':
        return <XCircle className="h-4 w-4 text-red-500" />;
      case 'warning':
        return <AlertTriangle className="h-4 w-4 text-yellow-500" />;
    }
  };

  const getStatusColor = (status: TestResult['status']) => {
    switch (status) {
      case 'pass':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'fail':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'warning':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
    }
  };

  const passCount = testResults.filter(r => r.status === 'pass').length;
  const failCount = testResults.filter(r => r.status === 'fail').length;
  const warningCount = testResults.filter(r => r.status === 'warning').length;

  // Only show in development
  if (process.env.NODE_ENV === 'production') {
    return null;
  }

  return (
    <Card className="border-dashed border-blue-200 bg-blue-50/50">
      <CardHeader>
        <CardTitle className="flex items-center gap-2 text-blue-700">
          <TestTube className="h-5 w-5" />
          Implementation Test Suite
        </CardTitle>
        <CardDescription className="text-blue-600">
          Validate call simulation data persistence and role-based dashboard functionality
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex items-center gap-4">
          <Button onClick={runTests} disabled={isRunning}>
            {isRunning ? 'Running Tests...' : 'Run Tests'}
          </Button>
          
          {testResults.length > 0 && (
            <div className="flex items-center gap-2">
              <Badge variant="outline" className="bg-green-100 text-green-800">
                {passCount} Passed
              </Badge>
              {warningCount > 0 && (
                <Badge variant="outline" className="bg-yellow-100 text-yellow-800">
                  {warningCount} Warnings
                </Badge>
              )}
              {failCount > 0 && (
                <Badge variant="outline" className="bg-red-100 text-red-800">
                  {failCount} Failed
                </Badge>
              )}
            </div>
          )}
        </div>

        {testResults.length > 0 && (
          <div className="space-y-3">
            <Separator />
            <div className="grid gap-2">
              {testResults.map((result, index) => (
                <div
                  key={index}
                  className={`p-3 rounded-md border ${getStatusColor(result.status)}`}
                >
                  <div className="flex items-center gap-2">
                    {getStatusIcon(result.status)}
                    <span className="font-medium">{result.name}</span>
                    <span className="text-sm">- {result.message}</span>
                  </div>
                  {result.details && (
                    <div className="text-xs mt-1 opacity-75">
                      {result.details}
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>
        )}

        <div className="text-xs text-blue-600 bg-blue-100 p-3 rounded-md">
          <strong>Test Coverage:</strong> User authentication, role-based permissions, 
          call metrics storage, dashboard data integration, data filtering, localStorage 
          persistence, and component visibility based on user roles.
        </div>
      </CardContent>
    </Card>
  );
}
