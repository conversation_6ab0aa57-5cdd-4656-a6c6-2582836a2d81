"use client";

import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { useAuth } from '@/lib/hooks/useAuth';
import { useDashboardPermissions, shouldShowComponent } from '@/lib/hooks/useDashboardPermissions';
import { Shield, Eye, EyeOff } from 'lucide-react';

export function PermissionTest() {
  const { user } = useAuth();
  const permissions = useDashboardPermissions();

  const componentTests = [
    { name: 'Call Volume Analytics', key: 'call-volume-analytics' },
    { name: 'Agent Performance', key: 'agent-performance' },
    { name: 'System Stats', key: 'system-stats' },
    { name: 'Agent Comparisons', key: 'agent-comparisons' },
    { name: 'Advanced Analytics', key: 'advanced-analytics' },
    { name: 'Drill Down Analytics', key: 'drill-down-analytics' },
    { name: 'Trends Analytics', key: 'trends-analytics' },
    { name: 'Targets Analytics', key: 'targets-analytics' },
  ];

  // Only show in development
  if (process.env.NODE_ENV === 'production') {
    return null;
  }

  return (
    <Card className="border-dashed border-green-200 bg-green-50/50">
      <CardHeader>
        <CardTitle className="flex items-center gap-2 text-green-700">
          <Shield className="h-5 w-5" />
          Role-Based Permission Test
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* User Info */}
        <div className="bg-green-100 p-3 rounded-md">
          <div className="text-sm font-medium text-green-800">Current User</div>
          <div className="text-xs text-green-700 mt-1">
            <strong>Email:</strong> {user?.email || 'Not logged in'}<br/>
            <strong>Role:</strong> {user?.role?.name || 'No role'}<br/>
            <strong>Permission Level:</strong> {user?.role?.permission_level || 'N/A'}<br/>
            <strong>Data Scope:</strong> {permissions.dataScope}
          </div>
        </div>

        {/* Permission Summary */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
          <div className="text-center">
            <Badge variant={permissions.canViewOwnMetrics ? "default" : "secondary"}>
              Own Metrics
            </Badge>
          </div>
          <div className="text-center">
            <Badge variant={permissions.canViewTeamMetrics ? "default" : "secondary"}>
              Team Metrics
            </Badge>
          </div>
          <div className="text-center">
            <Badge variant={permissions.canViewSystemMetrics ? "default" : "secondary"}>
              System Metrics
            </Badge>
          </div>
          <div className="text-center">
            <Badge variant={permissions.showSensitiveMetrics ? "default" : "secondary"}>
              Sensitive Data
            </Badge>
          </div>
        </div>

        {/* Component Visibility Tests */}
        <div className="space-y-2">
          <div className="text-sm font-medium text-green-800">Component Visibility</div>
          <div className="grid gap-2">
            {componentTests.map((test) => {
              const isVisible = shouldShowComponent(test.key, permissions);
              return (
                <div
                  key={test.key}
                  className={`flex items-center justify-between p-2 rounded text-xs ${
                    isVisible 
                      ? 'bg-green-200 text-green-800' 
                      : 'bg-gray-200 text-gray-600'
                  }`}
                >
                  <span>{test.name}</span>
                  <div className="flex items-center gap-1">
                    {isVisible ? (
                      <>
                        <Eye className="h-3 w-3" />
                        <span>Visible</span>
                      </>
                    ) : (
                      <>
                        <EyeOff className="h-3 w-3" />
                        <span>Hidden</span>
                      </>
                    )}
                  </div>
                </div>
              );
            })}
          </div>
        </div>

        {/* Expected Behavior */}
        <div className="text-xs text-green-600 bg-green-100 p-3 rounded-md">
          <strong>Expected for {user?.role?.name || 'Unknown Role'}:</strong>
          <br/>
          {user?.role?.permission_level === 2 ? (
            <>
              • Should see: Own metrics, personal performance, daily targets<br/>
              • Should NOT see: Call volume analytics, other agents' performance, system stats
            </>
          ) : user?.role?.permission_level && user.role.permission_level >= 3 ? (
            <>
              • Should see: All metrics, team performance, system analytics<br/>
              • Should have: Full dashboard access, agent comparisons, advanced features
            </>
          ) : (
            'Role not recognized or permission level unclear'
          )}
        </div>

        {/* Quick Role Check */}
        <div className="flex gap-2 text-xs">
          <Badge variant={user?.role?.permission_level === 2 ? "default" : "outline"}>
            Agent (Level 2)
          </Badge>
          <Badge variant={user?.role?.permission_level === 3 ? "default" : "outline"}>
            Supervisor (Level 3)
          </Badge>
          <Badge variant={user?.role?.permission_level && user.role.permission_level >= 5 ? "default" : "outline"}>
            Admin (Level 5+)
          </Badge>
          <Badge variant={user?.role?.permission_level === 10 ? "default" : "outline"}>
            Platform Owner (Level 10)
          </Badge>
        </div>
      </CardContent>
    </Card>
  );
}
