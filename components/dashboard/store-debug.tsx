"use client";

import { useEffect, useState } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { useCallMetrics, useCallMetricsStore, initializeCallMetricsStore } from '@/lib/stores/call-metrics-store';
import { useDashboardPermissions, shouldShowComponent } from '@/lib/hooks/useDashboardPermissions';
import { useAuth } from '@/lib/hooks/useAuth';
import { Bug, RefreshCw } from 'lucide-react';

export function StoreDebug() {
  const [debugInfo, setDebugInfo] = useState<any>(null);
  const callMetricsHook = useCallMetrics();
  const storeState = useCallMetricsStore();
  const { user } = useAuth();
  const permissions = useDashboardPermissions();

  const runDebug = () => {
    try {
      // Initialize store
      const initializedStore = initializeCallMetricsStore();
      
      // Get store state directly
      const directState = useCallMetricsStore.getState();
      
      // Test getDashboardSummary function
      let summaryResult = null;
      let summaryError = null;
      
      try {
        if (directState.getDashboardSummary) {
          summaryResult = directState.getDashboardSummary();
        } else {
          summaryError = 'getDashboardSummary function not found in store';
        }
      } catch (error) {
        summaryError = error instanceof Error ? error.message : 'Unknown error';
      }

      setDebugInfo({
        user: {
          id: user?.id,
          email: user?.email,
          role: user?.role?.name,
          permissionLevel: user?.role?.permission_level,
        },
        permissions: {
          dataScope: permissions.dataScope,
          canViewSystemStats: permissions.canViewSystemStats,
          canViewCallVolumeAnalytics: permissions.canViewCallVolumeAnalytics,
          canViewAllAgentPerformance: permissions.canViewAllAgentPerformance,
          canViewOwnMetrics: permissions.canViewOwnMetrics,
          showSensitiveMetrics: permissions.showSensitiveMetrics,
        },
        componentVisibility: {
          'call-volume-analytics': shouldShowComponent('call-volume-analytics', permissions),
          'agent-performance': shouldShowComponent('agent-performance', permissions),
          'system-stats': shouldShowComponent('system-stats', permissions),
        },
        hookAvailable: !!callMetricsHook,
        hookFunctions: Object.keys(callMetricsHook),
        storeState: {
          callMetricsLength: storeState.callMetrics?.length || 0,
          lastUpdated: storeState.lastUpdated,
          aggregatedMetrics: storeState.aggregatedMetrics,
        },
        directState: {
          callMetricsLength: directState.callMetrics?.length || 0,
          lastUpdated: directState.lastUpdated,
          hasDashboardSummary: !!directState.getDashboardSummary,
          functions: Object.keys(directState).filter(key => typeof directState[key] === 'function'),
        },
        summaryResult,
        summaryError,
        initializedStore: !!initializedStore,
      });
    } catch (error) {
      setDebugInfo({
        error: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : undefined,
      });
    }
  };

  useEffect(() => {
    runDebug();
  }, []);

  // Only show in development
  if (process.env.NODE_ENV === 'production') {
    return null;
  }

  return (
    <Card className="border-dashed border-purple-200 bg-purple-50/50">
      <CardHeader>
        <CardTitle className="flex items-center gap-2 text-purple-700">
          <Bug className="h-5 w-5" />
          Store Debug Information
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <Button onClick={runDebug} size="sm">
          <RefreshCw className="h-4 w-4 mr-2" />
          Refresh Debug Info
        </Button>
        
        {debugInfo && (
          <div className="bg-purple-100 p-4 rounded-md">
            <pre className="text-xs overflow-auto max-h-96">
              {JSON.stringify(debugInfo, null, 2)}
            </pre>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
