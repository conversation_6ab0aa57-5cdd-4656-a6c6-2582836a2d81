import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Progress } from "@/components/ui/progress"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Badge } from "@/components/ui/badge"

const agents = [
	{
		id: 1,
		name: "<PERSON>",
		avatar: "/placeholder.svg?height=40&width=40",
		initials: "S<PERSON>",
		ticketsResolved: 42,
		callsHandled: 78,
		satisfaction: 98,
		avgResponseTime: "1m 24s",
		avgHandleTime: 184, // in seconds
	},
	{
		id: 2,
		name: "<PERSON>",
		avatar: "/placeholder.svg?height=40&width=40",
		initials: "MC",
		ticketsResolved: 38,
		callsHandled: 65,
		satisfaction: 95,
		avgResponseTime: "1m 45s",
		avgHandleTime: 205, // in seconds
	},
	{
		id: 3,
		name: "<PERSON><PERSON> <PERSON>",
		avatar: "/placeholder.svg?height=40&width=40",
		initials: "AP",
		ticketsResolved: 35,
		callsHandled: 62,
		satisfaction: 97,
		avgResponseTime: "1m 30s",
		avgHandleTime: 190, // in seconds
	},
	{
		id: 4,
		name: "<PERSON>",
		avatar: "/placeholder.svg?height=40&width=40",
		initials: "DO",
		ticketsResolved: 33,
		callsHandled: 58,
		satisfaction: 94,
		avgResponseTime: "1m 50s",
		avgHandleTime: 210, // in seconds
	},
	{
		id: 5,
		name: "Emma Wilson",
		avatar: "/placeholder.svg?height=40&width=40",
		initials: "EW",
		ticketsResolved: 30,
		callsHandled: 52,
		satisfaction: 92,
		avgResponseTime: "2m 05s",
		avgHandleTime: 225, // in seconds
	},
	{
		id: 6,
		name: "Carlos Rodriguez",
		avatar: "/placeholder.svg?height=40&width=40",
		initials: "CR",
		ticketsResolved: 28,
		callsHandled: 49,
		satisfaction: 91,
		avgResponseTime: "2m 15s",
		avgHandleTime: 235, // in seconds
	},
	{
		id: 7,
		name: "Priya Sharma",
		avatar: "/placeholder.svg?height=40&width=40",
		initials: "PS",
		ticketsResolved: 25,
		callsHandled: 45,
		satisfaction: 90,
		avgResponseTime: "2m 20s",
		avgHandleTime: 240, // in seconds
	},
]

// Set the ticket target for 100% (can be dynamic or from settings)
const TICKET_TARGET = 120;

// Helper function to format handle time
const formatHandleTime = (seconds: number): string => {
	const minutes = Math.floor(seconds / 60)
	const remainingSeconds = seconds % 60
	return `${minutes}m ${remainingSeconds}s`
}

// Helper function to get handle time color based on duration
const getHandleTimeColor = (seconds: number): string => {
	if (seconds <= 180) return "bg-green-500" // Under 3 minutes
	if (seconds <= 300) return "bg-amber-500" // Under 5 minutes
	return "bg-red-500" // Over 5 minutes
}

export function AgentPerformance() {
	return (
		<ScrollArea className="h-[400px] pr-4">
			<div className="space-y-4">
				{agents.map((agent) => {
					const percent = Math.round((agent.ticketsResolved / TICKET_TARGET) * 100)
					return (
						<div key={agent.id} className="flex items-center gap-4">
							<Avatar>
								<AvatarImage src={agent.avatar} alt={agent.name} />
								<AvatarFallback>{agent.initials}</AvatarFallback>
							</Avatar>
							<div className="flex-1 space-y-2">
								<div className="flex items-center justify-between">
									<p className="text-sm font-medium leading-none">{agent.name}</p>
									<div className="flex items-center gap-2">
										<Badge variant="outline">{agent.callsHandled} calls</Badge>
										<Badge variant="secondary">{agent.ticketsResolved} tickets</Badge>
									</div>
								</div>

								{/* Ticket performance against target */}
								<div className="space-y-1">
									<div className="flex items-center justify-between text-xs">
										<span className="text-muted-foreground">Tickets Handled</span>
										<span className="font-medium">
											{agent.ticketsResolved} / {TICKET_TARGET} ({percent}%)
										</span>
									</div>
									<Progress value={percent} className="h-1.5" />
								</div>

								{/* Satisfaction score */}
								<div className="space-y-1">
									<div className="flex items-center justify-between text-xs">
										<span className="text-muted-foreground">Satisfaction</span>
										<span className="font-medium">{agent.satisfaction}%</span>
									</div>
									<Progress value={agent.satisfaction} className="h-1.5" />
								</div>

								{/* Handle time */}
								<div className="space-y-1">
									<div className="flex items-center justify-between text-xs">
										<span className="text-muted-foreground">Avg. Handle Time</span>
										<span className="font-medium">{formatHandleTime(agent.avgHandleTime)}</span>
									</div>
									<div className="h-1.5 w-full bg-muted rounded-full overflow-hidden">
										<div
											className={`h-full ${getHandleTimeColor(agent.avgHandleTime)}`}
											style={{
												width: `${Math.min(100, (agent.avgHandleTime / 300) * 100)}%`,
											}}
										/>
									</div>
								</div>

								<div className="flex items-center gap-2 text-xs text-muted-foreground">
									<span>{agent.avgResponseTime} avg. response</span>
								</div>
							</div>
						</div>
					)
				})}
			</div>
		</ScrollArea>
	)
}

