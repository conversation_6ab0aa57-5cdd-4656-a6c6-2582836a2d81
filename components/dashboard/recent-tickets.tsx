import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { MoreHorizontal } from "lucide-react"

const tickets = [
  {
    id: "T-1001",
    description: "Unable to access account",
    category: "Account",
    subcategory: "Login Issues",
    priority: "High",
    status: "Open",
    msisdn: "+************",
    userId: "U-1001",
    createdAt: "2023-05-15T09:24:00",
    customer: "<PERSON>",
  },
  {
    id: "T-1002",
    description: "Billing discrepancy on last invoice",
    category: "Billing",
    subcategory: "Invoice",
    priority: "Medium",
    status: "In Progress",
    msisdn: "+************",
    userId: "U-1002",
    createdAt: "2023-05-14T14:30:00",
    customer: "<PERSON>",
  },
  {
    id: "T-1003",
    description: "Service outage in Westlands area",
    category: "Network",
    subcategory: "Outage",
    priority: "Critical",
    status: "Open",
    msisdn: "+************",
    userId: "U-1003",
    createdAt: "2023-05-15T08:15:00",
    customer: "Michael Johnson",
  },
  {
    id: "T-1004",
    description: "Request for plan upgrade",
    category: "Service",
    subcategory: "Plan Change",
    priority: "Low",
    status: "Resolved",
    msisdn: "+************",
    userId: "U-1004",
    createdAt: "2023-05-13T11:45:00",
    customer: "Sarah Williams",
  },
  {
    id: "T-1005",
    description: "Payment not reflecting on account",
    category: "Billing",
    subcategory: "Payment",
    priority: "High",
    status: "In Progress",
    msisdn: "+************",
    userId: "U-1005",
    createdAt: "2023-05-14T16:20:00",
    customer: "Robert Brown",
  },
]

export function RecentTickets() {
  return (
    <div className="overflow-auto">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>ID</TableHead>
            <TableHead>Customer</TableHead>
            <TableHead>Description</TableHead>
            <TableHead>Category</TableHead>
            <TableHead>Priority</TableHead>
            <TableHead>Status</TableHead>
            <TableHead>Created</TableHead>
            <TableHead className="text-right">Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {tickets.map((ticket) => (
            <TableRow key={ticket.id}>
              <TableCell className="font-medium">{ticket.id}</TableCell>
              <TableCell>{ticket.customer}</TableCell>
              <TableCell>{ticket.description}</TableCell>
              <TableCell>{ticket.category}</TableCell>
              <TableCell>
                <PriorityBadge priority={ticket.priority} />
              </TableCell>
              <TableCell>
                <StatusBadge status={ticket.status} />
              </TableCell>
              <TableCell>{formatDate(ticket.createdAt)}</TableCell>
              <TableCell className="text-right">
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" className="h-8 w-8 p-0">
                      <span className="sr-only">Open menu</span>
                      <MoreHorizontal className="size-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuLabel>Actions</DropdownMenuLabel>
                    <DropdownMenuItem>View details</DropdownMenuItem>
                    <DropdownMenuItem>Assign ticket</DropdownMenuItem>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem>Update status</DropdownMenuItem>
                    <DropdownMenuItem>Add comment</DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  )
}

function PriorityBadge({ priority }: { priority: string }) {
  const getVariant = () => {
    switch (priority) {
      case "Critical":
        return "destructive"
      case "High":
        return "destructive"
      case "Medium":
        return "warning"
      case "Low":
        return "secondary"
      default:
        return "secondary"
    }
  }

  return <Badge variant={getVariant() as any}>{priority}</Badge>
}

function StatusBadge({ status }: { status: string }) {
  const getVariant = () => {
    switch (status) {
      case "Open":
        return "outline"
      case "In Progress":
        return "default"
      case "Resolved":
        return "success"
      default:
        return "secondary"
    }
  }

  return <Badge variant={getVariant() as any}>{status}</Badge>
}

function formatDate(dateString: string) {
  const date = new Date(dateString)
  return new Intl.DateTimeFormat("en-US", {
    month: "short",
    day: "numeric",
    hour: "2-digit",
    minute: "2-digit",
  }).format(date)
}

