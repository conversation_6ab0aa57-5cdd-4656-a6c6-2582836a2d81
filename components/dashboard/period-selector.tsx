"use client";

import React, { useState } from 'react';
import { Calendar, Clock, RefreshCw, Filter, X } from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { Calendar as CalendarComponent } from '@/components/ui/calendar';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { useAdvancedFilters, PERIOD_OPTIONS, formatPeriodLabel } from '@/lib/stores/filter-store';
import { FilterPeriod } from '@/lib/types/dashboard';
import { format } from 'date-fns';

interface PeriodSelectorProps {
  className?: string;
  showRefreshButton?: boolean;
  showAutoRefresh?: boolean;
}

export function PeriodSelector({ 
  className, 
  showRefreshButton = true, 
  showAutoRefresh = true 
}: PeriodSelectorProps) {
  const {
    filters,
    autoRefresh,
    refreshInterval,
    lastRefresh,
    isLoading,
    setPeriod,
    setCustomDateRange,
    setAutoRefresh,
    triggerRefresh,
    hasActiveFilters,
    getFilterSummary,
  } = useAdvancedFilters();

  const [isCustomDateOpen, setIsCustomDateOpen] = useState(false);
  const [customStartDate, setCustomStartDate] = useState<Date>();
  const [customEndDate, setCustomEndDate] = useState<Date>();

  const handlePeriodSelect = (period: FilterPeriod) => {
    setPeriod(period);
  };

  const handleCustomDateApply = () => {
    if (customStartDate && customEndDate) {
      setCustomDateRange(customStartDate, customEndDate);
      setIsCustomDateOpen(false);
      setCustomStartDate(undefined);
      setCustomEndDate(undefined);
    }
  };

  const handleAutoRefreshToggle = (enabled: boolean) => {
    setAutoRefresh(enabled, refreshInterval);
  };

  const formatLastRefresh = () => {
    if (!lastRefresh) return 'Never';
    return format(lastRefresh, 'HH:mm:ss');
  };

  return (
    <div className={`flex items-center gap-2 ${className}`}>
      {/* Period Selector */}
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="outline" className="flex items-center gap-2">
            <Calendar className="h-4 w-4" />
            <span>{formatPeriodLabel(filters.period)}</span>
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" className="w-[200px]">
          <DropdownMenuLabel>Select Period</DropdownMenuLabel>
          <DropdownMenuSeparator />
          
          {PERIOD_OPTIONS.map((period) => (
            <DropdownMenuItem
              key={period.type}
              onClick={() => handlePeriodSelect(period)}
              className="flex items-center justify-between"
            >
              <span>{period.label}</span>
              {filters.period.type === period.type && (
                <Badge variant="default" className="text-xs">
                  Active
                </Badge>
              )}
            </DropdownMenuItem>
          ))}
          
          <DropdownMenuSeparator />
          
          <Popover open={isCustomDateOpen} onOpenChange={setIsCustomDateOpen}>
            <PopoverTrigger asChild>
              <DropdownMenuItem onSelect={(e) => e.preventDefault()}>
                <Calendar className="mr-2 h-4 w-4" />
                Custom Range
              </DropdownMenuItem>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0" align="end">
              <div className="p-4 space-y-4">
                <div className="space-y-2">
                  <Label className="text-sm font-medium">Start Date</Label>
                  <CalendarComponent
                    mode="single"
                    selected={customStartDate}
                    onSelect={setCustomStartDate}
                    disabled={(date) => date > new Date()}
                  />
                </div>
                
                {customStartDate && (
                  <div className="space-y-2">
                    <Label className="text-sm font-medium">End Date</Label>
                    <CalendarComponent
                      mode="single"
                      selected={customEndDate}
                      onSelect={setCustomEndDate}
                      disabled={(date) => date < customStartDate || date > new Date()}
                    />
                  </div>
                )}
                
                <div className="flex gap-2">
                  <Button
                    size="sm"
                    onClick={handleCustomDateApply}
                    disabled={!customStartDate || !customEndDate}
                  >
                    Apply
                  </Button>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => setIsCustomDateOpen(false)}
                  >
                    Cancel
                  </Button>
                </div>
              </div>
            </PopoverContent>
          </Popover>
        </DropdownMenuContent>
      </DropdownMenu>

      {/* Active Filters Indicator */}
      {hasActiveFilters() && (
        <Badge variant="secondary" className="flex items-center gap-1">
          <Filter className="h-3 w-3" />
          {getFilterSummary()}
        </Badge>
      )}

      {/* Refresh Button */}
      {showRefreshButton && (
        <Button
          variant="outline"
          size="icon"
          onClick={triggerRefresh}
          disabled={isLoading}
          className="relative"
        >
          <RefreshCw className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
        </Button>
      )}

      {/* Auto Refresh Toggle */}
      {showAutoRefresh && (
        <div className="flex items-center gap-2 px-3 py-2 border rounded-md">
          <Switch
            id="auto-refresh"
            checked={autoRefresh}
            onCheckedChange={handleAutoRefreshToggle}
          />
          <Label htmlFor="auto-refresh" className="text-sm">
            Auto-refresh
          </Label>
          {autoRefresh && (
            <Badge variant="outline" className="text-xs">
              {refreshInterval}s
            </Badge>
          )}
        </div>
      )}

      {/* Last Refresh Indicator */}
      <div className="text-xs text-muted-foreground hidden lg:flex items-center gap-1">
        <Clock className="h-3 w-3" />
        Last: {formatLastRefresh()}
      </div>
    </div>
  );
}

// Compact version for mobile/smaller spaces
export function CompactPeriodSelector() {
  const { filters, triggerRefresh, isLoading } = useAdvancedFilters();

  return (
    <div className="flex items-center gap-2">
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="outline" size="sm">
            <Calendar className="h-4 w-4 mr-1" />
            {filters.period.type === 'custom' ? 'Custom' : filters.period.label}
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          {PERIOD_OPTIONS.map((period) => (
            <DropdownMenuItem key={period.type}>
              {period.label}
            </DropdownMenuItem>
          ))}
        </DropdownMenuContent>
      </DropdownMenu>

      <Button
        variant="outline"
        size="sm"
        onClick={triggerRefresh}
        disabled={isLoading}
      >
        <RefreshCw className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
      </Button>
    </div>
  );
}

// Filter summary component
export function FilterSummary() {
  const { filters, hasActiveFilters, getFilterSummary } = useAdvancedFilters();

  if (!hasActiveFilters()) return null;

  return (
    <div className="flex items-center gap-2 p-2 bg-muted rounded-md">
      <Filter className="h-4 w-4" />
      <span className="text-sm">Active filters: {getFilterSummary()}</span>
    </div>
  );
}