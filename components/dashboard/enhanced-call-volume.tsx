"use client";

import React, { useState, useEffect } from 'react';
import { useTheme } from "next-themes";
import { 
  Bar, 
  BarChart, 
  Line,
  LineChart,
  Area,
  AreaChart,
  CartesianGrid, 
  ResponsiveContainer, 
  Tooltip, 
  XAxis, 
  YAxis, 
  <PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  Cell
} from "recharts";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { 
  TrendingUp, 
  TrendingDown, 
  Phone, 
  PhoneCall, 
  PhoneOff,
  Download,
  Maximize2,
  BarChart3,
  <PERSON><PERSON>hart as LineChartIcon,
  <PERSON><PERSON><PERSON> as PieChartIcon
} from "lucide-react";
import { useAdvancedFilters } from '@/lib/stores/filter-store';
import { useWorkspace } from '@/lib/stores/workspace-store';
import { useCallMetrics } from '@/lib/stores/call-metrics-store';
import { useDashboardPermissions } from '@/lib/hooks/useDashboardPermissions';
import { useAuth } from '@/lib/hooks/useAuth';
import { CallVolumeData, CallAnalytics } from '@/lib/types/dashboard';

interface EnhancedCallVolumeProps {
  className?: string;
  showComparative?: boolean;
  allowDrillDown?: boolean;
}

// Mock data generator based on filters
const generateCallData = (period: string, workspaceId?: string): CallVolumeData[] => {
  const baseData = [
    { timestamp: "8 AM", inbound: 12, outbound: 8, missed: 3, abandoned: 2, totalDuration: 1440, averageWaitTime: 45 },
    { timestamp: "9 AM", inbound: 18, outbound: 12, missed: 5, abandoned: 3, totalDuration: 2160, averageWaitTime: 52 },
    { timestamp: "10 AM", inbound: 24, outbound: 15, missed: 4, abandoned: 2, totalDuration: 2880, averageWaitTime: 38 },
    { timestamp: "11 AM", inbound: 30, outbound: 18, missed: 6, abandoned: 4, totalDuration: 3600, averageWaitTime: 41 },
    { timestamp: "12 PM", inbound: 22, outbound: 14, missed: 3, abandoned: 2, totalDuration: 2640, averageWaitTime: 35 },
    { timestamp: "1 PM", inbound: 15, outbound: 10, missed: 2, abandoned: 1, totalDuration: 1800, averageWaitTime: 28 },
    { timestamp: "2 PM", inbound: 20, outbound: 16, missed: 4, abandoned: 3, totalDuration: 2400, averageWaitTime: 43 },
    { timestamp: "3 PM", inbound: 25, outbound: 20, missed: 5, abandoned: 3, totalDuration: 3000, averageWaitTime: 47 },
    { timestamp: "4 PM", inbound: 28, outbound: 22, missed: 7, abandoned: 5, totalDuration: 3360, averageWaitTime: 55 },
    { timestamp: "5 PM", inbound: 18, outbound: 15, missed: 3, abandoned: 2, totalDuration: 2160, averageWaitTime: 39 },
  ];

  // Apply workspace-specific multipliers based on product characteristics
  let multiplier = 1.0;
  if (workspaceId) {
    // Different products may have different call volumes
    const hash = workspaceId.split('').reduce((a, b) => {
      a = ((a << 5) - a) + b.charCodeAt(0);
      return a & a;
    }, 0);
    multiplier = 0.7 + (Math.abs(hash) % 100) / 100; // Range: 0.7 - 1.7
  }
  
  return baseData.map(item => ({
    ...item,
    inbound: Math.round(item.inbound * multiplier),
    outbound: Math.round(item.outbound * multiplier),
    missed: Math.round(item.missed * multiplier),
    abandoned: Math.round(item.abandoned * multiplier),
    totalDuration: Math.round(item.totalDuration * multiplier),
  }));
};

export function EnhancedCallVolume({ 
  className, 
  showComparative = true, 
  allowDrillDown = true 
}: EnhancedCallVolumeProps) {
  const { theme } = useTheme();
  const { filters } = useAdvancedFilters();
  const { currentWorkspace } = useWorkspace();
  const [chartType, setChartType] = useState<'bar' | 'line' | 'area'>('bar');
  const [selectedMetric, setSelectedMetric] = useState<'volume' | 'duration' | 'waitTime'>('volume');
  const [drillDownData, setDrillDownData] = useState<CallVolumeData[] | null>(null);

  const isDark = theme === "dark";
  const callData = generateCallData(filters.period.type, currentWorkspace?.id);

  // Calculate metrics
  const totalInbound = callData.reduce((sum, item) => sum + item.inbound, 0);
  const totalOutbound = callData.reduce((sum, item) => sum + item.outbound, 0);
  const totalMissed = callData.reduce((sum, item) => sum + item.missed, 0);
  const totalAbandoned = callData.reduce((sum, item) => sum + item.abandoned, 0);
  const averageWaitTime = callData.reduce((sum, item) => sum + item.averageWaitTime, 0) / callData.length;

  // Growth calculations (mock)
  const inboundGrowth = 12.5;
  const outboundGrowth = -3.2;
  const missedGrowth = -8.1;

  // Custom tooltip
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-card border rounded-md shadow-sm p-3 text-sm">
          <p className="font-medium mb-2">{label}</p>
          <div className="space-y-1">
            {payload.map((entry: any, index: number) => (
              <div key={index} className="flex items-center gap-2">
                <span 
                  className="w-3 h-3 rounded-full" 
                  style={{ backgroundColor: entry.color }}
                ></span>
                <span className="capitalize">{entry.dataKey}: {entry.value}</span>
              </div>
            ))}
          </div>
          {allowDrillDown && (
            <Button 
              size="sm" 
              variant="outline" 
              className="mt-2 text-xs"
              onClick={() => setDrillDownData(callData)}
            >
              View Details
            </Button>
          )}
        </div>
      );
    }
    return null;
  };

  // Chart component based on type
  const renderChart = () => {
    const chartProps = {
      data: callData,
      margin: { top: 10, right: 10, left: 0, bottom: 20 }
    };

    const commonElements = (
      <>
        <CartesianGrid strokeDasharray="3 3" stroke={isDark ? "#333" : "#eee"} />
        <XAxis 
          dataKey="timestamp" 
          stroke={isDark ? "#888" : "#888"} 
          fontSize={12} 
          tickLine={false} 
          axisLine={false} 
        />
        <YAxis 
          stroke={isDark ? "#888" : "#888"} 
          fontSize={12} 
          tickLine={false} 
          axisLine={false} 
        />
        <Tooltip content={<CustomTooltip />} />
        <Legend
          verticalAlign="bottom"
          height={36}
          iconType="circle"
          iconSize={8}
          wrapperStyle={{ paddingTop: "10px" }}
        />
      </>
    );

    switch (chartType) {
      case 'line':
        return (
          <LineChart {...chartProps}>
            {commonElements}
            <Line 
              type="monotone" 
              dataKey="inbound" 
              name="Inbound Calls" 
              stroke="#3b82f6" 
              strokeWidth={2}
              dot={{ r: 4 }}
            />
            <Line 
              type="monotone" 
              dataKey="outbound" 
              name="Outbound Calls" 
              stroke="#10b981" 
              strokeWidth={2}
              dot={{ r: 4 }}
            />
            <Line 
              type="monotone" 
              dataKey="missed" 
              name="Missed Calls" 
              stroke="#ef4444" 
              strokeWidth={2}
              dot={{ r: 4 }}
            />
          </LineChart>
        );
      
      case 'area':
        return (
          <AreaChart {...chartProps}>
            {commonElements}
            <Area 
              type="monotone" 
              dataKey="inbound" 
              name="Inbound Calls" 
              stackId="1"
              stroke="#3b82f6" 
              fill="#3b82f6"
              fillOpacity={0.6}
            />
            <Area 
              type="monotone" 
              dataKey="outbound" 
              name="Outbound Calls" 
              stackId="1"
              stroke="#10b981" 
              fill="#10b981"
              fillOpacity={0.6}
            />
            <Area 
              type="monotone" 
              dataKey="missed" 
              name="Missed Calls" 
              stackId="1"
              stroke="#ef4444" 
              fill="#ef4444"
              fillOpacity={0.6}
            />
          </AreaChart>
        );
      
      default:
        return (
          <BarChart {...chartProps}>
            {commonElements}
            <Bar dataKey="inbound" name="Inbound Calls" fill="#3b82f6" radius={[4, 4, 0, 0]} />
            <Bar dataKey="outbound" name="Outbound Calls" fill="#10b981" radius={[4, 4, 0, 0]} />
            <Bar dataKey="missed" name="Missed Calls" fill="#ef4444" radius={[4, 4, 0, 0]} />
          </BarChart>
        );
    }
  };

  return (
    <div className={className}>
      <Tabs defaultValue="overview" className="space-y-4">
        <div className="flex items-center justify-between">
          <TabsList>
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="inbound">Inbound</TabsTrigger>
            <TabsTrigger value="outbound">Outbound</TabsTrigger>
            <TabsTrigger value="analytics">Analytics</TabsTrigger>
          </TabsList>
          
          <div className="flex items-center gap-2">
            <div className="flex items-center gap-1 border rounded-md p-1">
              <Button
                variant={chartType === 'bar' ? 'default' : 'ghost'}
                size="sm"
                onClick={() => setChartType('bar')}
                className="h-7 w-7 p-0"
              >
                <BarChart3 className="h-3 w-3" />
              </Button>
              <Button
                variant={chartType === 'line' ? 'default' : 'ghost'}
                size="sm"
                onClick={() => setChartType('line')}
                className="h-7 w-7 p-0"
              >
                <LineChartIcon className="h-3 w-3" />
              </Button>
              <Button
                variant={chartType === 'area' ? 'default' : 'ghost'}
                size="sm"
                onClick={() => setChartType('area')}
                className="h-7 w-7 p-0"
              >
                <PieChartIcon className="h-3 w-3" />
              </Button>
            </div>
            
            <Button variant="outline" size="sm">
              <Download className="h-4 w-4 mr-1" />
              Export
            </Button>
          </div>
        </div>

        <TabsContent value="overview" className="space-y-4">
          {/* Summary Cards */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-muted-foreground">Inbound</p>
                    <p className="text-2xl font-bold">{totalInbound}</p>
                  </div>
                  <div className="flex items-center gap-1">
                    <Phone className="h-4 w-4 text-blue-500" />
                    {inboundGrowth > 0 ? (
                      <TrendingUp className="h-3 w-3 text-green-500" />
                    ) : (
                      <TrendingDown className="h-3 w-3 text-red-500" />
                    )}
                  </div>
                </div>
                <div className="flex items-center gap-1 mt-1">
                  <Badge variant={inboundGrowth > 0 ? "default" : "destructive"} className="text-xs">
                    {inboundGrowth > 0 ? '+' : ''}{inboundGrowth}%
                  </Badge>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-muted-foreground">Outbound</p>
                    <p className="text-2xl font-bold">{totalOutbound}</p>
                  </div>
                  <div className="flex items-center gap-1">
                    <PhoneCall className="h-4 w-4 text-green-500" />
                    {outboundGrowth > 0 ? (
                      <TrendingUp className="h-3 w-3 text-green-500" />
                    ) : (
                      <TrendingDown className="h-3 w-3 text-red-500" />
                    )}
                  </div>
                </div>
                <div className="flex items-center gap-1 mt-1">
                  <Badge variant={outboundGrowth > 0 ? "default" : "destructive"} className="text-xs">
                    {outboundGrowth > 0 ? '+' : ''}{outboundGrowth}%
                  </Badge>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-muted-foreground">Missed</p>
                    <p className="text-2xl font-bold">{totalMissed}</p>
                  </div>
                  <div className="flex items-center gap-1">
                    <PhoneOff className="h-4 w-4 text-red-500" />
                    {missedGrowth > 0 ? (
                      <TrendingUp className="h-3 w-3 text-red-500" />
                    ) : (
                      <TrendingDown className="h-3 w-3 text-green-500" />
                    )}
                  </div>
                </div>
                <div className="flex items-center gap-1 mt-1">
                  <Badge variant={missedGrowth < 0 ? "default" : "destructive"} className="text-xs">
                    {missedGrowth > 0 ? '+' : ''}{missedGrowth}%
                  </Badge>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-muted-foreground">Avg Wait</p>
                    <p className="text-2xl font-bold">{Math.round(averageWaitTime)}s</p>
                  </div>
                  <div className="flex items-center gap-1">
                    <Badge variant="outline" className="text-xs">
                      Target: 30s
                    </Badge>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Main Chart */}
          <Card>
            <CardHeader>
              <CardTitle>Call Volume Trends</CardTitle>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={350}>
                {renderChart()}
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="inbound">
          <Card>
            <CardHeader>
              <CardTitle>Inbound Call Analysis</CardTitle>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={350}>
                <AreaChart data={callData}>
                  <CartesianGrid strokeDasharray="3 3" stroke={isDark ? "#333" : "#eee"} />
                  <XAxis dataKey="timestamp" stroke={isDark ? "#888" : "#888"} fontSize={12} />
                  <YAxis stroke={isDark ? "#888" : "#888"} fontSize={12} />
                  <Tooltip content={<CustomTooltip />} />
                  <Area 
                    type="monotone" 
                    dataKey="inbound" 
                    name="Inbound Calls" 
                    stroke="#3b82f6" 
                    fill="#3b82f6"
                    fillOpacity={0.3}
                  />
                  <Area 
                    type="monotone" 
                    dataKey="missed" 
                    name="Missed Calls" 
                    stroke="#ef4444" 
                    fill="#ef4444"
                    fillOpacity={0.3}
                  />
                </AreaChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="outbound">
          <Card>
            <CardHeader>
              <CardTitle>Outbound Call Analysis</CardTitle>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={350}>
                <LineChart data={callData}>
                  <CartesianGrid strokeDasharray="3 3" stroke={isDark ? "#333" : "#eee"} />
                  <XAxis dataKey="timestamp" stroke={isDark ? "#888" : "#888"} fontSize={12} />
                  <YAxis stroke={isDark ? "#888" : "#888"} fontSize={12} />
                  <Tooltip content={<CustomTooltip />} />
                  <Line 
                    type="monotone" 
                    dataKey="outbound" 
                    name="Outbound Calls" 
                    stroke="#10b981" 
                    strokeWidth={3}
                    dot={{ r: 5 }}
                  />
                </LineChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="analytics">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle>Call Distribution</CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={250}>
                  <PieChart>
                    <Pie
                      data={[
                        { name: 'Inbound', value: totalInbound, fill: '#3b82f6' },
                        { name: 'Outbound', value: totalOutbound, fill: '#10b981' },
                        { name: 'Missed', value: totalMissed, fill: '#ef4444' },
                      ]}
                      cx="50%"
                      cy="50%"
                      outerRadius={80}
                      dataKey="value"
                      label={({ name, percent }) => `${name} ${percent ? (percent * 100).toFixed(0) : 0}%`}
                    />
                    <Tooltip />
                  </PieChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Performance Metrics</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex justify-between items-center">
                  <span className="text-sm">Answer Rate</span>
                  <Badge variant="default">
                    {((totalInbound - totalMissed) / totalInbound * 100).toFixed(1)}%
                  </Badge>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm">Abandonment Rate</span>
                  <Badge variant="destructive">
                    {(totalAbandoned / totalInbound * 100).toFixed(1)}%
                  </Badge>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm">Total Calls</span>
                  <Badge variant="outline">
                    {totalInbound + totalOutbound}
                  </Badge>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm">Peak Hour</span>
                  <Badge variant="secondary">
                    4 PM
                  </Badge>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}