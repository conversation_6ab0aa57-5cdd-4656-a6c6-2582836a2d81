"use client";

import React, { useState } from 'react';
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Progress } from "@/components/ui/progress";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Button } from "@/components/ui/button";
import { 
  TrendingUp, 
  TrendingDown, 
  Target, 
  Clock, 
  CheckCircle2, 
  AlertCircle,
  Trophy,
  Star,
  BarChart3
} from "lucide-react";
import { AgentPerformanceData, TicketResolutionMetrics } from '@/lib/types/dashboard';
import { useAdvancedFilters } from '@/lib/stores/filter-store';
import { useWorkspace } from '@/lib/stores/workspace-store';
import { useCallMetrics } from '@/lib/stores/call-metrics-store';
import { useDashboardPermissions } from '@/lib/hooks/useDashboardPermissions';
import { useAuth } from '@/lib/hooks/useAuth';
import { cn } from '@/lib/utils';

interface EnhancedAgentPerformanceProps {
  className?: string;
  showTrends?: boolean;
  maxAgents?: number;
}

// Mock agent performance data
const mockAgentData: AgentPerformanceData[] = [
  {
    agentId: '1',
    agentName: 'Sarah Johnson',
    avatar: '/placeholder.svg?height=40&width=40',
    ticketMetrics: {
      solvedTickets: 42,
      totalTickets: 120,
      averageResolutionTime: 125, // minutes
      firstContactResolution: 87.5, // percentage
      escalationRate: 8.3, // percentage
      customerSatisfaction: 4.8,
    },
    callMetrics: {
      totalCalls: 78,
      averageHandleTime: 184, // seconds
      callsPerHour: 6.5,
    },
    trends: {
      daily: [38, 42, 45, 41, 42],
      weekly: [280, 295, 310, 285, 294],
      monthly: [1200, 1250, 1180, 1320, 1294],
    },
    targets: {
      dailyTickets: 40,
      weeklyTickets: 280,
      monthlyTickets: 1200,
    },
  },
  {
    agentId: '2',
    agentName: 'Michael Chen',
    avatar: '/placeholder.svg?height=40&width=40',
    ticketMetrics: {
      solvedTickets: 38,
      totalTickets: 120,
      averageResolutionTime: 142,
      firstContactResolution: 84.4,
      escalationRate: 11.1,
      customerSatisfaction: 4.6,
    },
    callMetrics: {
      totalCalls: 65,
      averageHandleTime: 205,
      callsPerHour: 5.8,
    },
    trends: {
      daily: [35, 38, 40, 36, 38],
      weekly: [260, 275, 285, 270, 266],
      monthly: [1100, 1150, 1080, 1180, 1133],
    },
    targets: {
      dailyTickets: 35,
      weeklyTickets: 245,
      monthlyTickets: 1050,
    },
  },
  {
    agentId: '3',
    agentName: 'Aisha Patel',
    avatar: '/placeholder.svg?height=40&width=40',
    ticketMetrics: {
      solvedTickets: 100,
      totalTickets: 120,
      averageResolutionTime: 98,
      firstContactResolution: 92.5,
      escalationRate: 5.0,
      customerSatisfaction: 4.9,
    },
    callMetrics: {
      totalCalls: 62,
      averageHandleTime: 190,
      callsPerHour: 6.2,
    },
    trends: {
      daily: [32, 35, 37, 34, 35],
      weekly: [245, 260, 270, 255, 259],
      monthly: [1050, 1100, 1020, 1140, 1078],
    },
    targets: {
      dailyTickets: 32,
      weeklyTickets: 224,
      monthlyTickets: 960,
    },
  },
  {
    agentId: '4',
    agentName: 'David Ochieng',
    avatar: '/placeholder.svg?height=40&width=40',
    ticketMetrics: {
      solvedTickets: 33,
      totalTickets: 120,
      averageResolutionTime: 156,
      firstContactResolution: 79.5,
      escalationRate: 15.4,
      customerSatisfaction: 4.4,
    },
    callMetrics: {
      totalCalls: 58,
      averageHandleTime: 210,
      callsPerHour: 5.5,
    },
    trends: {
      daily: [30, 33, 35, 31, 33],
      weekly: [230, 245, 255, 240, 243],
      monthly: [980, 1030, 960, 1080, 1013],
    },
    targets: {
      dailyTickets: 30,
      weeklyTickets: 210,
      monthlyTickets: 900,
    },
  },
];

export function EnhancedAgentPerformance({ className, showTrends = true, maxAgents = 10 }: EnhancedAgentPerformanceProps) {
  const [sortBy, setSortBy] = useState<'tickets' | 'resolution' | 'satisfaction'>('tickets');
  const [viewMode, setViewMode] = useState<'list' | 'grid'>('list');
  const { filters } = useAdvancedFilters();
  const { currentWorkspace } = useWorkspace();
  const { user } = useAuth();
  const permissions = useDashboardPermissions();
  const { callMetrics, getAggregatedMetricsForAgent } = useCallMetrics();

  // Get real agent data based on permissions
  const getAgentData = (): AgentPerformanceData[] => {
    if (permissions.dataScope === 'own' && user?.id) {
      // For agents, show only their own data
      const userMetrics = getAggregatedMetricsForAgent(user.id);
      return [{
        agentId: user.id,
        agentName: `${user.first_name} ${user.last_name}`,
        avatar: '/placeholder.svg?height=40&width=40',
        ticketMetrics: {
          solvedTickets: Math.round(userMetrics.totalCalls * (userMetrics.resolutionRate / 100)),
          totalTickets: userMetrics.totalCalls,
          averageResolutionTime: userMetrics.averageHandleTime / 60, // Convert to minutes
          firstContactResolution: userMetrics.resolutionRate,
          escalationRate: userMetrics.escalationRate,
          customerSatisfaction: userMetrics.customerSatisfactionAverage,
        },
        callMetrics: {
          totalCalls: userMetrics.totalCalls,
          averageHandleTime: userMetrics.averageHandleTime,
          callsPerHour: userMetrics.totalCalls > 0 ? userMetrics.totalCalls / 8 : 0, // Assuming 8-hour workday
        },
        trends: {
          daily: [userMetrics.callsToday],
          weekly: [userMetrics.callsThisWeek],
          monthly: [userMetrics.callsThisMonth],
        },
        targets: {
          dailyTickets: 40,
          weeklyTickets: 280,
          monthlyTickets: 1200,
        },
      }];
    } else {
      // For supervisors and above, show team/system data
      // Group call metrics by agent
      const agentGroups = callMetrics.reduce((acc, metric) => {
        if (!acc[metric.agentId]) {
          acc[metric.agentId] = [];
        }
        acc[metric.agentId].push(metric);
        return acc;
      }, {} as Record<string, typeof callMetrics>);

      return Object.entries(agentGroups)
        .slice(0, maxAgents)
        .map(([agentId, metrics]) => {
          const agentMetrics = getAggregatedMetricsForAgent(agentId);
          const firstMetric = metrics[0];

          return {
            agentId,
            agentName: firstMetric?.callerName || `Agent ${agentId.slice(-4)}`,
            avatar: '/placeholder.svg?height=40&width=40',
            ticketMetrics: {
              solvedTickets: Math.round(agentMetrics.totalCalls * (agentMetrics.resolutionRate / 100)),
              totalTickets: agentMetrics.totalCalls,
              averageResolutionTime: agentMetrics.averageHandleTime / 60,
              firstContactResolution: agentMetrics.resolutionRate,
              escalationRate: agentMetrics.escalationRate,
              customerSatisfaction: agentMetrics.customerSatisfactionAverage,
            },
            callMetrics: {
              totalCalls: agentMetrics.totalCalls,
              averageHandleTime: agentMetrics.averageHandleTime,
              callsPerHour: agentMetrics.totalCalls > 0 ? agentMetrics.totalCalls / 8 : 0,
            },
            trends: {
              daily: [agentMetrics.callsToday],
              weekly: [agentMetrics.callsThisWeek],
              monthly: [agentMetrics.callsThisMonth],
            },
            targets: {
              dailyTickets: 40,
              weeklyTickets: 280,
              monthlyTickets: 1200,
            },
          };
        });
    }
  };

  const agentData = getAgentData();

  // Sort agents based on selected criteria
  const sortedAgents = [...agentData]
    .sort((a, b) => {
      switch (sortBy) {
        case 'tickets':
          return b.ticketMetrics.solvedTickets - a.ticketMetrics.solvedTickets;
        case 'resolution':
          return a.ticketMetrics.averageResolutionTime - b.ticketMetrics.averageResolutionTime;
        case 'satisfaction':
          return (b.ticketMetrics.customerSatisfaction || 0) - (a.ticketMetrics.customerSatisfaction || 0);
        default:
          return 0;
      }
    });

  // Helper functions
  const getPerformanceColor = (value: number, target: number, inverse = false) => {
    const ratio = value / target;
    if (inverse) {
      return ratio <= 0.8 ? 'text-green-500' : ratio <= 1.0 ? 'text-yellow-500' : 'text-red-500';
    }
    return ratio >= 1.2 ? 'text-green-500' : ratio >= 1.0 ? 'text-yellow-500' : 'text-red-500';
  };

  const getProgressColor = (value: number, target: number) => {
    const ratio = value / target;
    return ratio >= 1.2 ? 'bg-green-500' : ratio >= 1.0 ? 'bg-yellow-500' : 'bg-red-500';
  };

  const formatTime = (minutes: number): string => {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    return hours > 0 ? `${hours}h ${mins}m` : `${mins}m`;
  };

  const formatHandleTime = (seconds: number): string => {
    const minutes = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${minutes}m ${secs}s`;
  };

  const getTrendIcon = (current: number, previous: number) => {
    return current > previous ? (
      <TrendingUp className="h-3 w-3 text-green-500" />
    ) : (
      <TrendingDown className="h-3 w-3 text-red-500" />
    );
  };

  return (
    <div className={cn("space-y-4", className)}>
      <div className="flex items-center justify-between">
        <div className="space-x-4">
          <Button
            variant={sortBy === 'tickets' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setSortBy('tickets')}
          >
            Tickets
          </Button>
          <Button
            variant={sortBy === 'resolution' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setSortBy('resolution')}
          >
            Speed
          </Button>
          <Button
            variant={sortBy === 'satisfaction' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setSortBy('satisfaction')}
          >
            Rating
          </Button>
        </div>
      </div>

      <TabsContent value="performance">
        <ScrollArea className="h-[400px] pr-4">
          <div className="space-y-6">
            {sortedAgents.map((agent, index) => {
              const ticketProgress = (agent.ticketMetrics.solvedTickets / agent.targets.dailyTickets) * 100;
              const resolutionRate = (agent.ticketMetrics.solvedTickets / agent.ticketMetrics.totalTickets) * 100;
              
              return (
                <Card key={agent.agentId} className="transition-all hover:shadow-md">
                  <CardContent className="p-4">
                    <div className="flex flex-col items-start gap-6">
                      {/* Agent Info */}
                      <div className="flex items-center gap-4">
                        <div className="relative">
                          <Avatar className="h-14 w-14">
                            <AvatarImage src={agent.avatar} alt={agent.agentName} />
                            <AvatarFallback>
                              {agent.agentName.split(' ').map(n => n[0]).join('')}
                            </AvatarFallback>
                          </Avatar>
                          {index < 3 && (
                            <div className="absolute -top-1 -right-1">
                              <Badge variant="default" className="h-6 w-6 rounded-full p-0 flex items-center justify-center">
                                {index + 1}
                              </Badge>
                            </div>
                          )}
                        </div>
                        
                        <div className="space-y-1">
                          <h4 className="text-base font-medium">{agent.agentName}</h4>
                          <div className="flex items-center gap-3 text-sm text-muted-foreground">
                            <span>{agent.callMetrics.totalCalls} calls</span>
                            <span className="text-xs">•</span>
                            <span>{formatHandleTime(agent.callMetrics.averageHandleTime)} avg</span>
                          </div>
                        </div>
                      </div>

                      {/* Performance Metrics */}
                      <div className="flex-1 grid grid-cols-2 gap-8">
                        {/* Tickets Resolved */}
                        <div className="space-y-3">
                          <div className="flex items-center justify-between">
                            <span className="text-sm font-medium">Tickets Resolved</span>
                            <div className="flex items-center gap-3">
                              <CheckCircle2 className="h-5 w-5 text-green-500 ml-1" />
                              <span className="font-bold text-base">{agent.ticketMetrics.solvedTickets}</span>
                              <span className="text-muted-foreground">/{agent.ticketMetrics.totalTickets}</span>
                            </div>
                          </div>
                          <Progress value={resolutionRate} className="h-2" />
                          <div className="flex items-center justify-between text-xs">
                            <span className="text-muted-foreground">Resolution Rate</span>
                            <span className="font-medium">{resolutionRate.toFixed(1)}%</span>
                          </div>
                        </div>

                        {/* Resolution Time */}
                        <div className="space-y-3">
                          <div className="flex items-center justify-between">
                            <span className="text-sm font-medium">Resolution Time</span>
                            <div className="flex items-center gap-2">
                              <Clock className="h-5 w-5 text-blue-500" />
                              <span className="font-bold text-base">{formatTime(agent.ticketMetrics.averageResolutionTime)}</span>
                            </div>
                          </div>
                          <div className="flex items-center gap-2 mt-1">
                            <Badge 
                              variant={agent.ticketMetrics.averageResolutionTime <= 120 ? "default" : "destructive"}
                              className="text-xs"
                            >
                              {agent.ticketMetrics.averageResolutionTime <= 120 ? 'Fast' : 'Slow'}
                            </Badge>
                            <span className="text-xs text-muted-foreground">Target: 2h</span>
                          </div>
                        </div>

                      </div>
                    </div>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        </ScrollArea>
      </TabsContent>

      <TabsContent value="targets">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {sortedAgents.slice(0, 6).map((agent) => (
            <Card key={agent.agentId}>
              <CardHeader className="pb-3">
                <div className="flex items-center gap-2">
                  <Avatar className="h-8 w-8">
                    <AvatarImage src={agent.avatar} alt={agent.agentName} />
                    <AvatarFallback className="text-xs">
                      {agent.agentName.split(' ').map(n => n[0]).join('')}
                    </AvatarFallback>
                  </Avatar>
                  <CardTitle className="text-sm">{agent.agentName}</CardTitle>
                </div>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Daily Target</span>
                    <span className={getPerformanceColor(agent.ticketMetrics.solvedTickets, agent.targets.dailyTickets)}>
                      {agent.ticketMetrics.solvedTickets}/{agent.targets.dailyTickets}
                    </span>
                  </div>
                  <Progress 
                    value={(agent.ticketMetrics.solvedTickets / agent.targets.dailyTickets) * 100} 
                    className="h-2"
                  />
                </div>
                
                <div className="grid grid-cols-2 gap-2 text-xs">
                  <div className="text-center p-2 bg-muted rounded">
                    <div className="font-medium">Weekly</div>
                    <div className="text-muted-foreground">
                      {agent.trends.weekly[agent.trends.weekly.length - 1]}/{agent.targets.weeklyTickets}
                    </div>
                  </div>
                  <div className="text-center p-2 bg-muted rounded">
                    <div className="font-medium">Monthly</div>
                    <div className="text-muted-foreground">
                      {agent.trends.monthly[agent.trends.monthly.length - 1]}/{agent.targets.monthlyTickets}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </TabsContent>

      <TabsContent value="trends">
        <div className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-sm">Top Performer</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center gap-2">
                  <Trophy className="h-5 w-5 text-yellow-500" />
                  <div>
                    <div className="font-medium">{sortedAgents[0]?.agentName}</div>
                    <div className="text-sm text-muted-foreground">
                      {sortedAgents[0]?.ticketMetrics.solvedTickets} tickets resolved
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-sm">Fastest Resolution</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center gap-2">
                  <Clock className="h-5 w-5 text-blue-500" />
                  <div>
                    <div className="font-medium">
                      {[...sortedAgents].sort((a, b) => a.ticketMetrics.averageResolutionTime - b.ticketMetrics.averageResolutionTime)[0]?.agentName}
                    </div>
                    <div className="text-sm text-muted-foreground">
                      {formatTime([...sortedAgents].sort((a, b) => a.ticketMetrics.averageResolutionTime - b.ticketMetrics.averageResolutionTime)[0]?.ticketMetrics.averageResolutionTime || 0)} avg
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-sm">Highest Satisfaction</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center gap-2">
                  <Star className="h-5 w-5 text-yellow-500 fill-current" />
                  <div>
                    <div className="font-medium">
                      {[...sortedAgents].sort((a, b) => (b.ticketMetrics.customerSatisfaction || 0) - (a.ticketMetrics.customerSatisfaction || 0))[0]?.agentName}
                    </div>
                    <div className="text-sm text-muted-foreground">
                      {[...sortedAgents].sort((a, b) => (b.ticketMetrics.customerSatisfaction || 0) - (a.ticketMetrics.customerSatisfaction || 0))[0]?.ticketMetrics.customerSatisfaction?.toFixed(1)} rating
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>Team Performance Overview</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
                <div>
                  <div className="text-2xl font-bold">
                    {sortedAgents.reduce((sum, agent) => sum + agent.ticketMetrics.solvedTickets, 0)}
                  </div>
                  <div className="text-sm text-muted-foreground">Total Tickets Resolved</div>
                </div>
                <div>
                  <div className="text-2xl font-bold">
                    {(sortedAgents.reduce((sum, agent) => sum + agent.ticketMetrics.averageResolutionTime, 0) / sortedAgents.length).toFixed(0)}m
                  </div>
                  <div className="text-sm text-muted-foreground">Avg Resolution Time</div>
                </div>
                <div>
                  <div className="text-2xl font-bold">
                    {(sortedAgents.reduce((sum, agent) => sum + agent.ticketMetrics.firstContactResolution, 0) / sortedAgents.length).toFixed(1)}%
                  </div>
                  <div className="text-sm text-muted-foreground">First Contact Resolution</div>
                </div>
                <div>
                  <div className="text-2xl font-bold">
                    {(sortedAgents.reduce((sum, agent) => sum + (agent.ticketMetrics.customerSatisfaction || 0), 0) / sortedAgents.length).toFixed(1)}
                  </div>
                  <div className="text-sm text-muted-foreground">Avg Satisfaction</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </TabsContent>
    </div>
  );
}