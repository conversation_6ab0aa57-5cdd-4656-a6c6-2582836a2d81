"use client"

import { useTheme } from "next-themes"
import { <PERSON>, <PERSON><PERSON><PERSON>, CartesianGrid, ResponsiveContainer, Tooltip, <PERSON>A<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON> } from "recharts"

// Hourly data instead of daily
const data = [
  { name: "8 AM", inbound: 12, outbound: 8, missed: 3 },
  { name: "9 AM", inbound: 18, outbound: 12, missed: 5 },
  { name: "10 AM", inbound: 24, outbound: 15, missed: 4 },
  { name: "11 AM", inbound: 30, outbound: 18, missed: 6 },
  { name: "12 PM", inbound: 22, outbound: 14, missed: 3 },
  { name: "1 PM", inbound: 15, outbound: 10, missed: 2 },
  { name: "2 PM", inbound: 20, outbound: 16, missed: 4 },
  { name: "3 PM", inbound: 25, outbound: 20, missed: 5 },
  { name: "4 PM", inbound: 28, outbound: 22, missed: 7 },
  { name: "5 PM", inbound: 18, outbound: 15, missed: 3 },
  { name: "6 PM", inbound: 12, outbound: 10, missed: 2 },
  { name: "7 PM", inbound: 8, outbound: 6, missed: 1 },
]

export function CallVolume() {
  const { theme } = useTheme()
  const isDark = theme === "dark"

  // Custom tooltip to include abandoned calls
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-card border rounded-md shadow-sm p-2 text-sm">
          <p className="font-medium">{label}</p>
          <div className="space-y-1 mt-1">
            <p className="flex items-center gap-2">
              <span className="w-3 h-3 rounded-full bg-[#3b82f6]"></span>
              <span>Inbound: {payload[0].value}</span>
            </p>
            <p className="flex items-center gap-2">
              <span className="w-3 h-3 rounded-full bg-[#10b981]"></span>
              <span>Outbound: {payload[1].value}</span>
            </p>
            <p className="flex items-center gap-2">
              <span className="w-3 h-3 rounded-full bg-[#ef4444]"></span>
              <span>Missed: {payload[2].value}</span>
            </p>
            <p className="text-xs text-muted-foreground mt-1 pt-1 border-t">
              Total calls: {payload[0].value + payload[1].value + payload[2].value}
            </p>
          </div>
        </div>
      );
    }
    return null;
  };

  return (
    <ResponsiveContainer width="100%" height={350}>
      <BarChart data={data} margin={{ top: 10, right: 10, left: 0, bottom: 20 }}>
        <CartesianGrid strokeDasharray="3 3" stroke={isDark ? "#333" : "#eee"} />
        <XAxis dataKey="name" stroke={isDark ? "#888" : "#888"} fontSize={12} tickLine={false} axisLine={false} />
        <YAxis stroke={isDark ? "#888" : "#888"} fontSize={12} tickLine={false} axisLine={false} />
        <Tooltip content={<CustomTooltip />} />
        <Legend
          verticalAlign="bottom"
          height={36}
          iconType="circle"
          iconSize={8}
          wrapperStyle={{ paddingTop: "10px" }}
        />
        <Bar dataKey="inbound" name="Inbound Calls" fill="#3b82f6" radius={[4, 4, 0, 0]} />
        <Bar dataKey="outbound" name="Outbound Calls" fill="#10b981" radius={[4, 4, 0, 0]} />
        <Bar dataKey="missed" name="Missed Calls" fill="#ef4444" radius={[4, 4, 0, 0]} />
      </BarChart>
    </ResponsiveContainer>
  )
}

