"use client"

import { useState } from "react"
import { useTheme } from "next-themes"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Clock, Phone } from "lucide-react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger } from "@/components/ui/tabs"
import { PeriodSelector } from "./period-selector"
import { DashboardStats } from "./dashboard-stats"
import { EnhancedCallVolume } from "./enhanced-call-volume"
import { EnhancedAgentPerformance } from "./enhanced-agent-performance"
import { TicketsTable } from "@/components/tickets/tickets-table"
import { useMyTickets } from "@/lib/hooks/useMyTickets"
import type { Ticket } from "@/lib/api/types"
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  ResponsiveContainer,
  XAxis,
  YAxis,
  Tooltip,
  CartesianGrid,
} from "recharts"

function LongestWaitCalls() {
  const { theme } = useTheme()
  const isDark = theme === "dark"

  const data = [
    { id: "CQ-005", waitTime: 240, position: 5, timestamp: "14:35", caller: "+1 (555) 0123" },
    { id: "CQ-003", waitTime: 210, position: 3, timestamp: "15:20", caller: "+1 (555) 0456" },
    { id: "CQ-008", waitTime: 180, position: 8, timestamp: "16:05", caller: "+1 (555) 0789" },
  ]

  return (
    <Card className="bg-red-800">
      <CardHeader>
        <CardTitle className="text-base">Longest Wait Time Calls</CardTitle>
        <CardDescription>Top 3 calls with extended wait times</CardDescription>
      </CardHeader>
      <CardContent>
        <ResponsiveContainer width="100%" height={250}>
          <BarChart data={data} layout="vertical" margin={{ top: 10, right: 10, left: 40, bottom: 0 }}>
            <CartesianGrid strokeDasharray="3 3" stroke={isDark ? "#333" : "#eee"} />
            <XAxis type="number" domain={[0, 'dataMax + 30']} />
            <YAxis dataKey="id" type="category" />
            <Tooltip
              content={({ active, payload, label }) => {
                if (active && payload && payload.length) {
                  const data = payload[0].payload;
                  return (
                    <div className="bg-card border rounded-md shadow-sm p-2 text-sm">
                      <div className="font-medium mb-1">{data.id}</div>
                      <div className="space-y-1 text-sm">
                        <div className="flex items-center gap-2">
                          <Clock className="h-3 w-3 text-muted-foreground" />
                          <span>{data.waitTime} seconds</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <Phone className="h-3 w-3 text-muted-foreground" />
                          <span>{data.caller}</span>
                        </div>
                        <div className="text-xs text-muted-foreground">
                          Received at {data.timestamp}
                        </div>
                      </div>
                    </div>
                  );
                }
                return null;
              }}
            />
            <Bar
              dataKey="waitTime"
              fill="#ef4444"
              radius={[0, 4, 4, 0]}
              label={{ position: 'right', formatter: (value) => `${value}s` }}
            />
          </BarChart>
        </ResponsiveContainer>
        <div className="mt-4 space-y-2">
          {data.map((call) => (
            <div key={call.id} className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Badge variant="outline">{call.id}</Badge>
                <span className="text-sm text-muted-foreground">{call.caller}</span>
              </div>
              <Badge variant="destructive">{call.waitTime}s</Badge>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  )
}

export default function DashboardPage() {
  const [currentTicket, setCurrentTicket] = useState<Ticket | null>(null)
  const { myTickets, isLoading, error } = useMyTickets()

  const handleViewTicket = (ticket: Ticket) => {
    setCurrentTicket(ticket)
  }

  const handleAssignTicket = (ticket: Ticket) => {
    setCurrentTicket(ticket)
  }

  const handleEscalateTicket = (ticket: Ticket) => {
    setCurrentTicket(ticket)
  }

  return (
    <div className="space-y-4 p-8 pt-6">
      <div className="flex items-center justify-between space-y-2">
        <h2 className="text-3xl font-bold tracking-tight">Dashboard</h2>
        <div className="flex items-center space-x-2">
          <PeriodSelector />
        </div>
      </div>
      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
        </TabsList>
        <TabsContent value="overview" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            <DashboardStats />
          </div>
          <div className="grid gap-4 grid-cols-1 md:grid-cols-2 lg:grid-cols-7">
            <Card className="col-span-4 bg-amber-300">
              <CardHeader>
                <CardTitle>Call Volume Analytics</CardTitle>
                <CardDescription>Interactive call volume analysis with drill-down capabilities</CardDescription>
              </CardHeader>  
              <CardContent className="p-0">
                <EnhancedCallVolume showComparative={true} allowDrillDown={true} />
              </CardContent>
            </Card>
            <Card className="col-span-3">
              <CardHeader>
                <CardTitle>Agent Performance</CardTitle>
                <CardDescription>Ticket resolution tracking and performance metrics</CardDescription>
              </CardHeader>
              <CardContent className="p-0">
                <EnhancedAgentPerformance showTrends={true} maxAgents={6} />
              </CardContent>
            </Card>
          </div>
          <div className="grid gap-4 grid-cols-1 md:grid-cols-2">
            <LongestWaitCalls />
            <Card>
              <CardHeader>
                <CardTitle>Recent Tickets</CardTitle>
                <CardDescription>Recently created and updated tickets</CardDescription>
              </CardHeader>
              <CardContent>
                <TicketsTable
                  tickets={myTickets}
                  onViewTicket={handleViewTicket}
                  onAssignTicket={handleAssignTicket}
                  onEscalateTicket={handleEscalateTicket}
                />
              </CardContent>
            </Card>
          </div>
        </TabsContent>
        <TabsContent value="analytics" className="space-y-4">
          <div className="grid gap-4 space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Advanced Call Analytics</CardTitle>
                <CardDescription>Detailed call volume analysis with comparative data and trends</CardDescription>
              </CardHeader>
              <CardContent className="p-0">
                <EnhancedCallVolume showComparative={true} allowDrillDown={true} />
              </CardContent>
            </Card>
            <Card>
              <CardHeader>
                <CardTitle>Detailed Agent Performance</CardTitle>
                <CardDescription>Comprehensive agent performance metrics with targets and trends</CardDescription>
              </CardHeader>
              <CardContent className="p-0">
                <EnhancedAgentPerformance showTrends={true} maxAgents={10} />
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}