/**
 * Voice System Initializer Component
 * Initializes the CCR voice system when the application starts
 */

'use client';

import { useEffect, useState } from 'react';
import { useWebRTCVoice } from '@/lib/hooks/useWebRTCVoice';
import { loadSipVoiceSystem, isSipVoiceSystemLoaded } from '@/lib/services/sip-voice-loader';
import { useToast } from '@/hooks/use-toast';

interface VoiceSystemInitializerProps {
  children: React.ReactNode;
}

export function VoiceSystemInitializer({ children }: VoiceSystemInitializerProps) {
  const [isLoading, setIsLoading] = useState(true);
  const [isVoiceSystemReady, setIsVoiceSystemReady] = useState(false);
  const webrtcVoice = useWebRTCVoice();
  const { toast } = useToast();

  useEffect(() => {
    async function initializeVoiceSystem() {
      try {
        console.log('🎙️ Starting voice system initialization...');
        
        // 1. Load SIP voice system if not already loaded
        if (!isSipVoiceSystemLoaded()) {
          console.log('📋 Loading SIP voice system...');
          loadSipVoiceSystem();
          
          // Wait a bit for the system to load
          await new Promise(resolve => setTimeout(resolve, 500));
        }
        
        // 2. Check if voice system is supported
        if (!webrtcVoice.isSupported) {
          console.warn('⚠️ Voice system not supported');
          toast({
            variant: "destructive",
            title: "Voice System Not Available",
            description: "SIP voice library not loaded. Voice features will be disabled.",
          });
          setIsLoading(false);
          return;
        }
        
        // 3. Initialize the voice system
        console.log('🔄 Initializing voice communication...');
        const initialized = await webrtcVoice.initialize();
        
        if (initialized) {
          console.log('✅ Voice system ready!');
          setIsVoiceSystemReady(true);
          
          toast({
            title: "Voice System Ready",
            description: "Extension 1003 connected and ready for calls",
          });
        } else {
          console.error('❌ Voice system initialization failed');
          toast({
            variant: "destructive",
            title: "Voice System Failed",
            description: "Could not initialize voice communication. Voice features will be limited.",
          });
        }
        
      } catch (error) {
        console.error('❌ Voice system initialization error:', error);
        toast({
          variant: "destructive",
          title: "Voice System Error",
          description: "An error occurred while initializing voice communication.",
        });
      } finally {
        setIsLoading(false);
      }
    }

    // Initialize voice system on component mount
    initializeVoiceSystem();
  }, [webrtcVoice, toast]);

  // Show loading state while initializing
  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-sm text-muted-foreground">Initializing voice system...</p>
        </div>
      </div>
    );
  }

  // Render children with voice system status
  return (
    <div>
      {/* Voice system status indicator (for debugging) */}
      {process.env.NODE_ENV === 'development' && (
        <div className="fixed bottom-4 right-4 z-50 bg-background border rounded-lg p-2 text-xs">
          <div className="flex items-center gap-2">
            <div 
              className={`w-2 h-2 rounded-full ${
                isVoiceSystemReady ? 'bg-green-500' : 'bg-red-500'
              }`}
            />
            <span>
              Voice: {isVoiceSystemReady ? 'Ready' : 'Disabled'}
              {webrtcVoice.extension && ` (Ext. ${webrtcVoice.extension})`}
            </span>
          </div>
        </div>
      )}
      
      {children}
    </div>
  );
}
