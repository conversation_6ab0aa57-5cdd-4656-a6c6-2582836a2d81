"use client";

import React from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useCallHandler } from '@/lib/hooks/useCallHandler';
import { useToast } from '@/components/ui/use-toast';
import { Phone, PhoneCall } from 'lucide-react';

export function OutboundCallTest() {
  const { currentCall, makeOutboundCall, callTimer, formatCallTimer } = useCallHandler();
  const { toast } = useToast();

  const testNumbers = [
    '+254712345678',
    '+254722123456',
    '+254733987654'
  ];

  const handleTestCall = async (phoneNumber: string) => {
    try {
      await makeOutboundCall(phoneNumber);
      toast({
        title: "Test Call Initiated",
        description: `Calling ${phoneNumber}...`,
        variant: "default",
      });
    } catch (error) {
      console.error('Test call failed:', error);
      toast({
        title: "Test Call Failed",
        description: "Failed to initiate test call.",
        variant: "destructive",
      });
    }
  };

  return (
    <Card className="w-full max-w-md">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Phone className="h-5 w-5" />
          Outbound Call Test
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Current Call Status */}
        {currentCall && (
          <div className="p-3 bg-blue-50 rounded-lg border">
            <div className="flex items-center justify-between">
              <div>
                <p className="font-medium">
                  {currentCall.direction === 'Outbound' ? 'Outbound Call' : 'Incoming Call'}
                </p>
                <p className="text-sm text-muted-foreground">
                  {currentCall.callerNumber}
                </p>
                <p className="text-xs text-muted-foreground">
                  Status: {currentCall.status}
                </p>
              </div>
              {currentCall.status === 'answered' && (
                <div className="text-right">
                  <p className="font-mono text-lg">
                    {formatCallTimer ? formatCallTimer(callTimer || 0) : '00:00'}
                  </p>
                  <p className="text-xs text-muted-foreground">Call Time</p>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Test Call Buttons */}
        <div className="space-y-2">
          <p className="text-sm font-medium">Test Numbers:</p>
          {testNumbers.map((number) => (
            <Button
              key={number}
              variant="outline"
              className="w-full justify-start"
              onClick={() => handleTestCall(number)}
              disabled={Boolean(currentCall && currentCall.status !== 'ended')}
            >
              <PhoneCall className="h-4 w-4 mr-2" />
              {number}
            </Button>
          ))}
        </div>

        {/* Instructions */}
        <div className="text-xs text-muted-foreground p-3 bg-gray-50 rounded">
          <p className="font-medium mb-1">Test Instructions:</p>
          <ul className="space-y-1">
            <li>• Click a test number to initiate an outbound call</li>
            <li>• The call will show "ringing" status initially</li>
            <li>• SIP server should send connection/failure events</li>
            <li>• Timer will start when call is connected</li>
          </ul>
        </div>
      </CardContent>
    </Card>
  );
}
