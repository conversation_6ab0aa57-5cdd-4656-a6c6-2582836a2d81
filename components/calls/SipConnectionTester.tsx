"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { 
  Wifi, 
  WifiOff, 
  TestTube, 
  CheckCircle, 
  XCircle, 
  AlertTriangle,
  Loader2
} from "lucide-react";

interface TestResult {
  test: string;
  status: 'success' | 'error' | 'warning';
  message: string;
  details?: string;
}

export function SipConnectionTester() {
  const [isTestingConnection, setIsTestingConnection] = useState(false);
  const [testResults, setTestResults] = useState<TestResult[]>([]);
  const [customWssUrl, setCustomWssUrl] = useState("");
  const [customDomain, setCustomDomain] = useState("");

  const addTestResult = (test: string, status: 'success' | 'error' | 'warning', message: string, details?: string) => {
    setTestResults(prev => [...prev, { test, status, message, details }]);
  };

  const testWebSocketConnection = async (wssUrl: string): Promise<boolean> => {
    return new Promise((resolve) => {
      try {
        const ws = new WebSocket(wssUrl);
        
        const timeout = setTimeout(() => {
          ws.close();
          addTestResult('WebSocket', 'error', 'Connection timeout (10s)', 'WebSocket failed to connect within 10 seconds');
          resolve(false);
        }, 10000);

        ws.onopen = () => {
          clearTimeout(timeout);
          addTestResult('WebSocket', 'success', 'Connection established', `Successfully connected to ${wssUrl}`);
          ws.close();
          resolve(true);
        };

        ws.onerror = (error) => {
          clearTimeout(timeout);
          addTestResult('WebSocket', 'error', 'Connection failed', `Failed to connect to ${wssUrl}: ${error}`);
          resolve(false);
        };

        ws.onclose = (event) => {
          if (event.code !== 1000) { // 1000 is normal closure
            addTestResult('WebSocket', 'warning', `Connection closed unexpectedly`, `Code: ${event.code}, Reason: ${event.reason}`);
          }
        };

      } catch (error) {
        addTestResult('WebSocket', 'error', 'Connection error', `Exception: ${error}`);
        resolve(false);
      }
    });
  };

  const testDnsResolution = async (domain: string): Promise<boolean> => {
    try {
      // Simple DNS test by trying to fetch a basic HTTP request
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 5000);

      const response = await fetch(`https://${domain}`, {
        method: 'HEAD',
        signal: controller.signal,
        mode: 'no-cors' // Avoid CORS issues
      });

      clearTimeout(timeoutId);
      addTestResult('DNS', 'success', 'Domain resolves', `${domain} is reachable`);
      return true;
    } catch (error) {
      if (error instanceof Error && error.name === 'AbortError') {
        addTestResult('DNS', 'error', 'DNS timeout', `${domain} did not respond within 5 seconds`);
      } else {
        addTestResult('DNS', 'warning', 'DNS check inconclusive', `${domain} - ${error}`);
      }
      return false;
    }
  };

  const testEnvironmentVariables = () => {
    const requiredVars = [
      'NEXT_PUBLIC_WEBRTC_ENABLED',
      'NEXT_PUBLIC_SIP_SERVER_WSS',
      'NEXT_PUBLIC_SIP_DOMAIN',
      'NEXT_PUBLIC_SIP_EXTENSION_BASE',
      'NEXT_PUBLIC_SIP_PASSWORD'
    ];

    let allPresent = true;

    requiredVars.forEach(varName => {
      const value = process.env[varName];
      if (!value) {
        addTestResult('Environment', 'error', `Missing ${varName}`, 'Required environment variable not set');
        allPresent = false;
      } else {
        addTestResult('Environment', 'success', `${varName} is set`, `Value: ${varName.includes('PASSWORD') ? '***' : value}`);
      }
    });

    return allPresent;
  };

  const runConnectionTests = async () => {
    setIsTestingConnection(true);
    setTestResults([]);

    try {
      // Test 1: Environment Variables
      addTestResult('Test', 'success', 'Starting connection tests', 'Running comprehensive SIP connection diagnostics');
      
      const envOk = testEnvironmentVariables();
      
      // Test 2: DNS Resolution
      const domain = customDomain || process.env.NEXT_PUBLIC_SIP_DOMAIN || '';
      if (domain) {
        await testDnsResolution(domain);
      } else {
        addTestResult('DNS', 'error', 'No domain to test', 'SIP domain not configured');
      }

      // Test 3: WebSocket Connection
      const wssUrl = customWssUrl || process.env.NEXT_PUBLIC_SIP_SERVER_WSS || '';
      if (wssUrl) {
        await testWebSocketConnection(wssUrl);
      } else {
        addTestResult('WebSocket', 'error', 'No WebSocket URL to test', 'SIP WebSocket URL not configured');
      }

      // Test 4: Browser Capabilities
      if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {
        addTestResult('Browser', 'success', 'WebRTC supported', 'Browser supports WebRTC and media devices');
      } else {
        addTestResult('Browser', 'error', 'WebRTC not supported', 'Browser does not support WebRTC');
      }

      // Test 5: HTTPS Check
      if (window.location.protocol === 'https:' || window.location.hostname === 'localhost') {
        addTestResult('Security', 'success', 'Secure context', 'Running in HTTPS or localhost - WebRTC will work');
      } else {
        addTestResult('Security', 'error', 'Insecure context', 'WebRTC requires HTTPS in production');
      }

    } catch (error) {
      addTestResult('Test', 'error', 'Test suite failed', `Unexpected error: ${error}`);
    } finally {
      setIsTestingConnection(false);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'success': return <CheckCircle className="h-4 w-4 text-green-600" />;
      case 'error': return <XCircle className="h-4 w-4 text-red-600" />;
      case 'warning': return <AlertTriangle className="h-4 w-4 text-yellow-600" />;
      default: return null;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'success': return 'bg-green-50 border-green-200';
      case 'error': return 'bg-red-50 border-red-200';
      case 'warning': return 'bg-yellow-50 border-yellow-200';
      default: return 'bg-gray-50 border-gray-200';
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <TestTube className="h-5 w-5" />
          SIP Connection Tester
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Custom Test Inputs */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <Label htmlFor="custom-wss">Custom WebSocket URL (optional)</Label>
            <Input
              id="custom-wss"
              placeholder="wss://your-sip-server.com/ws"
              value={customWssUrl}
              onChange={(e) => setCustomWssUrl(e.target.value)}
            />
          </div>
          <div>
            <Label htmlFor="custom-domain">Custom SIP Domain (optional)</Label>
            <Input
              id="custom-domain"
              placeholder="your-sip-server.com"
              value={customDomain}
              onChange={(e) => setCustomDomain(e.target.value)}
            />
          </div>
        </div>

        {/* Test Button */}
        <Button 
          onClick={runConnectionTests} 
          disabled={isTestingConnection}
          className="w-full"
        >
          {isTestingConnection ? (
            <>
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              Running Tests...
            </>
          ) : (
            <>
              <TestTube className="h-4 w-4 mr-2" />
              Run Connection Tests
            </>
          )}
        </Button>

        {/* Test Results */}
        {testResults.length > 0 && (
          <div className="space-y-2">
            <h3 className="font-medium">Test Results:</h3>
            {testResults.map((result, index) => (
              <div key={index} className={`p-3 rounded-lg border ${getStatusColor(result.status)}`}>
                <div className="flex items-center gap-2">
                  {getStatusIcon(result.status)}
                  <span className="font-medium">{result.test}:</span>
                  <span>{result.message}</span>
                </div>
                {result.details && (
                  <p className="text-sm text-muted-foreground mt-1 ml-6">
                    {result.details}
                  </p>
                )}
              </div>
            ))}
          </div>
        )}

        {/* Current Configuration Display */}
        <div className="mt-4 p-3 bg-gray-50 rounded-lg">
          <h4 className="font-medium mb-2">Current Configuration:</h4>
          <div className="text-sm space-y-1">
            <p><strong>WebSocket:</strong> {process.env.NEXT_PUBLIC_SIP_SERVER_WSS || 'Not set'}</p>
            <p><strong>Domain:</strong> {process.env.NEXT_PUBLIC_SIP_DOMAIN || 'Not set'}</p>
            <p><strong>Extension:</strong> {process.env.NEXT_PUBLIC_SIP_EXTENSION_BASE || 'Not set'}</p>
            <p><strong>WebRTC Enabled:</strong> {process.env.NEXT_PUBLIC_WEBRTC_ENABLED || 'Not set'}</p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
