"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { useWebRTC } from "@/lib/hooks/useWebRTC";
import { useAuth } from "@/lib/hooks/useAuth";
import { 
  Phone, 
  PhoneOff, 
  Wifi, 
  WifiOff, 
  User, 
  AlertCircle, 
  CheckCircle,
  Loader2,
  RefreshCw
} from "lucide-react";

export function WebRTCStatus() {
  const { user } = useAuth();
  const {
    isEnabled,
    isRegistered,
    isRegistering,
    registrationError,
    currentAgent,
    activeCalls,
    incomingCall,
    registerAgent,
    unregisterAgent,
    isConnected
  } = useWebRTC();

  const [isManualAction, setIsManualAction] = useState(false);

  const handleRegister = async () => {
    setIsManualAction(true);
    try {
      await registerAgent();
    } finally {
      setIsManualAction(false);
    }
  };

  const handleUnregister = async () => {
    setIsManualAction(true);
    try {
      await unregisterAgent();
    } finally {
      setIsManualAction(false);
    }
  };

  if (!isEnabled) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <PhoneOff className="h-5 w-5 text-muted-foreground" />
            WebRTC Calling
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center gap-2">
            <Badge variant="secondary">Disabled</Badge>
            <span className="text-sm text-muted-foreground">
              WebRTC calling is not enabled
            </span>
          </div>
          <p className="text-xs text-muted-foreground mt-2">
            Configure NEXT_PUBLIC_WEBRTC_ENABLED=true in environment variables
          </p>
        </CardContent>
      </Card>
    );
  }

  if (!user) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <User className="h-5 w-5 text-muted-foreground" />
            WebRTC Calling
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center gap-2">
            <Badge variant="secondary">Not Available</Badge>
            <span className="text-sm text-muted-foreground">
              Please log in to enable calling
            </span>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Phone className="h-5 w-5" />
          WebRTC Calling
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Registration Status */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            {isRegistered ? (
              <CheckCircle className="h-4 w-4 text-green-600" />
            ) : isRegistering ? (
              <Loader2 className="h-4 w-4 animate-spin text-blue-600" />
            ) : (
              <AlertCircle className="h-4 w-4 text-red-600" />
            )}
            
            <div>
              <p className="text-sm font-medium">
                {isRegistered ? 'Registered' : isRegistering ? 'Registering...' : 'Not Registered'}
              </p>
              {currentAgent && (
                <p className="text-xs text-muted-foreground">
                  Extension: {currentAgent.extension} ({currentAgent.displayName})
                </p>
              )}
            </div>
          </div>

          <div className="flex gap-2">
            {isRegistered ? (
              <Button
                variant="outline"
                size="sm"
                onClick={handleUnregister}
                disabled={isManualAction}
              >
                {isManualAction ? (
                  <Loader2 className="h-4 w-4 animate-spin" />
                ) : (
                  <PhoneOff className="h-4 w-4" />
                )}
                Unregister
              </Button>
            ) : (
              <Button
                variant="default"
                size="sm"
                onClick={handleRegister}
                disabled={isRegistering || isManualAction}
              >
                {isRegistering || isManualAction ? (
                  <Loader2 className="h-4 w-4 animate-spin" />
                ) : (
                  <Phone className="h-4 w-4" />
                )}
                Register
              </Button>
            )}
          </div>
        </div>

        {/* Connection Status */}
        <div className="flex items-center gap-2">
          {isConnected ? (
            <Wifi className="h-4 w-4 text-green-600" />
          ) : (
            <WifiOff className="h-4 w-4 text-red-600" />
          )}
          <span className="text-sm">
            {isConnected ? 'Connected to SIP server' : 'Disconnected from SIP server'}
          </span>
        </div>

        {/* Error Display */}
        {registrationError && (
          <div className="p-3 border border-red-200 rounded-lg bg-red-50">
            <div className="flex items-center gap-2">
              <AlertCircle className="h-4 w-4 text-red-600" />
              <p className="text-sm font-medium text-red-800">Registration Error</p>
            </div>
            <p className="text-sm text-red-700 mt-1">{registrationError}</p>
            <Button
              variant="outline"
              size="sm"
              className="mt-2"
              onClick={handleRegister}
              disabled={isRegistering || isManualAction}
            >
              <RefreshCw className="h-4 w-4 mr-1" />
              Retry
            </Button>
          </div>
        )}

        {/* Active Calls Status */}
        {(activeCalls.length > 0 || incomingCall) && (
          <div className="p-3 border rounded-lg bg-blue-50">
            <p className="text-sm font-medium text-blue-800 mb-2">Call Status</p>
            
            {incomingCall && (
              <div className="flex items-center gap-2 mb-2">
                <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                <span className="text-sm text-blue-700">
                  Incoming call from {incomingCall.remoteNumber}
                </span>
              </div>
            )}
            
            {activeCalls.filter(call => call.status === 'answered').map(call => (
              <div key={call.id} className="flex items-center gap-2">
                <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                <span className="text-sm text-blue-700">
                  Active call with {call.remoteNumber} ({call.status})
                </span>
              </div>
            ))}
          </div>
        )}

        {/* Configuration Info */}
        <div className="text-xs text-muted-foreground space-y-1">
          <p>SIP Server: {process.env.NEXT_PUBLIC_SIP_DOMAIN}</p>
          <p>Extension: {process.env.NEXT_PUBLIC_SIP_EXTENSION_BASE}</p>
          {process.env.NEXT_PUBLIC_WEBRTC_DEBUG === 'true' && (
            <p className="text-yellow-600">Debug mode enabled</p>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
