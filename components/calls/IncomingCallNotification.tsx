"use client";

import { useState, useEffect, useRef, useMemo } from "react";
import { Phone, PhoneOff, User } from "lucide-react";
import { Button } from "@/components/ui/button";
import { CardFooter } from "@/components/ui/card";
import { IncomingCall } from "@/lib/services/socket-service";
import { formatPhoneNumber, formatRelativeTime } from "@/lib/utils/format";
import { useCallHandler } from "@/lib/hooks/useCallHandler";
import { playSound, stopSound, SOUNDS } from "@/lib/utils/sound";
import { AudioPlayer } from "@/components/ui/audio-player";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";

interface IncomingCallNotificationProps {
  call: IncomingCall;
}

export function IncomingCallNotification({ call }: IncomingCallNotificationProps) {
  const { answerCall, rejectCall } = useCallHandler();
  const [timeElapsed, setTimeElapsed] = useState<string>("");
  const [isOpen, setIsOpen] = useState(true);
  const [soundEnabled, setSoundEnabled] = useState(true);
  const audioRef = useRef<HTMLAudioElement | null>(null);

  // Use callId as key for notification uniqueness
  const notificationKey = useMemo(() => call.id, [call.id]);

  useEffect(() => {
    // Clean up previous notifications when a new call comes in
    const cleanup = () => {
      const existingNotifications = document.querySelectorAll('[data-notification-id]');
      existingNotifications.forEach(notification => {
        if (notification.getAttribute('data-notification-id') !== notificationKey) {
          notification.remove();
        }
      });
    };
    cleanup();

    return () => cleanup();
  }, [notificationKey]);

  // Log when component mounts
  useEffect(() => {
    console.log('IncomingCallNotification component mounted', { callStatus: call.status });

    // Try to directly create and play an audio element to test browser capabilities
    try {
      const testAudio = new Audio();
      console.log('Audio element created successfully');

      // Check if we can set the source
      testAudio.src = SOUNDS.INCOMING_CALL;
      console.log('Audio source set successfully:', SOUNDS.INCOMING_CALL);

      // Check if the file exists by listening for error events
      testAudio.addEventListener('error', (e) => {
        console.error('Error loading audio file:', e);
        console.error('Audio error code:', testAudio.error ? testAudio.error.code : 'unknown');
      });

      // Check if we can play
      testAudio.volume = 0.01; // Very low volume for the test
      const playPromise = testAudio.play();
      if (playPromise !== undefined) {
        playPromise
          .then(() => {
            console.log('TEST AUDIO PLAYED SUCCESSFULLY');
            // Stop it immediately
            testAudio.pause();
            testAudio.currentTime = 0;
          })
          .catch(err => {
            console.error('TEST AUDIO PLAY FAILED:', err.name, err.message);
          });
      }
    } catch (err) {
      console.error('Error creating test audio:', err);
    }

    return () => {
      console.log('IncomingCallNotification component unmounting');
    };
  }, []);

  // Play ringtone when component mounts
  useEffect(() => {
    console.log('Call status effect running, status:', call.status);

    if (call.status === 'ringing') {
      console.log('ATTEMPTING TO PLAY INCOMING CALL SOUND');

      // Create a direct Audio element first as a test
      const directAudio = new Audio(SOUNDS.INCOMING_CALL);
      directAudio.loop = true;
      directAudio.volume = 0.5;

      // Log the audio element properties
      console.log('Direct audio element created:', {
        src: directAudio.src,
        readyState: directAudio.readyState,
        paused: directAudio.paused,
        volume: directAudio.volume
      });

      // Try to play directly first
      directAudio.play()
        .then(() => {
          console.log('DIRECT AUDIO PLAYING SUCCESSFULLY');
          audioRef.current = directAudio;
        })
        .catch(err => {
          console.error('DIRECT AUDIO PLAY FAILED:', err.name, err.message);

          // Fall back to the sound utility
          console.log('Falling back to sound utility');

          // Try to play the sound with multiple attempts
          const playRingtone = () => {
            // Play the ringtone with looping enabled and force play to try to overcome autoplay restrictions
            audioRef.current = playSound(SOUNDS.INCOMING_CALL, {
              loop: true,
              volume: 0.7, // Increase volume
              forcePlay: true
            });

            console.log('Ringtone play attempt result:', audioRef.current ? 'Audio element created' : 'Failed to create audio');

            // Check if the audio is actually playing
            if (audioRef.current) {
              console.log('Audio state:', {
                paused: audioRef.current.paused,
                currentTime: audioRef.current.currentTime,
                readyState: audioRef.current.readyState
              });

              // Add event listeners to track audio state
              audioRef.current.addEventListener('play', () => console.log('Audio play event fired'));
              audioRef.current.addEventListener('playing', () => console.log('Audio playing event fired'));
              audioRef.current.addEventListener('pause', () => console.log('Audio pause event fired'));
              audioRef.current.addEventListener('error', (e) => console.error('Audio error event:', e));
            }
          };

          // Try to play immediately
          playRingtone();

          // If that fails, try again after a short delay (sometimes helps with timing issues)
          const retryTimeout = setTimeout(() => {
            if (!audioRef.current || audioRef.current.paused) {
              console.log('Retrying ringtone playback after delay');
              playRingtone();
            }
          }, 1000);

          // Try one more time with a longer delay
          const finalRetryTimeout = setTimeout(() => {
            if (!audioRef.current || audioRef.current.paused) {
              console.log('Final retry for ringtone playback');
              playRingtone();
            }
          }, 3000);

          // Store timeouts for cleanup
          const timeouts = [retryTimeout, finalRetryTimeout];

          // Clean up the retry timeouts
          return () => {
            timeouts.forEach(clearTimeout);
            if (audioRef.current) {
              console.log('Stopping ringtone sound');
              stopSound(audioRef.current);
            }
          };
        });
    }

    // Clean up function
    return () => {
      if (audioRef.current) {
        console.log('Stopping ringtone sound (cleanup)');
        stopSound(audioRef.current);
      }
    };
  }, [call.status]);

  // Update time elapsed every second
  useEffect(() => {
    const updateTime = () => {
      const elapsed = formatRelativeTime(new Date(call.timestamp));
      setTimeElapsed(elapsed);
    };

    updateTime();
    const interval = setInterval(updateTime, 1000);

    return () => clearInterval(interval);
  }, [call.timestamp]);

  // Auto-close after 30 seconds if not answered
  useEffect(() => {
    if (call.status === 'ringing') {
      const timeout = setTimeout(() => {
        if (audioRef.current) {
          stopSound(audioRef.current);
        }
        rejectCall();
        setIsOpen(false);
      }, 30000);

      return () => clearTimeout(timeout);
    }
  }, [call.status, rejectCall]);

  // Handle answer call
  const handleAnswer = () => {
    // Stop the ringtone
    if (audioRef.current) {
      stopSound(audioRef.current);
    }
    answerCall();
    setIsOpen(false);
  };

  // Handle reject call
  const handleReject = () => {
    // Stop the ringtone
    if (audioRef.current) {
      stopSound(audioRef.current);
    }
    rejectCall();
    setIsOpen(false);
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen} data-notification-id={notificationKey}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="text-center">Incoming Call</DialogTitle>
        </DialogHeader>
        <div className="flex flex-col items-center justify-center p-6 space-y-4">
          <div className="w-20 h-20 rounded-full bg-primary/10 flex items-center justify-center animate-pulse">
            <User className="h-10 w-10 text-primary" />
          </div>
          <div className="text-center">
            <h3 className="text-xl font-semibold">{formatPhoneNumber(call.callerNumber)}</h3>
            <p className="text-sm text-muted-foreground">via {formatPhoneNumber(call.channelNumber)}</p>
            {call.productName && (
              <p className="text-sm font-medium mt-1">
                <span className="px-2 py-0.5 bg-blue-100 text-blue-800 rounded-full text-xs">
                  {call.productName}
                </span>
              </p>
            )}
            {call.callerId && (
              <p className="text-xs font-medium mt-1">ID: {call.callerId}</p>
            )}
            <p className="text-xs text-muted-foreground mt-1">{timeElapsed}</p>
          </div>

          {/* Add the AudioPlayer component */}
          <AudioPlayer
            src={SOUNDS.INCOMING_CALL}
            autoPlay={true}
            loop={true}
            volume={0.5}
            onError={(error) => console.error('AudioPlayer error:', error)}
          />
        </div>
        <CardFooter className="flex justify-center gap-4">
          
          <Button
            variant="default"
            size="lg"
            className="rounded-full w-14 h-14 p-0 bg-green-600 hover:bg-green-700"
            onClick={handleAnswer}
          >
            <Phone className="h-6 w-6" />
          </Button>
        </CardFooter>
      </DialogContent>
    </Dialog>
  );
}
