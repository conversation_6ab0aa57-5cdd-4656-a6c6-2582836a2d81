"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Textarea } from "@/components/ui/textarea";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { sipRecordingService, SipRecording } from "@/lib/services/sip-recording-service";
import { useSipRecordings } from "@/lib/hooks/useSipRecordings";
import { FileAudio, RefreshCw, TestTube, Play, Download } from "lucide-react";
import { formatPhoneNumber } from "@/lib/utils/format";
import { EnhancedCallRecordingPlayer } from "./EnhancedCallRecordingPlayer";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";

export function SipRecordingDebug() {
  const { recordings, loading, error, refetch, stats } = useSipRecordings();
  const [testFilename, setTestFilename] = useState("in-+254709918000-716038129-20250528-134749-1748440069.72.wav");
  const [testCallerNumber, setTestCallerNumber] = useState("+254709918000");
  const [testCallId, setTestCallId] = useState("716038129");
  const [parseResult, setParseResult] = useState<any>(null);
  const [matchResult, setMatchResult] = useState<SipRecording | null>(null);
  const [selectedRecording, setSelectedRecording] = useState<SipRecording | null>(null);
  const [showPlayer, setShowPlayer] = useState(false);

  const handleParseTest = () => {
    const result = sipRecordingService.parseFilename(testFilename);
    setParseResult(result);
  };

  const handleMatchTest = async () => {
    const result = await sipRecordingService.getRecordingForCall(testCallerNumber, testCallId);
    setMatchResult(result);
  };

  const handlePlayRecording = (recording: SipRecording) => {
    // Open the enhanced audio player instead of redirecting
    setSelectedRecording(recording);
    setShowPlayer(true);
  };

  const handleTestDirectUrl = () => {
    const apiUrl = process.env.NEXT_PUBLIC_SIP_RECORDING_API || '/api/sip-recordings';
    window.open(apiUrl, '_blank');
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TestTube className="h-5 w-5" />
            SIP Recording Integration Debug
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Configuration Status */}
          <div className="space-y-3">
            <div className="flex items-center justify-between p-3 border rounded-lg">
              <div>
                <p className="font-medium">SIP Server Configuration</p>
                <p className="text-sm text-muted-foreground">
                  API: {process.env.NEXT_PUBLIC_SIP_RECORDING_API || '/api/sip-recordings (fallback)'}
                </p>
                <p className="text-sm text-muted-foreground">
                  Files: {process.env.NEXT_PUBLIC_SIP_FILES_ENDPOINT || '/api/sip-recordings/files (fallback)'}
                </p>
              </div>
              <div className="flex items-center gap-2">
                <Badge variant={process.env.NEXT_PUBLIC_SIP_RECORDING_API ? "default" : "secondary"}>
                  {process.env.NEXT_PUBLIC_SIP_RECORDING_API ? "Configured" : "Using Mock"}
                </Badge>
                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={refetch}
                    disabled={loading}
                  >
                    <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleTestDirectUrl}
                  >
                    Test URL
                  </Button>
                </div>
              </div>
            </div>

            {/* Configuration Instructions */}
            {!process.env.NEXT_PUBLIC_SIP_RECORDING_API && (
              <div className="p-3 border border-yellow-200 rounded-lg bg-yellow-50">
                <p className="text-sm font-medium text-yellow-800 mb-2">⚠️ SIP Server Not Configured</p>
                <p className="text-sm text-yellow-700 mb-2">
                  Add these environment variables to your <code className="bg-yellow-100 px-1 rounded">.env.local</code> file:
                </p>
                <pre className="text-xs bg-yellow-100 p-2 rounded border text-yellow-800">
{`NEXT_PUBLIC_SIP_RECORDING_API="http://your-sip-server/api/recordings"
NEXT_PUBLIC_SIP_FILES_ENDPOINT="http://your-sip-server/api/recordings/files"`}
                </pre>
                <p className="text-sm text-yellow-700 mt-2">
                  Currently using mock endpoints for testing.
                </p>
              </div>
            )}

            {/* Connection Status */}
            <div className="flex items-center justify-between p-3 border rounded-lg">
              <div>
                <p className="font-medium">Connection Status</p>
                <p className="text-sm text-muted-foreground">
                  {error ? error : 'API responding normally'}
                </p>
              </div>
              <Badge variant={error ? "destructive" : "default"}>
                {error ? "Error" : "Online"}
              </Badge>
            </div>
          </div>

          {/* Statistics */}
          {stats && (
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="text-center p-3 border rounded-lg">
                <div className="text-2xl font-bold">{stats.total}</div>
                <div className="text-sm text-muted-foreground">Total</div>
              </div>
              <div className="text-center p-3 border rounded-lg">
                <div className="text-2xl font-bold">{stats.inbound}</div>
                <div className="text-sm text-muted-foreground">Inbound</div>
              </div>
              <div className="text-center p-3 border rounded-lg">
                <div className="text-2xl font-bold">{stats.outbound}</div>
                <div className="text-sm text-muted-foreground">Outbound</div>
              </div>
              <div className="text-center p-3 border rounded-lg">
                <div className="text-2xl font-bold">{stats.today}</div>
                <div className="text-sm text-muted-foreground">Today</div>
              </div>
            </div>
          )}

          {/* Error Display */}
          {error && (
            <div className="p-3 border border-destructive rounded-lg bg-destructive/10">
              <p className="text-sm text-destructive font-medium">Connection Error:</p>
              <p className="text-sm text-destructive whitespace-pre-wrap">{error}</p>

              {/* Common ngrok issues */}
              {error.includes('<!DOCTYPE') && (
                <div className="mt-3 p-2 bg-yellow-50 border border-yellow-200 rounded">
                  <p className="text-sm font-medium text-yellow-800">💡 Possible Solutions:</p>
                  <ul className="text-sm text-yellow-700 mt-1 space-y-1">
                    <li>• Check if the SIP server endpoint is correct</li>
                    <li>• Verify ngrok tunnel is active and accessible</li>
                    <li>• Test the URL directly in Postman with header: <code>ngrok-skip-browser-warning: true</code></li>
                    <li>• Ensure the endpoint returns JSON, not HTML</li>
                  </ul>
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Filename Parser Test */}
      <Card>
        <CardHeader>
          <CardTitle>Filename Parser Test</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label htmlFor="test-filename">Test Filename</Label>
            <Input
              id="test-filename"
              value={testFilename}
              onChange={(e) => setTestFilename(e.target.value)}
              placeholder="in-+254709918000-716038129-20250528-134749-1748440069.72.wav"
            />
          </div>
          <Button onClick={handleParseTest}>Parse Filename</Button>
          
          {parseResult && (
            <div className="p-3 border rounded-lg bg-muted/50">
              <p className="font-medium mb-2">Parse Result:</p>
              <pre className="text-sm">{JSON.stringify(parseResult, null, 2)}</pre>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Call Matching Test */}
      <Card>
        <CardHeader>
          <CardTitle>Call Matching Test</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="test-caller">Caller Number</Label>
              <Input
                id="test-caller"
                value={testCallerNumber}
                onChange={(e) => setTestCallerNumber(e.target.value)}
                placeholder="+254709918000"
              />
            </div>
            <div>
              <Label htmlFor="test-call-id">Call ID (optional)</Label>
              <Input
                id="test-call-id"
                value={testCallId}
                onChange={(e) => setTestCallId(e.target.value)}
                placeholder="716038129"
              />
            </div>
          </div>
          <Button onClick={handleMatchTest}>Find Recording</Button>
          
          {matchResult && (
            <div className="p-3 border rounded-lg bg-muted/50">
              <p className="font-medium mb-2">Match Result:</p>
              <div className="space-y-2">
                <p><strong>Filename:</strong> {matchResult.filename}</p>
                <p><strong>Caller:</strong> {formatPhoneNumber(matchResult.callerNumber)}</p>
                <p><strong>Direction:</strong> {matchResult.direction}</p>
                <p><strong>Timestamp:</strong> {matchResult.timestamp}</p>
                <p><strong>URL:</strong> {matchResult.url}</p>
                <div className="flex gap-2 mt-2">
                  <Button size="sm" onClick={() => handlePlayRecording(matchResult)}>
                    <Play className="h-4 w-4 mr-1" />
                    Play in App
                  </Button>
                  <Button size="sm" variant="outline" onClick={() => window.open(matchResult.url, '_blank')}>
                    Test URL
                  </Button>
                  <Button size="sm" variant="outline" asChild>
                    <a href={matchResult.url} download>
                      <Download className="h-4 w-4 mr-1" />
                      Download
                    </a>
                  </Button>
                </div>

                {/* Alternative URL patterns to test */}
                <div className="mt-3 p-2 bg-gray-50 rounded border">
                  <p className="text-sm font-medium mb-2">Alternative URLs to test:</p>
                  <div className="space-y-1 text-xs">
                    <div>
                      <span className="font-medium">Current:</span>
                      <code className="ml-1 bg-white px-1 rounded">{matchResult.url}</code>
                    </div>
                    <div>
                      <span className="font-medium">Without date:</span>
                      <code className="ml-1 bg-white px-1 rounded">
                        {`https://123f-41-203-218-161.ngrok-free.app/api/recordings/${matchResult.filename}`}
                      </code>
                      <Button
                        size="sm"
                        variant="ghost"
                        className="ml-2 h-6 px-2 text-xs"
                        onClick={() => window.open(`https://123f-41-203-218-161.ngrok-free.app/api/recordings/${matchResult.filename}`, '_blank')}
                      >
                        Test
                      </Button>
                    </div>
                    <div>
                      <span className="font-medium">Direct file:</span>
                      <code className="ml-1 bg-white px-1 rounded">
                        {`https://123f-41-203-218-161.ngrok-free.app/${matchResult.filename}`}
                      </code>
                      <Button
                        size="sm"
                        variant="ghost"
                        className="ml-2 h-6 px-2 text-xs"
                        onClick={() => window.open(`https://123f-41-203-218-161.ngrok-free.app/${matchResult.filename}`, '_blank')}
                      >
                        Test
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Recordings List */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileAudio className="h-5 w-5" />
            Available Recordings ({recordings.length})
          </CardTitle>
        </CardHeader>
        <CardContent>
          {loading ? (
            <p className="text-center text-muted-foreground">Loading recordings...</p>
          ) : recordings.length === 0 ? (
            <p className="text-center text-muted-foreground">No recordings found</p>
          ) : (
            <div className="space-y-2 max-h-96 overflow-y-auto">
              {recordings.slice(0, 10).map((recording, index) => (
                <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                  <div className="flex-1 min-w-0">
                    <p className="font-medium truncate">{recording.filename}</p>
                    <p className="text-sm text-muted-foreground">
                      {formatPhoneNumber(recording.callerNumber)} • {recording.direction} • {recording.timestamp}
                    </p>
                  </div>
                  <div className="flex gap-2">
                    <Button size="sm" variant="outline" onClick={() => handlePlayRecording(recording)}>
                      <Play className="h-4 w-4" />
                    </Button>
                    <Button size="sm" variant="ghost" onClick={() => window.open(recording.url, '_blank')}>
                      Test URL
                    </Button>
                  </div>
                </div>
              ))}
              {recordings.length > 10 && (
                <p className="text-center text-sm text-muted-foreground">
                  ... and {recordings.length - 10} more recordings
                </p>
              )}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Enhanced Recording Player Dialog */}
      <Dialog open={showPlayer} onOpenChange={setShowPlayer}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>SIP Recording Player</DialogTitle>
          </DialogHeader>
          {selectedRecording && (
            <EnhancedCallRecordingPlayer
              recordingUrl={selectedRecording.url}
              callId={selectedRecording.callId}
              title={`Recording: ${selectedRecording.filename}`}
              description={`Caller: ${formatPhoneNumber(selectedRecording.callerNumber)} • Direction: ${selectedRecording.direction} • ${selectedRecording.timestamp}`}
              onError={(error) => {
                console.error('Recording player error:', error);
              }}
            />
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
}
