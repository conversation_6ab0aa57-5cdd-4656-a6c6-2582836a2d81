"use client";

import { useState, useEffect } from "react";
import {
  PhoneIncoming,
  PhoneMissed,
  PhoneOff,
  Clock,
  Search,
  Calendar,
  Mic,
  FileAudio,
  MessageSquare
} from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Textarea } from "@/components/ui/textarea";
import { CallLog } from "@/lib/services/socket-service";
import { formatPhoneNumber, formatDate, formatTime, formatDuration } from "@/lib/utils/format";
import { useCallHandler } from "@/lib/hooks/useCallHandler";
import { EnhancedCallRecordingPlayer } from "./EnhancedCallRecordingPlayer";
import { useAuditLogger } from "@/lib/hooks/useAuditLogger";
import { useSipRecordings } from "@/lib/hooks/useSipRecordings";

interface CallHistoryProps {
  calls: CallLog[];
}

// Generate a realistic SIP call ID
const generateSipCallId = (): string => {
  // Format: SIP-yyyyMMdd-HHmmss-xxxx where xxxx is a random 4-digit number
  const now = new Date();
  const year = now.getFullYear();
  const month = String(now.getMonth() + 1).padStart(2, '0');
  const day = String(now.getDate()).padStart(2, '0');
  const hours = String(now.getHours()).padStart(2, '0');
  const minutes = String(now.getMinutes()).padStart(2, '0');
  const seconds = String(now.getSeconds()).padStart(2, '0');
  const random = Math.floor(Math.random() * 10000).toString().padStart(4, '0');

  return `SIP-${year}${month}${day}-${hours}${minutes}${seconds}-${random}`;
};

export function CallHistory({ calls }: CallHistoryProps) {
  const { addNotesToCall } = useCallHandler();
  const { logCallEvent } = useAuditLogger();
  const { recordings, loading: recordingsLoading, error: recordingsError, getRecordingForCall, hasRecording } = useSipRecordings();
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedCall, setSelectedCall] = useState<CallLog | null>(null);
  const [notes, setNotes] = useState("");
  const [showRecordingPlayer, setShowRecordingPlayer] = useState(false);

  // Filter calls based on search query
  const filteredCalls = calls.filter(call => {
    const query = searchQuery.toLowerCase();
    return (
      call.callerNumber.toLowerCase().includes(query) ||
      call.channelNumber.toLowerCase().includes(query) ||
      (call.notes && call.notes.toLowerCase().includes(query))
    );
  });

  // Debug: Log calls
  useEffect(() => {
    console.log("CallHistory component received calls:", calls);
  }, [calls]);

  // Group calls by date
  const callsByDate = filteredCalls.reduce((acc, call) => {
    const date = new Date(call.timestamp).toDateString();
    if (!acc[date]) {
      acc[date] = [];
    }
    acc[date].push(call);
    return acc;
  }, {} as Record<string, CallLog[]>);

  // Sort dates in descending order
  const sortedDates = Object.keys(callsByDate).sort((a, b) => {
    return new Date(b).getTime() - new Date(a).getTime();
  });

  // Handle save notes
  const handleSaveNotes = () => {
    if (!selectedCall) return;

    addNotesToCall(selectedCall.id, notes);
    setSelectedCall(null);
    setNotes("");
  };

  // Handle open call details
  const handleOpenCallDetails = (call: CallLog) => {
    setSelectedCall(call);
    setNotes(call.notes || "");
  };

  // Render call status icon
  const renderStatusIcon = (status: string) => {
    switch (status) {
      case 'answered':
        return <PhoneIncoming className="size-4 text-green-500" />;
      case 'missed':
        return <PhoneMissed className="size-4 text-red-500" />;
      case 'ended':
        return <PhoneOff className="size-4 text-blue-500" />;
      default:
        return <Clock className="size-4 text-gray-500" />;
    }
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <CardTitle>Call History</CardTitle>
            <CardDescription>View and manage your call history</CardDescription>
          </div>
          <div className="relative w-full sm:w-64">
            <Search className="absolute left-2.5 top-2.5 size-4 text-muted-foreground" />
            <Input
              placeholder="Search calls..."
              className="pl-8"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
        </div>
      </CardHeader>
      <CardContent>
        {filteredCalls.length === 0 ? (
          <div className="flex flex-col items-center justify-center py-8">
            <div className="w-12 h-12 rounded-full bg-muted flex items-center justify-center mb-4">
              <PhoneOff className="h-6 w-6 text-muted-foreground" />
            </div>
            <h3 className="text-lg font-medium">No calls found</h3>
            <p className="text-sm text-muted-foreground mt-1">
              {searchQuery ? "Try a different search term" : "Your call history will appear here"}
            </p>
          </div>
        ) : (
          <div className="space-y-6">
            {sortedDates.map(date => (
              <div key={date} className="space-y-2">
                <div className="flex items-center gap-2">
                  <Calendar className="size-4 text-muted-foreground" />
                  <h3 className="text-sm font-medium">{formatDate(date)}</h3>
                </div>
                <div className="rounded-md border">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead className="w-[100px]">Time</TableHead>
                        <TableHead>Caller</TableHead>
                        <TableHead>Channel</TableHead>
                        <TableHead className="w-[100px]">Duration</TableHead>
                        <TableHead className="w-[100px]">Status</TableHead>
                        <TableHead className="w-[180px]">Call ID</TableHead>
                        <TableHead className="w-[100px]">Recording</TableHead>
                        <TableHead className="w-[100px]">Notes</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {callsByDate[date].map(call => (
                        <TableRow key={call.id} className="cursor-pointer hover:bg-muted/50" onClick={() => handleOpenCallDetails(call)}>
                          <TableCell className="font-medium">{formatTime(call.timestamp)}</TableCell>
                          <TableCell>{formatPhoneNumber(call.callerNumber)}</TableCell>
                          <TableCell>{formatPhoneNumber(call.channelNumber)}</TableCell>
                          <TableCell>
                            {call.duration ? formatDuration(call.duration) : '-'}
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center gap-1">
                              {renderStatusIcon(call.status)}
                              <span className="text-xs capitalize">{call.status}</span>
                            </div>
                          </TableCell>
                          <TableCell>
                            <span className="text-xs font-mono">
                              {call.callId || generateSipCallId()}
                            </span>
                          </TableCell>
                          <TableCell>
                            {hasRecording(call) ? (
                              <Button
                                variant="ghost"
                                size="sm"
                                className="h-8 w-8 p-0"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  setSelectedCall(call);
                                  setShowRecordingPlayer(true);

                                  // Get the SIP recording for audit logging
                                  const sipRecording = getRecordingForCall(call);

                                  // Log recording access to audit logs
                                  logCallEvent('CALL_RECORDING_ACCESS', call.callId || call.id, {
                                    callerNumber: call.callerNumber,
                                    timestamp: call.timestamp,
                                    recordingUrl: sipRecording?.url,
                                    sipFilename: sipRecording?.filename
                                  });
                                }}
                                disabled={recordingsLoading}
                              >
                                <FileAudio className="size-4" />
                              </Button>
                            ) : (
                              <span className="text-xs text-muted-foreground">
                                {recordingsLoading ? '...' : '-'}
                              </span>
                            )}
                          </TableCell>
                          <TableCell>
                            {call.notes ? (
                              <Badge variant="outline" className="text-xs">
                                <MessageSquare className="h-3 w-3 mr-1" />
                                Notes
                              </Badge>
                            ) : (
                              <span className="text-xs text-muted-foreground">-</span>
                            )}
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              </div>
            ))}
          </div>
        )}
      </CardContent>

      {/* Call Details Dialog */}
      <Dialog open={!!selectedCall} onOpenChange={(open) => !open && setSelectedCall(null)}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Call Details</DialogTitle>
            <DialogDescription>
              View and manage call information
            </DialogDescription>
          </DialogHeader>

          {selectedCall && (
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <p className="text-sm font-medium">Caller</p>
                  <p className="text-sm">{formatPhoneNumber(selectedCall.callerNumber)}</p>
                </div>
                <div>
                  <p className="text-sm font-medium">Channel</p>
                  <p className="text-sm">{formatPhoneNumber(selectedCall.channelNumber)}</p>
                </div>
                <div>
                  <p className="text-sm font-medium">Date & Time</p>
                  <p className="text-sm">{formatDate(selectedCall.timestamp)} {formatTime(selectedCall.timestamp)}</p>
                </div>
                <div>
                  <p className="text-sm font-medium">Duration</p>
                  <p className="text-sm">{selectedCall.duration ? formatDuration(selectedCall.duration) : '-'}</p>
                </div>
                <div>
                  <p className="text-sm font-medium">Status</p>
                  <div className="flex items-center gap-1">
                    {renderStatusIcon(selectedCall.status)}
                    <span className="text-sm capitalize">{selectedCall.status}</span>
                  </div>
                </div>
                <div>
                  <p className="text-sm font-medium">Call ID</p>
                  <p className="text-sm font-mono">
                    {selectedCall.callId || generateSipCallId()}
                  </p>
                </div>
                <div>
                  <p className="text-sm font-medium">Recording</p>
                  {selectedCall.recordingUrl ? (
                    <Button variant="outline" size="sm" className="mt-1" asChild>
                      <a href={selectedCall.recordingUrl} target="_blank" rel="noopener noreferrer">
                        <FileAudio className="size-4 mr-2" />
                        Play Recording
                      </a>
                    </Button>
                  ) : (
                    <p className="text-sm text-muted-foreground">No recording available</p>
                  )}
                </div>
              </div>

              <div className="space-y-2">
                <p className="text-sm font-medium">Notes</p>
                <Textarea
                  placeholder="Add notes about this call..."
                  value={notes}
                  onChange={(e) => setNotes(e.target.value)}
                  className="min-h-[100px]"
                />
              </div>
            </div>
          )}

          <DialogFooter>
            <Button variant="outline" onClick={() => setSelectedCall(null)}>
              Cancel
            </Button>
            <Button onClick={handleSaveNotes}>
              Save Notes
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Recording Player Dialog */}
      <Dialog open={showRecordingPlayer} onOpenChange={setShowRecordingPlayer}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Call Recording</DialogTitle>
            <DialogDescription>
              {selectedCall && (
                <span>
                  Call from {formatPhoneNumber(selectedCall.callerNumber)} on {formatDate(selectedCall.timestamp)}
                </span>
              )}
            </DialogDescription>
          </DialogHeader>
          {selectedCall && (() => {
            const sipRecording = getRecordingForCall(selectedCall);
            return sipRecording ? (
              <EnhancedCallRecordingPlayer
                recordingUrl={sipRecording.url}
                callId={sipRecording.callId || selectedCall.callId || selectedCall.id}
                title={`Call with ${selectedCall.callerName || formatPhoneNumber(selectedCall.callerNumber)}`}
                description={`Duration: ${selectedCall.duration ? formatDuration(selectedCall.duration) : 'Unknown'} • SIP File: ${sipRecording.filename}`}
                onError={(error) => {
                  console.error('Recording player error:', error);
                }}
              />
            ) : (
              <div className="p-4 text-center text-muted-foreground">
                <FileAudio className="h-12 w-12 mx-auto mb-2 opacity-50" />
                <p>No recording found for this call</p>
                {recordingsError && (
                  <p className="text-sm text-destructive mt-2">
                    Error loading recordings: {recordingsError}
                  </p>
                )}
              </div>
            );
          })()}
        </DialogContent>
      </Dialog>
    </Card>
  );
}
