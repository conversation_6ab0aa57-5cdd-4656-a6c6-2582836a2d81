"use client";

import { useState, useEffect } from "react";
import { useTickets } from "@/lib/hooks/useTickets";
import { formatDate, formatTime } from "@/lib/utils/format";
import { Contact } from "@/lib/api/types";
import { Ticket } from "@/lib/api/types";
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardHeader, 
  CardTitle 
} from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCaption,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Loader2, ExternalLink } from "lucide-react";

interface CallerPreviousTicketsProps {
  contactId?: string;
  limit?: number;
}

export function CallerPreviousTickets({ contactId, limit = 5 }: CallerPreviousTicketsProps) {
  const [isLoading, setIsLoading] = useState(true);
  const [previousTickets, setPreviousTickets] = useState<Ticket[]>([]);
  const { tickets: allTickets } = useTickets();

  // Fetch previous tickets for this contact
  useEffect(() => {
    if (!contactId) {
      setIsLoading(false);
      setPreviousTickets([]);
      return;
    }

    setIsLoading(true);

    // Filter tickets by contact ID
    const filteredTickets = allTickets
      .filter(ticket => ticket.contact?.id === contactId)
      .sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime())
      .slice(0, limit);

    setPreviousTickets(filteredTickets);
    setIsLoading(false);
  }, [contactId, allTickets, limit]);

  // Get status badge color
  const getStatusBadge = (status: string) => {
    switch (status.toLowerCase()) {
      case 'open':
        return <Badge variant="outline" className="bg-blue-100 text-blue-800">Open</Badge>;
      case 'in_progress':
        return <Badge variant="outline" className="bg-yellow-100 text-yellow-800">In Progress</Badge>;
      case 'resolved':
        return <Badge variant="outline" className="bg-green-100 text-green-800">Resolved</Badge>;
      case 'closed':
        return <Badge variant="outline" className="bg-gray-100 text-gray-800">Closed</Badge>;
      case 'pending':
        return <Badge variant="outline" className="bg-orange-100 text-orange-800">Pending</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  // Get priority badge color
  const getPriorityBadge = (priority: string) => {
    switch (priority.toLowerCase()) {
      case 'low':
        return <Badge variant="outline" className="bg-green-100 text-green-800">Low</Badge>;
      case 'medium':
        return <Badge variant="outline" className="bg-blue-100 text-blue-800">Medium</Badge>;
      case 'high':
        return <Badge variant="outline" className="bg-orange-100 text-orange-800">High</Badge>;
      case 'urgent':
        return <Badge variant="outline" className="bg-red-100 text-red-800">Urgent</Badge>;
      default:
        return <Badge variant="outline">{priority}</Badge>;
    }
  };

  if (!contactId) {
    return null;
  }

  return (
    <Card className="w-full">
      <CardHeader className="pb-2">
        <CardTitle className="text-lg">Previous Tickets</CardTitle>
        <CardDescription>
          {previousTickets.length > 0 
            ? `Showing ${previousTickets.length} previous tickets for this caller` 
            : "No previous tickets found for this caller"}
        </CardDescription>
      </CardHeader>
      <CardContent>
        {isLoading ? (
          <div className="flex justify-center items-center py-4">
            <Loader2 className="h-6 w-6 animate-spin text-muted-foreground" />
          </div>
        ) : previousTickets.length === 0 ? (
          <div className="text-center py-4 text-muted-foreground">
            No previous tickets found
          </div>
        ) : (
          <div className="overflow-auto max-h-[300px]">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Date</TableHead>
                  <TableHead>Description</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Priority</TableHead>
                  <TableHead>Agent</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {previousTickets.map((ticket) => (
                  <TableRow key={ticket.id}>
                    <TableCell className="whitespace-nowrap">
                      <div className="font-medium">{formatDate(ticket.created_at)}</div>
                      <div className="text-xs text-muted-foreground">{formatTime(ticket.created_at)}</div>
                    </TableCell>
                    <TableCell>
                      <div className="font-medium truncate max-w-[200px]">{ticket.description}</div>
                      <div className="text-xs text-muted-foreground">
                        {ticket.category?.name}
                        {ticket.product && ` • ${ticket.product.name}`}
                      </div>
                    </TableCell>
                    <TableCell>{getStatusBadge(ticket.status)}</TableCell>
                    <TableCell>{getPriorityBadge(ticket.priority)}</TableCell>
                    <TableCell>
                      {ticket.assigned_to ? (
                        <div className="text-sm">
                          {ticket.assigned_to.first_name} {ticket.assigned_to.last_name}
                        </div>
                      ) : (
                        <div className="text-xs text-muted-foreground">Unassigned</div>
                      )}
                    </TableCell>
                    <TableCell className="text-right">
                      <Button variant="ghost" size="sm" asChild>
                        <a href={`/dashboard/tickets/${ticket.id}`} target="_blank" rel="noopener noreferrer">
                          <ExternalLink className="size-4" />
                        </a>
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
