"use client";

import { useState, useEffect } from "react";
import { 
  AlertCircle, 
  Info, 
  CheckCircle, 
  Search, 
  RefreshCw,
  Download,
  Filter
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { formatDate, formatTime } from "@/lib/utils/format";

// Log types
type LogLevel = 'info' | 'warning' | 'error' | 'success';

interface SystemLog {
  id: string;
  timestamp: string;
  level: LogLevel;
  message: string;
  source: string;
  details?: string;
}

// Mock logs data
const generateMockLogs = (count: number): SystemLog[] => {
  const sources = ['call-service', 'socket-service', 'recording-service', 'auth-service', 'system'];
  const levels: LogLevel[] = ['info', 'warning', 'error', 'success'];
  
  const infoMessages = [
    'Socket connection established',
    'Call received from {phone}',
    'Call answered by agent',
    'Recording started for call {id}',
    'Recording stopped for call {id}',
    'User logged in',
    'System startup complete',
  ];
  
  const warningMessages = [
    'Socket connection attempt failed, retrying...',
    'Call quality degraded',
    'Recording quality suboptimal',
    'High system load detected',
    'API response slow',
  ];
  
  const errorMessages = [
    'Socket connection failed after multiple attempts',
    'Failed to establish call',
    'Recording failed to start',
    'Database connection error',
    'API request failed',
  ];
  
  const successMessages = [
    'Call completed successfully',
    'Recording saved successfully',
    'User session validated',
    'Database backup completed',
    'System update applied',
  ];
  
  const logs: SystemLog[] = [];
  
  for (let i = 0; i < count; i++) {
    const level = levels[Math.floor(Math.random() * levels.length)];
    const source = sources[Math.floor(Math.random() * sources.length)];
    
    let message = '';
    switch (level) {
      case 'info':
        message = infoMessages[Math.floor(Math.random() * infoMessages.length)];
        break;
      case 'warning':
        message = warningMessages[Math.floor(Math.random() * warningMessages.length)];
        break;
      case 'error':
        message = errorMessages[Math.floor(Math.random() * errorMessages.length)];
        break;
      case 'success':
        message = successMessages[Math.floor(Math.random() * successMessages.length)];
        break;
    }
    
    // Replace placeholders
    message = message.replace('{phone}', `+1${Math.floor(Math.random() * 9000000000) + 1000000000}`);
    message = message.replace('{id}', Math.random().toString(36).substring(2, 11));
    
    // Generate random timestamp within the last 24 hours
    const timestamp = new Date(Date.now() - Math.floor(Math.random() * 24 * 60 * 60 * 1000)).toISOString();
    
    logs.push({
      id: Math.random().toString(36).substring(2, 11),
      timestamp,
      level,
      message,
      source,
      details: level === 'error' ? JSON.stringify({
        error: 'Error details would go here',
        code: Math.floor(Math.random() * 500) + 100,
        stack: 'Stack trace would go here',
      }, null, 2) : undefined,
    });
  }
  
  // Sort by timestamp (newest first)
  return logs.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());
};

export function SystemLogs() {
  const [logs, setLogs] = useState<SystemLog[]>([]);
  const [searchQuery, setSearchQuery] = useState("");
  const [levelFilter, setLevelFilter] = useState<string>("all");
  const [sourceFilter, setSourceFilter] = useState<string>("all");
  const [isLoading, setIsLoading] = useState(false);
  
  // Load logs on mount
  useEffect(() => {
    loadLogs();
  }, []);
  
  // Load logs
  const loadLogs = () => {
    setIsLoading(true);
    
    // Simulate API call
    setTimeout(() => {
      const mockLogs = generateMockLogs(50);
      setLogs(mockLogs);
      setIsLoading(false);
    }, 500);
  };
  
  // Filter logs
  const filteredLogs = logs.filter(log => {
    const matchesSearch = searchQuery === "" || 
      log.message.toLowerCase().includes(searchQuery.toLowerCase()) ||
      log.source.toLowerCase().includes(searchQuery.toLowerCase());
    
    const matchesLevel = levelFilter === "all" || log.level === levelFilter;
    const matchesSource = sourceFilter === "all" || log.source === sourceFilter;
    
    return matchesSearch && matchesLevel && matchesSource;
  });
  
  // Get unique sources for filter
  const sources = Array.from(new Set(logs.map(log => log.source)));
  
  // Render log level badge
  const renderLevelBadge = (level: LogLevel) => {
    switch (level) {
      case 'info':
        return <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200"><Info className="h-3 w-3 mr-1" /> Info</Badge>;
      case 'warning':
        return <Badge variant="outline" className="bg-yellow-50 text-yellow-700 border-yellow-200"><AlertCircle className="h-3 w-3 mr-1" /> Warning</Badge>;
      case 'error':
        return <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200"><AlertCircle className="h-3 w-3 mr-1" /> Error</Badge>;
      case 'success':
        return <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200"><CheckCircle className="h-3 w-3 mr-1" /> Success</Badge>;
    }
  };
  
  // Download logs as JSON
  const downloadLogs = () => {
    const dataStr = JSON.stringify(filteredLogs, null, 2);
    const dataUri = `data:application/json;charset=utf-8,${encodeURIComponent(dataStr)}`;
    
    const exportFileDefaultName = `system-logs-${new Date().toISOString().split('T')[0]}.json`;
    
    const linkElement = document.createElement('a');
    linkElement.setAttribute('href', dataUri);
    linkElement.setAttribute('download', exportFileDefaultName);
    linkElement.click();
  };
  
  return (
    <Card className="w-full">
      <CardHeader>
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <CardTitle>System Logs</CardTitle>
            <CardDescription>View system activity and troubleshoot issues</CardDescription>
          </div>
          <div className="flex flex-col sm:flex-row gap-2">
            <Button variant="outline" size="sm" onClick={loadLogs} disabled={isLoading}>
              <RefreshCw className={`size-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
              Refresh
            </Button>
            <Button variant="outline" size="sm" onClick={downloadLogs}>
              <Download className="size-4 mr-2" />
              Export
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div className="flex flex-col sm:flex-row gap-2">
            <div className="relative flex-1">
              <Search className="absolute left-2.5 top-2.5 size-4 text-muted-foreground" />
              <Input
                placeholder="Search logs..."
                className="pl-8"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
            <div className="flex gap-2">
              <Select value={levelFilter} onValueChange={setLevelFilter}>
                <SelectTrigger className="w-[130px]">
                  <Filter className="size-4 mr-2" />
                  <SelectValue placeholder="Level" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Levels</SelectItem>
                  <SelectItem value="info">Info</SelectItem>
                  <SelectItem value="warning">Warning</SelectItem>
                  <SelectItem value="error">Error</SelectItem>
                  <SelectItem value="success">Success</SelectItem>
                </SelectContent>
              </Select>
              
              <Select value={sourceFilter} onValueChange={setSourceFilter}>
                <SelectTrigger className="w-[150px]">
                  <Filter className="size-4 mr-2" />
                  <SelectValue placeholder="Source" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Sources</SelectItem>
                  {sources.map(source => (
                    <SelectItem key={source} value={source}>{source}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
          
          {isLoading ? (
            <div className="flex justify-center py-8">
              <RefreshCw className="h-8 w-8 animate-spin text-muted-foreground" />
            </div>
          ) : filteredLogs.length === 0 ? (
            <div className="flex flex-col items-center justify-center py-8">
              <div className="w-12 h-12 rounded-full bg-muted flex items-center justify-center mb-4">
                <AlertCircle className="h-6 w-6 text-muted-foreground" />
              </div>
              <h3 className="text-lg font-medium">No logs found</h3>
              <p className="text-sm text-muted-foreground mt-1">
                {searchQuery || levelFilter !== "all" || sourceFilter !== "all" 
                  ? "Try adjusting your filters" 
                  : "System logs will appear here"}
              </p>
            </div>
          ) : (
            <div className="space-y-2 max-h-[600px] overflow-auto">
              {filteredLogs.map(log => (
                <div key={log.id} className="p-3 rounded-md border">
                  <div className="flex flex-col sm:flex-row sm:items-center gap-2 mb-2">
                    <div className="flex items-center gap-2">
                      {renderLevelBadge(log.level)}
                      <Badge variant="outline">{log.source}</Badge>
                    </div>
                    <div className="text-xs text-muted-foreground sm:ml-auto">
                      {formatDate(log.timestamp)} {formatTime(log.timestamp)}
                    </div>
                  </div>
                  <p className="text-sm">{log.message}</p>
                  {log.details && (
                    <div className="mt-2 p-2 bg-muted rounded-md">
                      <pre className="text-xs overflow-auto max-h-[100px]">{log.details}</pre>
                    </div>
                  )}
                </div>
              ))}
            </div>
          )}
        </div>
      </CardContent>
      <CardFooter className="flex justify-between">
        <p className="text-sm text-muted-foreground">
          Showing {filteredLogs.length} of {logs.length} logs
        </p>
      </CardFooter>
    </Card>
  );
}
