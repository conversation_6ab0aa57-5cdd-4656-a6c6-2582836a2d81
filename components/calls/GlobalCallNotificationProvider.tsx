"use client";

import React, { createContext, useContext, useState, useEffect, useMemo, useRef } from 'react';
import { useCallHandler } from '@/lib/hooks/useCallHandler';
import { IncomingCall } from '@/lib/services/socket-service';
import { ActiveCall } from './ActiveCall';
import { ErrorBoundary } from '@/components/ErrorBoundary';

// Context type
interface GlobalCallNotificationContextType {
  currentCall: IncomingCall | null;
  isCallActive: boolean;
  showNotification: boolean;
}

// Create context
const GlobalCallNotificationContext = createContext<GlobalCallNotificationContextType | undefined>(undefined);

// Hook to use the context
export function useGlobalCallNotification() {
  const context = useContext(GlobalCallNotificationContext);
  if (context === undefined) {
    throw new Error('useGlobalCallNotification must be used within a GlobalCallNotificationProvider');
  }
  return context;
}

// Simple notification component
function SimpleIncomingCallNotification({ call }: { call: IncomingCall }) {
  const { answerCall } = useCallHandler();
  const [isConnecting, setIsConnecting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Reset connecting state when call status changes to answered
  useEffect(() => {
    if (call.status === 'answered') {
      setIsConnecting(false);
      setError(null);
    }
  }, [call.status]);

  const handleAnswer = async () => {
    console.log('📞 Answering call with channelId:', call.channelId);
    setIsConnecting(true);
    setError(null);

    if (call.channelId) {
      try {
        const answerEndpoint = process.env.NEXT_PUBLIC_SIP_ANSWER_ENDPOINT || 'https://7a11-41-203-218-161.ngrok-free.app/api/answer';

        const response = await fetch(answerEndpoint, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            channelId: call.channelId
          })
        });

        if (response.ok) {
          console.log('✅ SIP answer request sent successfully');
          console.log('⏳ Waiting for SIP server to confirm call is bridged...');
          // Note: UI will update when SIP server sends call_bridged WebSocket event
          // Keep connecting state until WebSocket event is received
        } else {
          const errorText = await response.text();
          console.error('❌ SIP answer request failed:', response.status, errorText);
          setError(`Failed to answer call: ${response.status}`);
          setIsConnecting(false);
        }
      } catch (error) {
        console.error('❌ Error sending SIP answer request:', error);
        setError('Network error - please try again');
        setIsConnecting(false);
      }
    } else {
      // Handle simulated calls - immediate state update
      answerCall();
      setIsConnecting(false);
    }
  };



  return (
    <div className="fixed inset-0 z-[9999] bg-black/50 flex items-center justify-center p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-md w-full p-6">
        <h2 className="text-xl font-bold mb-4">
          {isConnecting ? 'Connecting Call...' : 'Incoming Call'}
        </h2>
        <p className="mb-2">From: <strong>{call.callerNumber}</strong></p>
        <p className="mb-4 text-sm text-gray-600">Channel: {call.channelId}</p>

        {error && (
          <div className="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
            {error}
          </div>
        )}

        {isConnecting ? (
          <div className="flex items-center justify-center py-4">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-600"></div>
            <span className="ml-3 text-gray-600">Bridging call with SIP server...</span>
          </div>
        ) : (
          <div className="flex justify-center">
            <button
              className="bg-green-600 hover:bg-green-700 text-white px-8 py-3 rounded-lg disabled:opacity-50 font-medium"
              onClick={handleAnswer}
              disabled={isConnecting}
            >
              Answer Call
            </button>
          </div>
        )}
      </div>
    </div>
  );
}

// Provider component
export function GlobalCallNotificationProvider({ children }: { children: React.ReactNode }) {
  try {
    const { currentCall } = useCallHandler();

  // Determine what to show based on call status (memoized to prevent excessive re-renders)
  const shouldShowNotification = useMemo(() => {
    const show = currentCall?.status?.toLowerCase() === 'ringing';
    console.log('🔍 Notification Check:', {
      currentCallStatus: currentCall?.status,
      shouldShow: show,
      currentCall
    });
    return show;
  }, [currentCall?.status]);

  const shouldShowActiveCall = useMemo(() => {
    const show = currentCall?.status === 'answered';
    console.log('🔍 Active Call Check:', {
      currentCallStatus: currentCall?.status,
      shouldShow: show
    });
    return show;
  }, [currentCall?.status]);

  // Debounced logging to prevent spam
  const lastLogRef = useRef<string>('');
  useEffect(() => {
    const logData = {
      hasCurrentCall: !!currentCall,
      callStatus: currentCall?.status,
      shouldShowNotification,
      shouldShowActiveCall,
      channelId: currentCall?.channelId
    };

    const logString = JSON.stringify(logData);
    if (logString !== lastLogRef.current) {
      console.log('🔄 GlobalCallNotificationProvider:', logData);
      lastLogRef.current = logString;
    }
  }, [currentCall?.status, shouldShowNotification, shouldShowActiveCall, currentCall?.channelId]);

  const contextValue: GlobalCallNotificationContextType = useMemo(() => ({
    currentCall,
    isCallActive: shouldShowActiveCall,
    showNotification: shouldShowNotification,
  }), [currentCall, shouldShowActiveCall, shouldShowNotification]);

  return (
    <GlobalCallNotificationContext.Provider value={contextValue}>
      {children}

      {/* Incoming Call Notification */}
      {shouldShowNotification && currentCall && (
        <SimpleIncomingCallNotification call={currentCall} />
      )}

      {/* Active Call Interface */}
      {shouldShowActiveCall && currentCall && (
        <div className="fixed bottom-4 right-4 z-[9998]">
          {/* Validate call object before rendering ActiveCall */}
          {(currentCall.id || currentCall.channelId) ? (
            <ErrorBoundary
              fallback={
                <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-2 rounded max-w-sm">
                  <div className="font-semibold">Call Interface Error</div>
                  <div className="text-sm mt-1">Unable to display call interface</div>
                  <div className="text-xs mt-1 opacity-75">
                    Call ID: {currentCall.id || currentCall.channelId || 'Unknown'}
                  </div>
                </div>
              }
              onError={(error, errorInfo) => {
                console.error('ActiveCall component crashed:', error, errorInfo);
                console.error('Call object that caused crash:', currentCall);
              }}
            >
              <ActiveCall call={currentCall} />
            </ErrorBoundary>
          ) : (
            <div className="bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-2 rounded">
              Call data incomplete - missing ID
            </div>
          )}
        </div>
      )}
    </GlobalCallNotificationContext.Provider>
  );
  } catch (error) {
    console.error('GlobalCallNotificationProvider error:', error);
    // Return children without notification functionality to prevent app crash
    return <>{children}</>;
  }
}