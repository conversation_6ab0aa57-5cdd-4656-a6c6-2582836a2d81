"use client";

import { useState, useRef, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON> } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { IncomingCall } from "@/lib/services/socket-service";
import { formatPhoneNumber } from "@/lib/utils/format";
import { cn } from "@/lib/utils";
import { useCallHandler } from "@/lib/hooks/useCallHandler";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from "@/components/ui/dialog";
import { useToast } from "@/hooks/use-toast";
import Draggable from "react-draggable";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";

interface ActiveCallProps {
  call: IncomingCall;
}

export function ActiveCall({ call }: ActiveCallProps) {
  try {
    // Debug logging
    console.log('🎯 ActiveCall component rendering with call:', call);

    // Validate call object
    if (!call) {
      console.error('❌ ActiveCall: call prop is null/undefined');
      throw new Error('Call object is required');
    }

    if (!call.id && !call.channelId && !call.callId) {
      console.error('❌ ActiveCall: call missing all identifiers (id, channelId, callId):', call);
      throw new Error('Call object missing required identifiers');
    }

    // Validate required phone number
    if (!call.callerNumber) {
      console.error('❌ ActiveCall: call missing callerNumber:', call);
      throw new Error('Call object missing caller number');
    }

    const {
      endCall,
      isRecording,
      startRecording,
      stopRecording,
      callTimer,
      formatCallTimer
    } = useCallHandler();

    // Validate hook return values
    if (typeof callTimer !== 'number') {
      console.error('❌ ActiveCall: callTimer is not a number:', callTimer);
    }

    if (typeof formatCallTimer !== 'function') {
      console.error('❌ ActiveCall: formatCallTimer is not a function:', formatCallTimer);
    }

    // Safe phone number formatting
    const safeFormatPhoneNumber = (number: string | undefined | null): string => {
      try {
        return formatPhoneNumber(number);
      } catch (error) {
        console.error('Error formatting phone number:', error, 'Number:', number);
        return number || 'Unknown Number';
      }
    };

    // Prevent excessive re-renders
    const renderCountRef = useRef(0);
    useEffect(() => {
      renderCountRef.current += 1;
      if (renderCountRef.current > 100) {
        console.warn('⚠️ ActiveCall: Excessive re-renders detected, potential infinite loop');
        throw new Error('ActiveCall component re-rendered too many times');
      }
    });
  const [isCallEnded, setIsCallEnded] = useState(false);
  const [showEscalate, setShowEscalate] = useState(false);
  const [selectedCRM, setSelectedCRM] = useState("");
  const [note, setNote] = useState("");
  const [copied, setCopied] = useState(false);
  const { toast } = useToast();

  // Timer is now managed centrally by useCallHandler

  // Handle end call
  const handleEndCall = () => {
    endCall();
    setIsCallEnded(true);
  };

  // Handle toggle recording
  const handleToggleRecording = () => {
    if (isRecording) {
      stopRecording();
    } else {
      startRecording();
    }
  };

  const handleEscalate = () => {
    setShowEscalate(true);
  };

  const handleEscalateSubmit = () => {
    setShowEscalate(false);
    setSelectedCRM("");
    setNote("");
    toast({
      title: "Escalated!",
      description: `Ticket escalated to ${selectedCRM}.`,
      variant: "success",
    });
  };

  const generateSipCallId = () => `sip-${Math.random().toString(36).substring(2, 11)}`;
  const callId = (call as any).callId || generateSipCallId();

  const handleCopy = () => {
    navigator.clipboard.writeText(callId);
    setCopied(true);
    setTimeout(() => setCopied(false), 1200);
  };

  if (isCallEnded) return null;

  return (
    <Draggable handle=".active-call-drag-handle">
      <div className="fixed z-50 top-16 left-1/2 -translate-x-1/2 max-w-md w-full">
        <Card className="shadow-lg">
          <CardHeader className="pb-2 active-call-drag-handle cursor-move select-none">
            <div className="flex justify-between items-center">
              <CardTitle className="text-lg">Active Call</CardTitle>
              <Badge variant={(call?.status === 'answered') ? "default" : "secondary"}>
                {(call?.status === 'answered') ? 'In Progress' : (call?.status || 'Unknown')}
              </Badge>
            </div>
          </CardHeader>
          <CardContent className="pb-2">
            <div className="flex items-center gap-4">
              <div className="w-12 h-12 rounded-full bg-primary/10 flex items-center justify-center">
                <User className="h-6 w-6 text-primary" />
              </div>
              <div>
                <h3 className="font-medium">
                  {call?.callerName || safeFormatPhoneNumber(call?.callerNumber)}
                </h3>
                <p className="text-sm text-muted-foreground">
                  via {safeFormatPhoneNumber(call?.channelNumber || call?.destinationNumber)}
                </p>
                {call.productName && (
                  <p className="text-xs mt-1">
                    <span className="px-2 py-0.5 bg-blue-100 text-blue-800 rounded-full">
                      {call.productName}
                    </span>
                  </p>
                )}
                {call.callerId && (
                  <p className="text-xs mt-1">ID: {call.callerId}</p>
                )}
              </div>
              <div className="ml-auto text-right">
                <p className="text-sm font-medium">
                  {formatCallTimer ? formatCallTimer(callTimer || 0) : '00:00'}
                </p>
                <p className="text-xs text-muted-foreground">
                  {isRecording ? (
                    <span className="flex items-center gap-1 text-red-500">
                      <span className="relative flex h-2 w-2">
                        <span className="animate-ping absolute inline-flex h-full w-full rounded-full bg-red-400 opacity-75"></span>
                        <span className="relative inline-flex rounded-full h-2 w-2 bg-red-500"></span>
                      </span>
                      Recording
                    </span>
                  ) : 'Not recording'}
                </p>
              </div>
            </div>
            <div className="mt-1 text-xs flex items-center gap-2">
              <span className="text-muted-foreground">Call ID:</span>
              <span className="font-mono font-medium select-all">{callId}</span>
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button variant="ghost" size="icon" className="h-5 w-5 p-0" onClick={handleCopy} aria-label="Copy Call ID">
                      <Copy className="h-4 w-4" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>{copied ? "Copied!" : "Copy Call ID"}</TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </div>
          </CardContent>
          <CardFooter className="flex justify-center gap-4 pt-2">
            <Button
              variant="outline"
              size="icon"
              className={cn(
                "rounded-full w-10 h-10",
                isRecording && "bg-red-100 text-red-600 border-red-200"
              )}
              onClick={handleToggleRecording}
            >
              {isRecording ? <MicOff className="h-5 w-5" /> : <Mic className="h-5 w-5" />}
            </Button>
            <Button
              variant="destructive"
              size="icon"
              className="rounded-full w-10 h-10"
              onClick={handleEndCall}
            >
              <PhoneOff className="h-5 w-5" />
            </Button>
            <Button
              variant="secondary"
              size="icon"
              className="rounded-full w-10 h-10"
              onClick={handleEscalate}
              title="Escalate to CRM"
            >
              <span role="img" aria-label="escalate">🚀</span>
            </Button>
          </CardFooter>
        </Card>
        <Dialog open={showEscalate} onOpenChange={setShowEscalate}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Escalate to CRM</DialogTitle>
            </DialogHeader>
            <div className="space-y-4">
              <label className="block text-sm font-medium">Select CRM</label>
              <select
                className="w-full border rounded px-2 py-1"
                value={selectedCRM}
                onChange={e => setSelectedCRM(e.target.value)}
              >
                <option value="">-- Select --</option>
                <option value="Salesforce">Salesforce</option>
                <option value="HubSpot">HubSpot</option>
                <option value="Zoho">Zoho</option>
                <option value="Freshdesk">Freshdesk</option>
                <option value="Other">Other</option>
              </select>
              <label className="block text-sm font-medium">Note (optional)</label>
              <textarea
                className="w-full border rounded px-2 py-1"
                rows={2}
                value={note}
                onChange={e => setNote(e.target.value)}
                placeholder="Add a note for the CRM..."
              />
            </div>
            <DialogFooter>
              <Button
                onClick={handleEscalateSubmit}
                disabled={!selectedCRM}
              >
                Escalate
              </Button>
              <Button variant="ghost" onClick={() => setShowEscalate(false)}>
                Cancel
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>
    </Draggable>
  );
  } catch (error) {
    console.error('ActiveCall component error:', error);
    console.error('Call object that caused error:', call);

    // Return a more detailed fallback UI
    return (
      <div className="fixed bottom-4 right-4 bg-red-100 border border-red-400 text-red-700 px-4 py-2 rounded max-w-sm">
        <div className="font-semibold">Call Interface Error</div>
        <div className="text-sm mt-1">
          {error instanceof Error ? error.message : 'Unknown error occurred'}
        </div>
        <div className="text-xs mt-1 opacity-75">
          Call ID: {call?.id || call?.channelId || 'Unknown'}
        </div>
      </div>
    );
  }
}
