"use client";

import { useState, useEffect, use<PERSON>emo } from "react";
import { Phone, PhoneOff, User, Ticket, UserPlus, HelpCircle, Copy } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON>ooter, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { IncomingCall, CallLog, useCallStore } from "@/lib/services/socket-service";
import { formatPhoneNumber, formatRelativeTime } from "@/lib/utils/format";
import { cn } from "@/lib/utils";
import { useCallHandler } from "@/lib/hooks/useCallHandler";
import { useContacts } from "@/lib/hooks/useContacts";
import { useProducts } from "@/lib/hooks/useProducts";
import { useWebRTC } from "@/lib/hooks/useWebRTC";
import { FloatingTicketDialog } from "@/components/tickets/floating-ticket-dialog";
import { CreateContactDialog } from "@/components/contacts/create-contact-dialog";
import { CallerPreviousTickets } from "@/components/calls/CallerPreviousTickets";
import { ProductFAQsDialog } from "@/components/faqs/product-faqs-dialog";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Contact } from "@/lib/api/types";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";

interface EnhancedIncomingCallProps {
  call: IncomingCall;
}

export function EnhancedIncomingCall({ call: initialCall }: EnhancedIncomingCallProps) {
  const { answerCall, rejectCall, endCall, callHistory, currentCall } = useCallHandler();
  const { contacts } = useContacts();
  const { data: products = [] } = useProducts();
  const {
    isEnabled: webrtcEnabled,
    isRegistered: webrtcRegistered,
    incomingCall: webrtcIncomingCall,
    answerCall: answerWebRTCCall,
    rejectCall: rejectWebRTCCall,
    endCall: endWebRTCCall
  } = useWebRTC();
  const [timeElapsed, setTimeElapsed] = useState<string>("");
  const [isOpen, setIsOpen] = useState(true);
  const [isTicketDialogOpen, setIsTicketDialogOpen] = useState(false);
  const [isContactDialogOpen, setIsContactDialogOpen] = useState(false);
  const [isFAQsDialogOpen, setIsFAQsDialogOpen] = useState(false);
  const [matchedContact, setMatchedContact] = useState<Contact | null>(null);
  const [copied, setCopied] = useState(false);

  // Determine if this is a WebRTC call or simulated call (memoized)
  const isWebRTCCall = useMemo(() => {
    return webrtcEnabled && webrtcRegistered && webrtcIncomingCall &&
           (webrtcIncomingCall.remoteNumber === initialCall.callerNumber ||
            webrtcIncomingCall.id === initialCall.id);
  }, [webrtcEnabled, webrtcRegistered, webrtcIncomingCall, initialCall.callerNumber, initialCall.id]);

  // Use WebRTC call data if available, otherwise use simulated call data (memoized)
  const activeCall = useMemo(() => {
    return isWebRTCCall && webrtcIncomingCall ? {
      ...initialCall,
      id: webrtcIncomingCall.id,
      callerId: webrtcIncomingCall.id,
      status: webrtcIncomingCall.status as any
    } : initialCall;
  }, [isWebRTCCall, initialCall, webrtcIncomingCall]);

  // Function to get product ID by name
  const getProductIdByName = (productName?: string): string => {
    if (!productName || !products.length) return "";

    const product = products.find(p =>
      p.name.toLowerCase() === productName.toLowerCase()
    );

    return product?.id || "";
  };

  // Function to get a random product when none is provided
  const getRandomProduct = () => {
    if (!products.length) return null;
    const randomIndex = Math.floor(Math.random() * products.length);
    return products[randomIndex];
  };

  // Generate a realistic SIP call ID
  const generateSipCallId = (): string => {
    // Format: SIP-yyyyMMdd-HHmmss-xxxx where xxxx is a random 4-digit number
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const day = String(now.getDate()).padStart(2, '0');
    const hours = String(now.getHours()).padStart(2, '0');
    const minutes = String(now.getMinutes()).padStart(2, '0');
    const seconds = String(now.getSeconds()).padStart(2, '0');
    const random = Math.floor(Math.random() * 10000).toString().padStart(4, '0');

    return `SIP-${year}${month}${day}-${hours}${minutes}${seconds}-${random}`;
  };

  // Use the local state for UI, but sync with currentCall for operations
  const [call, setCall] = useState<IncomingCall>(initialCall);

  // State to track the selected product (either from call or randomly assigned)
  const [selectedProduct, setSelectedProduct] = useState<any>(null);

  // Sync local call state with currentCall when it changes
  useEffect(() => {
    if (currentCall && currentCall.id === initialCall.id && currentCall.status !== call.status) {
      setCall(currentCall);
    }
  }, [currentCall, initialCall.id, call.status]);

  // Find matching contact based on phone number
  useEffect(() => {
    if (contacts && contacts.length > 0 && call) {
      // Clean the phone number for comparison (remove spaces, dashes, etc.)
      const cleanCallerNumber = call.callerNumber.replace(/\D/g, '');

      // Find a contact with matching phone number
      const contact = contacts.find(c => {
        const cleanContactPhone = c.phone.replace(/\D/g, '');
        return cleanContactPhone.includes(cleanCallerNumber) ||
               cleanCallerNumber.includes(cleanContactPhone);
      });

      setMatchedContact(contact || null);
    }
  }, [contacts, call]);

  // Listen for call status changes
  useEffect(() => {
    // Create a handler for call answered events
    const handleCallAnswered = (e: any) => {
      console.log("Call answered event received", e.detail);
      if (e.detail && e.detail.id === call.id) {
        // Update the call status in our component state
        setCall(prevCall => ({
          ...prevCall,
          status: 'answered'
        }));
      }
    };

    // Create a handler for call ended events
    const handleCallEnded = (e: any) => {
      console.log("Call ended event received", e.detail);
      if (!e.detail || e.detail.id === call.id) {
        // Update the call status in our component state
        setCall(prevCall => ({
          ...prevCall,
          status: 'ended'
        }));

        // Close the dialogs
        setIsOpen(false);
        setIsTicketDialogOpen(false);
        setIsContactDialogOpen(false);
        setIsFAQsDialogOpen(false);
      }
    };

    // Create a handler for call rejected events
    const handleCallRejected = (e: any) => {
      console.log("Call rejected event received", e.detail);
      if (!e.detail || e.detail.id === call.id) {
        // Update the call status in our component state
        setCall(prevCall => ({
          ...prevCall,
          status: 'missed'
        }));

        // Close the dialogs
        setIsOpen(false);
        setIsTicketDialogOpen(false);
        setIsContactDialogOpen(false);
      }
    };

    // Add event listeners
    window.addEventListener('callAnswered', handleCallAnswered);
    window.addEventListener('callEnded', handleCallEnded);
    window.addEventListener('callRejected', handleCallRejected);

    // Cleanup
    return () => {
      window.removeEventListener('callAnswered', handleCallAnswered);
      window.removeEventListener('callEnded', handleCallEnded);
      window.removeEventListener('callRejected', handleCallRejected);
    };
  }, [call.id]); // Only depend on call.id, not the entire call object

  // Update time elapsed every second
  useEffect(() => {
    const updateTime = () => {
      const elapsed = formatRelativeTime(new Date(call.timestamp ?? Date.now()));
      setTimeElapsed(elapsed);
    };

    updateTime();
    const interval = setInterval(updateTime, 1000);

    return () => clearInterval(interval);
  }, [call.timestamp]);

  // Removed excessive debug logging to prevent console spam

  // Set product when component mounts or when products are loaded
  useEffect(() => {
    if (products.length > 0) {
      // If call has a product name, find the matching product
      if (call.productName) {
        const matchedProduct = products.find(p =>
          p.name.toLowerCase() === call.productName?.toLowerCase()
        );

        if (matchedProduct) {
          setSelectedProduct(matchedProduct);
        } else {
          // If product name doesn't match any product, get a random one
          const randomProduct = getRandomProduct();
          setSelectedProduct(randomProduct);

          // Update the call with the random product name
          setCall(prevCall => ({
            ...prevCall,
            productName: randomProduct?.name
          }));
        }
      } else {
        // If no product name, assign a random product
        const randomProduct = getRandomProduct();
        setSelectedProduct(randomProduct);

        // Update the call with the random product name
        setCall(prevCall => ({
          ...prevCall,
          productName: randomProduct?.name
        }));
      }
    }
  }, [products, call.productName]);

  // Handle new contact creation
  const handleContactCreated = (contactId: string) => {
    // Close contact dialog and update ticket dialog with new contact
    setIsContactDialogOpen(false);

    // Set the newly created contact ID for the ticket
    if (isTicketDialogOpen) {
      // The ticket dialog will pick up the new contact ID
      setMatchedContact({ id: contactId } as Contact);
    }
  };

  // Handle answering the call
  const handleAnswerCall = async () => {
    // First update the local call state immediately to ensure UI updates
    setCall(prevCall => ({
      ...prevCall,
      status: 'answered'
    }));

    // Handle different call types
    if (isWebRTCCall && webrtcIncomingCall) {
      console.log("Answering WebRTC call:", webrtcIncomingCall.id);
      answerWebRTCCall(webrtcIncomingCall.id);
    } else if (call.channelId) {
      // Handle real SIP call - send answer request to SIP server
      console.log("📞 Answering real SIP call with channelId:", call.channelId);
      try {
        const answerEndpoint = process.env.NEXT_PUBLIC_SIP_ANSWER_ENDPOINT || 'https://7a11-41-203-218-161.ngrok-free.app/api/answer';

        const response = await fetch(answerEndpoint, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            channelId: call.channelId
          })
        });

        if (response.ok) {
          console.log("✅ SIP answer request sent successfully");
        } else {
          console.error("❌ SIP answer request failed:", response.status, response.statusText);
        }
      } catch (error) {
        console.error("❌ Error sending SIP answer request:", error);
      }
    } else {
      // Handle simulated call
      console.log("📞 Answering simulated call");
      setTimeout(() => {
        answerCall();
      }, 0);
    }

    // Open the ticket dialog immediately
    setIsTicketDialogOpen(true);

    // If no matching contact, also open contact creation dialog
    if (!matchedContact) {
      setIsContactDialogOpen(true);
    }

    // Always open the FAQs dialog since we now always have a product
    setIsFAQsDialogOpen(true);

    // Dispatch a custom event to notify the global provider
    window.dispatchEvent(new CustomEvent('callAnswered', {
      detail: { ...call, status: 'answered' }
    }));

    console.log("Call answered, status updated to 'answered'");
  };

  // Handle rejecting/ending the call
  const handleRejectCall = () => {
    console.log("Ending call with status:", call.status);
    console.log("Current call from useCallHandler:", currentCall);

    // Close the incoming call dialog
    setIsOpen(false);

    // Close the ticket dialog if it's open
    setIsTicketDialogOpen(false);

    // Create a manual call log entry if needed
    const createManualCallLog = () => {
      // Calculate call duration
      const startTime = new Date(call.timestamp ?? Date.now()).getTime();
      const endTime = Date.now();
      const duration = Math.floor((endTime - startTime) / 1000); // Duration in seconds

      // Create a call log entry with a unique ID
      const callLog: CallLog = {
        ...call,
        // Ensure unique ID by appending a timestamp
        id: `${call.id}-${Date.now()}`,
        status: call.status === 'answered' ? 'ended' : 'missed',
        duration: call.status === 'answered' ? duration : undefined,
        agentId: 'manual-entry',
      };

      console.log("Manually adding call to history:", callLog);

      // Directly add to call history using the store
      useCallStore.getState().addToCallHistory(callLog);

      // Also dispatch a custom event to add this call to history
      const event = new CustomEvent('manualCallLog', { detail: callLog });
      window.dispatchEvent(event);

      // Force a cleanup
      window.dispatchEvent(new CustomEvent('callEnded', {
        detail: { id: call.id, status: call.status === 'answered' ? 'ended' : 'missed' }
      }));
    };

    // Always create a manual call log entry first to ensure the call is recorded
    createManualCallLog();

    // Dispatch custom events to notify the global provider
    if (call.status === 'answered') {
      window.dispatchEvent(new CustomEvent('callEnded', {
        detail: { ...call, status: 'ended' }
      }));
    } else {
      window.dispatchEvent(new CustomEvent('callRejected', {
        detail: { ...call, status: 'missed' }
      }));
    }

    // Handle WebRTC call or simulated call
    if (isWebRTCCall && webrtcIncomingCall) {
      if (call.status === 'answered') {
        console.log("Ending WebRTC call:", webrtcIncomingCall.id);
        endWebRTCCall(webrtcIncomingCall.id);
      } else {
        console.log("Rejecting WebRTC call:", webrtcIncomingCall.id);
        rejectWebRTCCall(webrtcIncomingCall.id);
      }
    } else {
      // Handle simulated call
      if (call.status === 'answered') {
        console.log("Using endCall() for answered simulated call");
        try {
          endCall();
        } catch (error) {
          console.error("Error ending call:", error);
        }
      } else {
        console.log("Using rejectCall() for unanswered simulated call");
        try {
          rejectCall();
        } catch (error) {
          console.error("Error rejecting call:", error);
        }
      }
    }
  };

  // Copy Call ID to clipboard
  const callId = (call as any).callId || generateSipCallId();
  const handleCopy = () => {
    navigator.clipboard.writeText(callId);
    setCopied(true);
    setTimeout(() => setCopied(false), 1200);
  };

  return (
    <>
      {/* For ringing calls, show a modal dialog */}
      {call.status === 'ringing' && (
        <Dialog open={isOpen} onOpenChange={setIsOpen}>
          <DialogContent className="sm:max-w-md">
            <DialogHeader>
              <DialogTitle className="text-center">Incoming Call</DialogTitle>
            </DialogHeader>
            <div className="flex flex-col items-center justify-center p-6 space-y-4">
              <div className="w-20 h-20 rounded-full bg-primary/10 flex items-center justify-center animate-pulse">
                <User className="h-10 w-10 text-primary" />
              </div>
              <div className="text-center">
                {matchedContact ? (
                  <>
                    <h3 className="text-xl font-semibold">{matchedContact.first_name} {matchedContact.last_name}</h3>
                    <p className="text-sm font-medium">{formatPhoneNumber(call.callerNumber)}</p>
                    <p className="text-xs font-medium px-2 py-0.5 bg-blue-100 text-blue-800 rounded-full inline-block">
                      {call.productName || (selectedProduct && selectedProduct.name) || "Loading product..."}
                    </p>
                  </>
                ) : (
                  <>
                    <h3 className="text-xl font-semibold">{formatPhoneNumber(call.callerNumber)}</h3>
                    <p className="text-sm text-muted-foreground">Unknown Caller</p>
                    <p className="text-xs font-medium px-2 py-0.5 bg-blue-100 text-blue-800 rounded-full inline-block mt-1">
                      {call.productName || (selectedProduct && selectedProduct.name) || "Loading product..."}
                    </p>
                  </>
                )}
                <p className="text-xs text-muted-foreground mt-1">via {formatPhoneNumber(call.channelNumber)}</p>
                <p className="text-xs text-muted-foreground">{timeElapsed}</p>
                <div className="mt-1 text-xs flex items-center gap-2">
                  <span className="text-muted-foreground">Call ID:</span>
                  <span className="font-mono font-medium select-all">{callId}</span>
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Button variant="ghost" size="icon" className="h-5 w-5 p-0" onClick={handleCopy} aria-label="Copy Call ID">
                          <Copy className="h-4 w-4" />
                        </Button>
                      </TooltipTrigger>
                      <TooltipContent>{copied ? "Copied!" : "Copy Call ID"}</TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </div>
              </div>
            </div>
            <CardFooter className="flex justify-center gap-8 pb-6">
              <Button
                variant="default"
                size="lg"
                className="rounded-full w-14 h-14 p-0 bg-green-600 hover:bg-green-700"
                onClick={handleAnswerCall}
              >
                <Phone className="h-6 w-6" />
              </Button>
            </CardFooter>
          </DialogContent>
        </Dialog>
      )}

      {/* For answered calls, show a floating card */}
      {call.status === 'answered' && (
        <>
          <div className="fixed bottom-4 left-4 z-50 w-80 bg-card border rounded-lg shadow-lg">
            <div className="p-4 border-b">
              <div className="flex justify-between items-center">
                <h3 className="text-sm font-semibold">Active Call</h3>
                <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                  <span className="relative flex h-2 w-2 mr-1">
                    <span className="animate-ping absolute inline-flex h-full w-full rounded-full bg-green-400 opacity-75"></span>
                    <span className="relative inline-flex rounded-full h-2 w-2 bg-green-500"></span>
                  </span>
                  In Progress
                </span>
              </div>
            </div>
            <div className="p-4">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 rounded-full bg-primary/10 flex items-center justify-center">
                  <User className="h-5 w-5 text-primary" />
                </div>
                <div>
                  {matchedContact ? (
                    <p className="font-medium text-sm">{matchedContact.first_name} {matchedContact.last_name}</p>
                  ) : (
                    <p className="font-medium text-sm">{formatPhoneNumber(call.callerNumber)}</p>
                  )}
                  <p className="text-xs text-muted-foreground">{timeElapsed}</p>
                </div>
              </div>

              <div className="mt-2 flex items-center gap-2">
                <span className="px-2 py-0.5 bg-blue-100 text-blue-800 rounded-full text-xs">
                  {call.productName || (selectedProduct && selectedProduct.name) || "Loading product..."}
                </span>
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-6 p-0 text-xs text-blue-600"
                  onClick={() => setIsFAQsDialogOpen(true)}
                >
                  <HelpCircle className="h-3 w-3 mr-1" />
                  FAQs
                </Button>
              </div>

              {call.callerId && (
                <div className="mt-1 text-xs">
                  <span className="text-muted-foreground">Caller ID: </span>
                  <span className="font-medium">{call.callerId}</span>
                </div>
              )}

              {/* Display Call ID (SIP server ID) */}
              <div className="mt-1 text-xs flex items-center gap-2">
                <span className="text-muted-foreground">Call ID:</span>
                <span className="font-mono font-medium select-all">{callId}</span>
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button variant="ghost" size="icon" className="h-5 w-5 p-0" onClick={handleCopy} aria-label="Copy Call ID">
                        <Copy className="h-4 w-4" />
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>{copied ? "Copied!" : "Copy Call ID"}</TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </div>
            </div>
            <div className="p-3 bg-muted/50 flex justify-center">
              <Button
                variant="destructive"
                size="sm"
                className="w-full"
                onClick={handleRejectCall}
              >
                <PhoneOff className="size-4 mr-2" />
                End Call
              </Button>
            </div>
          </div>

          {/* Show previous tickets for this caller */}
          {matchedContact && (
            <div className="fixed bottom-4 right-4 z-50 w-[600px] max-h-[400px] overflow-auto">
              <CallerPreviousTickets contactId={matchedContact.id} limit={5} />
            </div>
          )}
        </>
      )}

      {/* Floating Ticket Creation Dialog */}
      <FloatingTicketDialog
        open={isTicketDialogOpen}
        onOpenChange={(open) => {
          setIsTicketDialogOpen(open);
          // Don't end the call when the ticket dialog is closed
          // This allows the agent to continue the call after creating a ticket
        }}
        initialContactId={matchedContact?.id}
        initialChannelType="phone"
        initialProductId={selectedProduct?.id || getProductIdByName(call.productName)}
        createAnother={true}
      />

      {/* Contact Creation Dialog */}
      <CreateContactDialog
        open={isContactDialogOpen}
        onOpenChange={setIsContactDialogOpen}
        initialPhoneNumber={call.callerNumber}
        onSuccess={handleContactCreated}
      />

      {/* Product FAQs Dialog */}
      <ProductFAQsDialog
        open={isFAQsDialogOpen}
        onOpenChange={setIsFAQsDialogOpen}
        productId={selectedProduct?.id || getProductIdByName(call.productName)}
        productName={call.productName || (selectedProduct && selectedProduct.name)}
      />
    </>
  );
}
