"use client";

import React, { useState, useCallback } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { 
  Di<PERSON>, 
  DialogContent, 
  DialogHeader, 
  DialogTitle, 
  DialogFooter 
} from '@/components/ui/dialog';
import {
  Phone,
  PhoneCall,
  Delete,
  X,
  Loader2
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { useToast } from '@/components/ui/use-toast';

interface DialPadProps {
  isOpen: boolean;
  onClose: () => void;
  onCall: (phoneNumber: string) => Promise<void>;
  isDialing?: boolean;
}

export function DialPad({ isOpen, onClose, onCall, isDialing = false }: DialPadProps) {
  const [phoneNumber, setPhoneNumber] = useState('');
  const { toast } = useToast();

  // Dial pad button configuration
  const dialPadButtons = [
    { number: '1', letters: '' },
    { number: '2', letters: 'ABC' },
    { number: '3', letters: 'DEF' },
    { number: '4', letters: 'GHI' },
    { number: '5', letters: 'JKL' },
    { number: '6', letters: 'MNO' },
    { number: '7', letters: 'PQRS' },
    { number: '8', letters: 'TUV' },
    { number: '9', letters: 'WXYZ' },
    { number: '*', letters: '' },
    { number: '0', letters: '+' },
    { number: '#', letters: '' },
  ];

  // Add digit to phone number
  const addDigit = useCallback((digit: string) => {
    if (phoneNumber.length < 20) { // Reasonable limit
      setPhoneNumber(prev => prev + digit);
    }
  }, [phoneNumber.length]);

  // Remove last digit
  const removeDigit = useCallback(() => {
    setPhoneNumber(prev => prev.slice(0, -1));
  }, []);

  // Clear all digits
  const clearNumber = useCallback(() => {
    setPhoneNumber('');
  }, []);

  // Validate phone number
  const isValidPhoneNumber = useCallback((number: string): boolean => {
    // Remove all non-digit characters except +
    const cleaned = number.replace(/[^\d+]/g, '');
    
    // Must have at least 7 digits and not more than 15 (international standard)
    const digitCount = cleaned.replace(/[^\d]/g, '').length;
    return digitCount >= 7 && digitCount <= 15;
  }, []);

  // Handle call initiation
  const handleCall = useCallback(async () => {
    if (!phoneNumber.trim()) {
      toast({
        title: "Invalid Number",
        description: "Please enter a phone number to dial.",
        variant: "destructive",
      });
      return;
    }

    if (!isValidPhoneNumber(phoneNumber)) {
      toast({
        title: "Invalid Phone Number",
        description: "Please enter a valid phone number (7-15 digits).",
        variant: "destructive",
      });
      return;
    }

    try {
      await onCall(phoneNumber);
      // Clear the number after successful call initiation
      setPhoneNumber('');
    } catch (error) {
      console.error('Error initiating call:', error);
      toast({
        title: "Call Failed",
        description: "Failed to initiate the call. Please try again.",
        variant: "destructive",
      });
    }
  }, [phoneNumber, isValidPhoneNumber, onCall, toast]);

  // Handle modal close
  const handleClose = useCallback(() => {
    if (!isDialing) {
      setPhoneNumber('');
      onClose();
    }
  }, [isDialing, onClose]);

  // Handle keyboard input
  const handleKeyDown = useCallback((event: React.KeyboardEvent) => {
    const { key } = event;
    
    if (key >= '0' && key <= '9') {
      addDigit(key);
    } else if (key === '*' || key === '#') {
      addDigit(key);
    } else if (key === 'Backspace') {
      removeDigit();
    } else if (key === 'Enter') {
      event.preventDefault();
      handleCall();
    } else if (key === 'Escape') {
      handleClose();
    }
  }, [addDigit, removeDigit, handleCall, handleClose]);

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent 
        className="sm:max-w-md"
        onKeyDown={handleKeyDown}
        tabIndex={-1}
      >
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Phone className="h-5 w-5" />
            Dial Number
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          {/* Phone Number Display */}
          <div className="relative">
            <Input
              value={phoneNumber}
              onChange={(e) => setPhoneNumber(e.target.value)}
              placeholder="Enter phone number"
              className="text-center text-lg font-mono tracking-wider pr-10"
              disabled={isDialing}
              autoFocus
            />
            {phoneNumber && (
              <Button
                variant="ghost"
                size="sm"
                className="absolute right-1 top-1/2 -translate-y-1/2 h-8 w-8 p-0"
                onClick={clearNumber}
                disabled={isDialing}
              >
                <X className="h-4 w-4" />
              </Button>
            )}
          </div>

          {/* Dial Pad Grid */}
          <div className="grid grid-cols-3 gap-3">
            {dialPadButtons.map((button) => (
              <Button
                key={button.number}
                variant="outline"
                className={cn(
                  "h-14 flex flex-col items-center justify-center",
                  "hover:bg-primary hover:text-primary-foreground",
                  "transition-colors duration-200"
                )}
                onClick={() => addDigit(button.number)}
                disabled={isDialing}
              >
                <span className="text-lg font-semibold">{button.number}</span>
                {button.letters && (
                  <span className="text-xs text-muted-foreground">
                    {button.letters}
                  </span>
                )}
              </Button>
            ))}
          </div>

          {/* Action Buttons */}
          <div className="flex gap-3 mt-6">
            <Button
              variant="outline"
              className="flex-1"
              onClick={removeDigit}
              disabled={!phoneNumber || isDialing}
            >
              <Delete className="h-4 w-4 mr-2" />
              Delete
            </Button>
            
            <Button
              className="flex-1 bg-green-600 hover:bg-green-700"
              onClick={handleCall}
              disabled={!phoneNumber || isDialing}
            >
              {isDialing ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Dialing...
                </>
              ) : (
                <>
                  <PhoneCall className="h-4 w-4 mr-2" />
                  Call
                </>
              )}
            </Button>
          </div>
        </div>

        <DialogFooter>
          <Button 
            variant="ghost" 
            onClick={handleClose}
            disabled={isDialing}
          >
            {isDialing ? 'Dialing...' : 'Cancel'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
