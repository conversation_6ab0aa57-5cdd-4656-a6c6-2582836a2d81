"use client";

import React, { useState, useCallback } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Phone } from 'lucide-react';
import { DialPad } from './DialPad';
import { useCallHandler } from '@/lib/hooks/useCallHandler';
import { useToast } from '@/components/ui/use-toast';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';

export function GlobalDialButton() {
  const [isDialPadOpen, setIsDialPadOpen] = useState(false);
  const [isDialing, setIsDialing] = useState(false);
  const { currentCall, makeOutboundCall } = useCallHandler();
  const { toast } = useToast();

  // Check if we can make a call (no active call)
  const canMakeCall = !currentCall || currentCall.status === 'ended';

  // Handle opening dial pad
  const handleOpenDialPad = useCallback(() => {
    if (!canMakeCall) {
      toast({
        title: "Call in Progress",
        description: "Please end the current call before making a new one.",
        variant: "destructive",
      });
      return;
    }
    setIsDialPadOpen(true);
  }, [canMakeCall, toast]);

  // Handle closing dial pad
  const handleCloseDialPad = useCallback(() => {
    if (!isDialing) {
      setIsDialPadOpen(false);
    }
  }, [isDialing]);

  // Handle making outbound call
  const handleMakeCall = useCallback(async (phoneNumber: string) => {
    try {
      setIsDialing(true);
      
      // Call the outbound call function from useCallHandler
      await makeOutboundCall(phoneNumber);
      
      // Close dial pad on successful call initiation
      setIsDialPadOpen(false);
      
      toast({
        title: "Call Initiated",
        description: `Calling ${phoneNumber}...`,
        variant: "default",
      });
      
    } catch (error) {
      console.error('Error making outbound call:', error);
      toast({
        title: "Call Failed",
        description: "Failed to initiate the call. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsDialing(false);
    }
  }, [makeOutboundCall, toast]);

  return (
    <>
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <Button
              variant={canMakeCall ? "default" : "secondary"}
              size="sm"
              onClick={handleOpenDialPad}
              disabled={!canMakeCall}
              className="relative"
            >
              <Phone className="h-4 w-4 mr-2" />
              Dial
              {!canMakeCall && (
                <span className="absolute -top-1 -right-1 h-3 w-3 bg-red-500 rounded-full animate-pulse" />
              )}
            </Button>
          </TooltipTrigger>
          <TooltipContent>
            {canMakeCall 
              ? "Make an outbound call" 
              : "End current call to dial new number"
            }
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>

      <DialPad
        isOpen={isDialPadOpen}
        onClose={handleCloseDialPad}
        onCall={handleMakeCall}
        isDialing={isDialing}
      />
    </>
  );
}
