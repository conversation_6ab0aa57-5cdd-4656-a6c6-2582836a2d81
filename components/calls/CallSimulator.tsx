"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Phone, PhoneIncoming } from "lucide-react";
import { useContacts } from "@/lib/hooks/useContacts";
import { useSocketService } from "@/lib/hooks/useSocketService";
import { useCallHandler } from "@/lib/hooks/useCallHandler";
import { EnhancedIncomingCall } from "./EnhancedIncomingCall";
import { IncomingCall } from "@/lib/services/socket-service";
import { formatPhoneNumber } from "@/lib/utils/format";

// Define custom event types
interface IncomingCallEvent extends CustomEvent {
  detail: IncomingCall;
}

interface CallEndEvent extends CustomEvent {
  detail: {
    id: string;
    status: string;
  };
}

export function CallSimulator() {
  const { contacts } = useContacts();
  const { simulateIncomingCall } = useSocketService();
  const { callHistory } = useCallHandler();
  const [activeCall, setActiveCall] = useState<IncomingCall | null>(null);
  const [isSimulating, setIsSimulating] = useState(false);

  // Listen for real SIP calls and simulated calls
  useEffect(() => {
    const handleIncomingCall = (event: Event) => {
      const callEvent = event as IncomingCallEvent;
      setActiveCall(callEvent.detail);
      setIsSimulating(true);
    };

    const handleCallEnded = (event: Event) => {
      const endEvent = event as CallEndEvent;
      setActiveCall(prevCall => {
        // Only clear if this is the same call or no specific call ID
        if (!prevCall || !endEvent.detail || endEvent.detail.id === prevCall.id) {
          setIsSimulating(false);
          return null;
        }
        return prevCall;
      });
    };

    // Subscribe to events
    window.addEventListener("incomingCall", handleIncomingCall as EventListener);
    window.addEventListener("callEnded", handleCallEnded as EventListener);
    window.addEventListener("callRejected", handleCallEnded as EventListener);

    return () => {
      window.removeEventListener("incomingCall", handleIncomingCall as EventListener);
      window.removeEventListener("callEnded", handleCallEnded as EventListener);
      window.removeEventListener("callRejected", handleCallEnded as EventListener);
    };
  }, []); // Remove activeCall dependency to prevent infinite loop

  // Simulate a call for testing
  const handleSimulateCall = () => {
    if (isSimulating) {
      return;
    }

    const randomContact = contacts[Math.floor(Math.random() * contacts.length)];
    if (!randomContact) {
      return;
    }

    simulateIncomingCall({
      callerNumber: randomContact.phone,
      callerName: randomContact.first_name + " " + randomContact.last_name,
      channelNumber: "+254800123456", // Default channel number
      timestamp: new Date().toISOString()
    });
    
    setIsSimulating(true);
  };

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-medium">Call Testing</h3>
        <Button
          onClick={handleSimulateCall}
          disabled={isSimulating}
        >
          {isSimulating ? 'Call in Progress...' : 'Simulate Call from Contact'}
        </Button>
      </div>

      {/* Enhanced Incoming Call Component */}
      {activeCall && (
        <EnhancedIncomingCall call={activeCall} />
      )}
    </div>
  );
}
