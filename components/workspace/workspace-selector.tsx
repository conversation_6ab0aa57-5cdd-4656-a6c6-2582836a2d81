"use client";

import React, { useEffect } from 'react';
import { Check, ChevronDown, Building2, Loader2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Badge } from '@/components/ui/badge';
import { useWorkspace } from '@/lib/stores/workspace-store';
import { useAuth } from '@/lib/hooks/useAuth';
import { WorkspaceContext } from '@/lib/types/dashboard';

interface WorkspaceSelectorProps {
  className?: string;
  showLabel?: boolean;
}

export function WorkspaceSelector({ className, showLabel = true }: WorkspaceSelectorProps) {
  const { user } = useAuth();
  const {
    currentWorkspace,
    availableWorkspaces,
    isLoading,
    isInitialized,
    switchWorkspace,
    initializeWorkspaces,
  } = useWorkspace();

  // Initialize workspaces when component mounts
  useEffect(() => {
    console.log('WorkspaceSelector useEffect:', {
      userId: user?.id,
      isInitialized,
      availableWorkspaces: availableWorkspaces.length,
      currentWorkspace: currentWorkspace?.name
    });
    
    if (user?.id && !isInitialized) {
      console.log('Initializing workspaces for user:', user.id);
      initializeWorkspaces(user.id);
    }
  }, [user?.id, isInitialized, initializeWorkspaces]);

  const handleWorkspaceSwitch = async (workspace: WorkspaceContext) => {
    if (workspace.id === currentWorkspace?.id) return;
    
    const success = await switchWorkspace(workspace.id);
    if (success) {
      // Optional: Show success toast
      console.log(`Switched to ${workspace.name} workspace`);
    } else {
      // Optional: Show error toast
      console.error(`Failed to switch to ${workspace.name} workspace`);
    }
  };

  // Show loading state if not initialized
  if (!isInitialized) {
    return (
      <div className={`flex items-center gap-2 ${className}`}>
        {showLabel && (
          <span className="text-sm font-medium text-muted-foreground hidden md:block">
            Workspace:
          </span>
        )}
        <Button
          variant="outline"
          className="flex items-center gap-2 min-w-[180px] justify-between"
          disabled
        >
          <div className="flex items-center gap-2">
            <Building2 className="h-4 w-4" />
            <span className="truncate">Loading workspaces...</span>
          </div>
          <Loader2 className="h-4 w-4 animate-spin" />
        </Button>
      </div>
    );
  }

  // Show message if no workspaces available
  if (availableWorkspaces.length === 0) {
    return (
      <div className={`flex items-center gap-2 ${className}`}>
        {showLabel && (
          <span className="text-sm font-medium text-muted-foreground hidden md:block">
            Workspace:
          </span>
        )}
        <Button
          variant="outline"
          className="flex items-center gap-2 min-w-[180px] justify-between"
          disabled
        >
          <div className="flex items-center gap-2">
            <Building2 className="h-4 w-4" />
            <span className="truncate">No workspaces available</span>
          </div>
        </Button>
      </div>
    );
  }

  return (
    <div className={`flex items-center gap-2 ${className}`}>
      {showLabel && (
        <span className="text-sm font-medium text-muted-foreground hidden md:block">
          Workspace:
        </span>
      )}
      
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button
            variant="outline"
            className="flex items-center gap-2 min-w-[180px] justify-between"
            disabled={isLoading}
          >
            <div className="flex items-center gap-2">
              <Building2 className="h-4 w-4" />
              <span className="truncate">
                {currentWorkspace?.name || 'Select Workspace'}
              </span>
            </div>
            {isLoading ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <ChevronDown className="h-4 w-4" />
            )}
          </Button>
        </DropdownMenuTrigger>
        
        <DropdownMenuContent align="end" className="w-[250px]">
          <DropdownMenuLabel className="flex items-center justify-between">
            <span>Available Workspaces</span>
            <Badge variant="secondary" className="text-xs">
              {availableWorkspaces.length}
            </Badge>
          </DropdownMenuLabel>
          
          <DropdownMenuSeparator />
          
          {availableWorkspaces.map((workspace) => (
            <DropdownMenuItem
              key={workspace.id}
              onClick={() => handleWorkspaceSwitch(workspace)}
              className="flex items-center justify-between cursor-pointer"
              disabled={!workspace.isActive}
            >
              <div className="flex flex-col gap-1">
                <div className="flex items-center gap-2">
                  <Building2 className="h-4 w-4" />
                  <span className="font-medium">{workspace.name}</span>
                  {currentWorkspace?.id === workspace.id && (
                    <Check className="h-4 w-4 text-primary" />
                  )}
                </div>
                <span className="text-xs text-muted-foreground">
                  {workspace.productName}
                </span>
              </div>
              
              {!workspace.isActive && (
                <Badge variant="destructive" className="text-xs">
                  Inactive
                </Badge>
              )}
            </DropdownMenuItem>
          ))}
          
          <DropdownMenuSeparator />
          
          <DropdownMenuItem className="text-xs text-muted-foreground" disabled>
            <div className="flex flex-col gap-1">
              <span>Current Permissions:</span>
              <div className="flex flex-wrap gap-1">
                {currentWorkspace?.permissions.slice(0, 3).map((permission) => (
                  <Badge key={permission} variant="outline" className="text-xs">
                    {permission.split(':')[1] || permission}
                  </Badge>
                ))}
                {currentWorkspace && currentWorkspace.permissions.length > 3 && (
                  <Badge variant="outline" className="text-xs">
                    +{currentWorkspace.permissions.length - 3} more
                  </Badge>
                )}
              </div>
            </div>
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );
}

// Workspace indicator for mobile/compact views
export function WorkspaceIndicator() {
  const { currentWorkspace } = useWorkspace();
  
  if (!currentWorkspace) return null;
  
  return (
    <div className="flex items-center gap-2 px-3 py-1 bg-muted rounded-md">
      <Building2 className="h-3 w-3" />
      <span className="text-xs font-medium truncate">
        {currentWorkspace.name}
      </span>
    </div>
  );
}

// Hook to check workspace permissions
export function useWorkspacePermissions() {
  const { currentWorkspace, hasWorkspacePermission } = useWorkspace();
  
  return {
    currentWorkspace,
    hasPermission: hasWorkspacePermission,
    permissions: currentWorkspace?.permissions || [],
  };
}