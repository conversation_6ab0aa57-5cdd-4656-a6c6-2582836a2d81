# Inbound Calls Module Features Implementation

This document outlines the implementation of the two key features for the call center application's inbound calls module, with a unified architecture for both simulated and real SIP calls.

## Unified Call Interface Architecture

### Overview
The system now uses a **unified architecture** where both simulated calls and real SIP calls use the same `EnhancedIncomingCall` component, providing a consistent, high-quality user experience.

### Component Architecture Decision

**Previous Architecture (Fixed):**
- ❌ Real SIP calls used `IncomingCallNotification` (basic modal)
- ❌ Simulated calls were supposed to use `EnhancedIncomingCall` but didn't
- ❌ Inconsistent UI/UX between simulation and production

**New Unified Architecture:**
- ✅ **Both** real SIP calls and simulated calls use `EnhancedIncomingCall`
- ✅ Consistent, rich interface for all incoming calls
- ✅ Same features available in simulation and production

## Feature 1: Global Incoming Call Notifications

### Overview
A global incoming call notification system that appears as an overlay/modal on any page of the application, triggered via socket connections. Now uses the enhanced interface for superior user experience.

### Implementation Details

#### Components Created/Updated:
1. **GlobalCallNotificationProvider** (`components/calls/GlobalCallNotificationProvider.tsx`)
   - Context provider that manages global call state
   - Listens for socket events and custom events
   - Renders call notifications as overlays with highest z-index (9999)
   - **Now uses EnhancedIncomingCall** for superior interface
   - Integrated into the main application providers

2. **EnhancedIncomingCall** (`components/calls/EnhancedIncomingCall.tsx`)
   - **Primary component for ALL incoming calls** (both real and simulated)
   - Rich interface with contact matching and display
   - Automatic ticket creation dialog integration
   - Product FAQs integration with floating dialogs
   - Previous tickets display for known contacts
   - Professional call status indicators
   - SIP call ID generation and display

3. **CallSimulator** (`components/calls/CallSimulator.tsx`)
   - **Now properly uses EnhancedIncomingCall** for realistic simulation
   - Provides high-quality simulation experience
   - Consistent with real call interface

#### Key Features:
- **Unified Interface**: Same rich interface for both real SIP calls and simulated calls
- **Global Coverage**: Notifications appear on any page of the application
- **Socket Integration**: Uses existing socket service for real-time communication
- **Contact Intelligence**:
  - Automatic contact matching by phone number
  - Display of caller information when available
  - Contact creation dialog for unknown callers
- **Product Integration**:
  - Product assignment (from call data or random assignment)
  - Product-specific FAQs dialog
  - Product information display
- **Ticket Integration**:
  - Automatic ticket creation dialog when call is answered
  - Pre-filled with caller and product information
- **Call History**:
  - Display of previous tickets for known contacts
  - Professional call status tracking
- **Professional Features**:
  - SIP call ID generation and display
  - Call duration tracking
  - Rich visual design with animations
- **High Z-Index**: Ensures notifications appear above all other UI elements
- **Role-Based Access**: Respects existing permission system

#### Usage:
```typescript
// The provider is automatically included in the app
// Test incoming calls with the "Test Call" button on /calls page
// Or programmatically:
import { simulateIncomingCall } from "@/lib/services/socket-service";
simulateIncomingCall();
```

## Feature 2: Call Recording Playback System

### Overview
A robust audio player component with SIP format conversion capabilities for playing back recorded calls.

### Implementation Details

#### Components Created:
1. **EnhancedCallRecordingPlayer** (`components/calls/EnhancedCallRecordingPlayer.tsx`)
   - Advanced audio player with full controls
   - Supports play, pause, seek, volume, speed adjustment
   - Download functionality for recordings
   - Error handling and loading states

2. **SIP Conversion Service** (`lib/services/sip-conversion-service.ts`)
   - Flexible format conversion system
   - Supports common SIP formats: G.711, G.729, G.722, GSM, Speex, etc.
   - Automatic format detection
   - Conversion to web-playable formats (MP3, WAV, OGG)

#### Key Features:
- **Format Support**: Handles various SIP audio formats
- **Web Compatibility**: Converts to browser-playable formats
- **Advanced Controls**:
  - Play/pause, skip forward/backward (10s)
  - Volume control with mute
  - Playback speed (0.5x to 2x)
  - Seek bar with time display
  - Download functionality
- **Error Handling**: Graceful handling of conversion failures
- **Loading States**: Shows conversion progress
- **Audit Integration**: Logs recording access events

#### Supported SIP Formats:
- G.711 μ-law (PCMU) - Common in North America
- G.711 A-law (PCMA) - Common in Europe  
- G.729 - Low bitrate codec
- G.722 - Wideband audio codec
- GSM 06.10 - Mobile codec
- Speex - Open source codec
- Opus - Modern codec (web-playable)
- WAV - Uncompressed audio (web-playable)
- Raw PCM audio data

#### Integration Points:
1. **Call History Table**: "Play Recording" button opens enhanced player
2. **Audit Logs**: Records when agents access call recordings
3. **Role-Based Access**: Agents can only access their own recordings (unless supervisor+)

### Usage Examples

#### Playing a Recording:
```typescript
import { EnhancedCallRecordingPlayer } from "@/components/calls/EnhancedCallRecordingPlayer";

<EnhancedCallRecordingPlayer
  recordingUrl="https://example.com/recording.g711"
  callId="SIP-20241229-143022-1234"
  title="Call with Customer"
  description="Duration: 5m 32s"
  onError={(error) => console.error(error)}
/>
```

#### SIP Conversion:
```typescript
import { sipConversionService } from "@/lib/services/sip-conversion-service";

const result = await sipConversionService.convertRecording(
  "https://example.com/recording.g729",
  {
    targetFormat: 'mp3',
    quality: 'medium',
    normalize: true,
    removeNoise: true
  }
);

if (result.success) {
  // Use result.convertedUrl for playback
}
```

## Technical Architecture

### Socket Integration
- Uses existing `socket-service.ts` for real-time communication
- Supports both actual socket events and simulation for development
- Event types: `INCOMING_CALL`, `CALL_ANSWERED`, `CALL_REJECTED`, `CALL_ENDED`

### State Management
- Global call state managed via Zustand store in socket service
- Context providers for UI state management
- Audit logging integrated throughout

### Role-Based Access Control
- Respects existing permission system
- Agents can only access their own call recordings
- Supervisors and above can access all recordings
- Audit trail for all recording access

### Error Handling
- Graceful degradation for unsupported formats
- Retry mechanisms for failed conversions
- User-friendly error messages
- Fallback to original URLs when conversion fails

## Configuration

### Environment Variables
```env
# SIP Conversion API (optional)
NEXT_PUBLIC_SIP_CONVERSION_API=/api/convert-recording
NEXT_PUBLIC_SIP_CONVERSION_API_KEY=your-api-key

# Socket Service
NEXT_PUBLIC_SIP_WS_URL=http://localhost:8080
```

### Development Mode
- Simulated conversion with mock delays
- Sample audio files for testing
- Debug logging enabled

### Production Setup
1. Deploy SIP conversion backend service
2. Configure environment variables
3. Add actual SIP recording samples
4. Test with real SIP hardware

## Testing

### Test Incoming Calls:
1. Navigate to `/calls` page
2. Click "Test Call" button
3. Call notification should appear globally
4. Answer call to see integrated ticket/FAQ modals

### Test Recording Playback:
1. Navigate to call history
2. Click recording icon for any call with recording
3. Enhanced player should open with full controls
4. Test various playback features

## Future Enhancements

1. **Real-time Transcription**: Add speech-to-text for live calls
2. **Call Analytics**: Sentiment analysis and keyword detection
3. **Advanced Filtering**: Search recordings by content
4. **Batch Operations**: Bulk download/conversion of recordings
5. **Integration**: CRM and ticketing system integration
6. **Mobile Support**: Responsive design for mobile agents

## Dependencies Added

- Enhanced existing socket service
- Integrated with existing UI components
- Uses established patterns for modals and providers
- Maintains compatibility with React 19 and Next.js 15

## Audit Trail

All call-related actions are logged to the audit system:
- `CALL_RECEIVED`: When an inbound call is received
- `CALL_ANSWERED`: When a call is answered
- `CALL_REJECTED`: When a call is rejected
- `CALL_RECORDING_ACCESS`: When a recording is accessed
- Agent status changes are also tracked

This implementation provides a comprehensive solution for both global call notifications and call recording playback, following the established patterns and maintaining consistency with the existing codebase.
