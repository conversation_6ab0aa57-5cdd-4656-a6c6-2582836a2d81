import { generateMockContactAnalytics } from '@/lib/hooks/useContactAnalytics';
import { exportContacts } from '@/lib/utils/contact-export';
import { Contact, ContactCategory, ContactStatus, PreferredContactMethod } from '@/lib/api/types';

// Mock contact data for testing
const mockContacts: Contact[] = [
  {
    id: '1',
    first_name: '<PERSON>',
    last_name: '<PERSON><PERSON>',
    email: '<EMAIL>',
    phone: '+1234567890',
    company: 'Test Company',
    address: '123 Test St',
    city: 'Test City',
    country: 'Test Country',
    notes: 'Test notes',
    preferred_contact_method: PreferredContactMethod.EMAIL,
    status: ContactStatus.ACTIVE,
    category: ContactCategory.CUSTOMER,
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z',
    created_by: 'test-user',
  },
  {
    id: '2',
    first_name: '<PERSON>',
    last_name: '<PERSON>',
    email: '<EMAIL>',
    phone: '+1234567891',
    company: 'Another Company',
    address: '456 Another St',
    city: 'Another City',
    country: 'Another Country',
    notes: 'Another notes',
    preferred_contact_method: PreferredContactMethod.PHONE,
    status: ContactStatus.ACTIVE,
    category: ContactCategory.LEAD,
    created_at: '2024-01-02T00:00:00Z',
    updated_at: '2024-01-02T00:00:00Z',
    created_by: 'test-user',
  },
];

describe('Contact Analytics', () => {
  describe('generateMockContactAnalytics', () => {
    it('should generate analytics data with correct structure', () => {
      const params = {
        startDate: '2024-01-01',
        endDate: '2024-01-31',
      };

      const analytics = generateMockContactAnalytics(params);

      expect(analytics).toHaveProperty('metrics');
      expect(analytics).toHaveProperty('trends');
      expect(analytics).toHaveProperty('productBreakdown');
      expect(analytics).toHaveProperty('categoryBreakdown');

      expect(analytics.metrics).toHaveProperty('totalContacts');
      expect(analytics.metrics).toHaveProperty('newContacts');
      expect(analytics.metrics).toHaveProperty('growthRate');
      expect(analytics.metrics).toHaveProperty('previousPeriodTotal');

      expect(Array.isArray(analytics.trends)).toBe(true);
      expect(Array.isArray(analytics.productBreakdown)).toBe(true);
      expect(Array.isArray(analytics.categoryBreakdown)).toBe(true);
    });

    it('should generate trends data within date range', () => {
      const params = {
        startDate: '2024-01-01',
        endDate: '2024-01-05',
      };

      const analytics = generateMockContactAnalytics(params);
      
      expect(analytics.trends.length).toBeGreaterThan(0);
      expect(analytics.trends.length).toBeLessThanOrEqual(5);

      analytics.trends.forEach(trend => {
        expect(trend).toHaveProperty('date');
        expect(trend).toHaveProperty('count');
        expect(typeof trend.count).toBe('number');
        expect(trend.count).toBeGreaterThanOrEqual(0);
      });
    });

    it('should calculate growth rate correctly', () => {
      const params = {
        startDate: '2024-01-01',
        endDate: '2024-01-31',
      };

      const analytics = generateMockContactAnalytics(params);
      
      if (analytics.metrics.previousPeriodTotal > 0) {
        const expectedGrowthRate = 
          ((analytics.metrics.totalContacts - analytics.metrics.previousPeriodTotal) / 
           analytics.metrics.previousPeriodTotal) * 100;
        
        expect(Math.abs(analytics.metrics.growthRate - expectedGrowthRate)).toBeLessThan(0.01);
      }
    });

    it('should have product breakdown that sums to 100%', () => {
      const params = {
        startDate: '2024-01-01',
        endDate: '2024-01-31',
      };

      const analytics = generateMockContactAnalytics(params);
      
      const totalPercentage = analytics.productBreakdown.reduce(
        (sum, product) => sum + product.percentage, 
        0
      );
      
      expect(totalPercentage).toBe(100);
    });

    it('should have category breakdown that sums to 100%', () => {
      const params = {
        startDate: '2024-01-01',
        endDate: '2024-01-31',
      };

      const analytics = generateMockContactAnalytics(params);
      
      const totalPercentage = analytics.categoryBreakdown.reduce(
        (sum, category) => sum + category.percentage, 
        0
      );
      
      expect(totalPercentage).toBe(100);
    });
  });
});

describe('Contact Export', () => {
  // Mock DOM methods for testing
  beforeAll(() => {
    global.URL.createObjectURL = jest.fn(() => 'mock-url');
    global.URL.revokeObjectURL = jest.fn();
    
    // Mock document methods
    const mockLink = {
      setAttribute: jest.fn(),
      click: jest.fn(),
      style: {},
    };
    
    document.createElement = jest.fn(() => mockLink as any);
    document.body.appendChild = jest.fn();
    document.body.removeChild = jest.fn();
  });

  it('should handle CSV export without errors', async () => {
    const { exportContactsToCSV } = require('@/lib/utils/contact-export');
    
    expect(() => {
      exportContactsToCSV(mockContacts, 'test-contacts');
    }).not.toThrow();
  });

  it('should handle Excel export without errors', async () => {
    const { exportContactsToExcel } = require('@/lib/utils/contact-export');
    
    await expect(
      exportContactsToExcel(mockContacts, 'test-contacts')
    ).resolves.not.toThrow();
  });

  it('should handle PDF export without errors', () => {
    const { exportContactsToPDF } = require('@/lib/utils/contact-export');
    
    // Mock window.open
    global.window.open = jest.fn(() => ({
      document: {
        write: jest.fn(),
        close: jest.fn(),
      },
      focus: jest.fn(),
      print: jest.fn(),
      close: jest.fn(),
    })) as any;

    expect(() => {
      exportContactsToPDF(mockContacts, 'test-contacts');
    }).not.toThrow();
  });

  it('should handle unsupported export format', async () => {
    await expect(
      exportContacts(mockContacts, 'unsupported' as any, 'test')
    ).rejects.toThrow('Unsupported export format: unsupported');
  });
});

describe('Contact Table Functionality', () => {
  it('should filter contacts correctly', () => {
    const customerContacts = mockContacts.filter(
      contact => contact.category === ContactCategory.CUSTOMER
    );
    
    expect(customerContacts).toHaveLength(1);
    expect(customerContacts[0].first_name).toBe('John');
  });

  it('should search contacts by name', () => {
    const searchTerm = 'john';
    const searchResults = mockContacts.filter(contact =>
      contact.first_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      contact.last_name.toLowerCase().includes(searchTerm.toLowerCase())
    );
    
    expect(searchResults).toHaveLength(1);
    expect(searchResults[0].first_name).toBe('John');
  });

  it('should search contacts by email', () => {
    const searchTerm = 'jane.smith';
    const searchResults = mockContacts.filter(contact =>
      contact.email.toLowerCase().includes(searchTerm.toLowerCase())
    );
    
    expect(searchResults).toHaveLength(1);
    expect(searchResults[0].email).toBe('<EMAIL>');
  });
});
