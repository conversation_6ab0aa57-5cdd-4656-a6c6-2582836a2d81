/**
 * Test script to verify the analytics dashboard fix
 * This script tests the date handling functionality that was causing the runtime error
 */

// Mock the filter store behavior to test our fix
const mockFilterPeriod = {
  type: 'monthly',
  startDate: undefined, // This was causing the error
  endDate: null, // This was also causing issues
  label: 'This Month'
};

// Test the toISOStringSafe function logic
function toISOStringSafe(date) {
  if (!date) return undefined;
  
  try {
    // If it's already a string, assume it's an ISO string
    if (typeof date === 'string') return date;
    
    // If it's a Date object, convert to ISO string
    if (date instanceof Date) return date.toISOString();
    
    // If it's neither, try to create a Date object
    const dateObj = new Date(date);
    if (isNaN(dateObj.getTime())) return undefined;
    
    return dateObj.toISOString();
  } catch (error) {
    console.warn('Failed to convert date to ISO string:', date, error);
    return undefined;
  }
}

// Test the default date range function
function getDefaultDateRange() {
  const now = new Date();
  const startOfCurrentMonth = new Date(now.getFullYear(), now.getMonth(), 1);
  const endOfCurrentMonth = new Date(now.getFullYear(), now.getMonth() + 1, 0);
  
  return {
    startDate: startOfCurrentMonth.toISOString(),
    endDate: endOfCurrentMonth.toISOString(),
  };
}

// Test the analytics params building logic
function buildAnalyticsParams(filterPeriod, selectedProduct = "all", selectedCategory = "all") {
  const defaultDates = getDefaultDateRange();
  
  return {
    startDate: toISOStringSafe(filterPeriod.startDate) || defaultDates.startDate,
    endDate: toISOStringSafe(filterPeriod.endDate) || defaultDates.endDate,
    ...(selectedProduct !== "all" ? { productId: selectedProduct } : {}),
    ...(selectedCategory !== "all" ? { category: selectedCategory } : {}),
  };
}

// Run tests
console.log('🧪 Testing Analytics Dashboard Date Handling Fix\n');

console.log('1. Testing toISOStringSafe function:');
console.log('   - undefined input:', toISOStringSafe(undefined));
console.log('   - null input:', toISOStringSafe(null));
console.log('   - Date object:', toISOStringSafe(new Date()));
console.log('   - ISO string:', toISOStringSafe('2024-01-01T00:00:00.000Z'));
console.log('   - Invalid input:', toISOStringSafe('invalid-date'));

console.log('\n2. Testing getDefaultDateRange function:');
const defaultRange = getDefaultDateRange();
console.log('   - Start date:', defaultRange.startDate);
console.log('   - End date:', defaultRange.endDate);

console.log('\n3. Testing buildAnalyticsParams with problematic filter:');
const analyticsParams = buildAnalyticsParams(mockFilterPeriod);
console.log('   - Analytics params:', JSON.stringify(analyticsParams, null, 2));

console.log('\n4. Testing with various filter scenarios:');

// Test with valid dates
const validFilter = {
  type: 'weekly',
  startDate: new Date('2024-01-01'),
  endDate: new Date('2024-01-07'),
  label: 'Test Week'
};
console.log('   - Valid dates:', JSON.stringify(buildAnalyticsParams(validFilter), null, 2));

// Test with string dates (from persistence)
const stringDateFilter = {
  type: 'custom',
  startDate: '2024-01-01T00:00:00.000Z',
  endDate: '2024-01-31T23:59:59.999Z',
  label: 'Custom Range'
};
console.log('   - String dates:', JSON.stringify(buildAnalyticsParams(stringDateFilter), null, 2));

// Test with mixed scenarios
const mixedFilter = {
  type: 'daily',
  startDate: new Date(),
  endDate: undefined,
  label: 'Today'
};
console.log('   - Mixed dates:', JSON.stringify(buildAnalyticsParams(mixedFilter), null, 2));

console.log('\n✅ All tests completed successfully!');
console.log('The analytics dashboard should now load without the "toISOString is not a function" error.');
