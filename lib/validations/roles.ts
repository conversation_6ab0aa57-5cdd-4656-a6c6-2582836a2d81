import { z } from 'zod';

// Permission schema
export const permissionSchema = z.object({
  id: z.string().uuid(),
  name: z.string().min(1),
  description: z.string(),
  created_at: z.string().datetime(),
  updated_at: z.string().datetime(),
});

// Role schema
export const roleSchema = z.object({
  id: z.string().uuid(),
  name: z.string().min(1).max(50),
  description: z.string(),
  permission_level: z.number()
    .int()
    .min(1)
    .max(10) // Increased to 10 to accommodate platform_owner
    .refine(
      (level) => level >= 1 && level <= 10,
      {
        message: 'Permission level must be between 1 (Agent) and 10 (Platform Owner)',
      }
    ),
  is_system_role: z.boolean(),
  permissions: z.array(permissionSchema),
  created_at: z.string().datetime(),
  updated_at: z.string().datetime(),
  createdAt: z.string().datetime(),
  updatedAt: z.string().datetime(),
});

// Permission categories and types
export const permissionCategories = ['tickets', 'users', 'reports', 'settings'] as const;
export const permissionTypes = ['view', 'create', 'edit', 'delete'] as const;

// Permission dependencies - each key requires the listed permissions
export const permissionDependencies: Record<string, string[]> = {
  'edit': ['view'],
  'delete': ['view'],
  'create': ['view'],
};

// Helper function to check permission dependencies
export const getRequiredPermissions = (category: string, permission: string): string[] => {
  const dependencies = permissionDependencies[permission] || [];
  return dependencies.map(dep => `${category}.${dep}`);
};

// Schema for creating/updating a role
export const roleUpdateSchema = z.object({
  name: z.string().min(1).max(50),
  description: z.string(),
  permission_level: z.number()
    .int()
    .min(1)
    .max(10) // Increased to 10 to accommodate platform_owner
    .refine(
      (level) => level >= 1 && level <= 10,
      {
        message: 'Permission level must be between 1 (Agent) and 10 (Platform Owner)',
      }
    ),
  permissions: z.array(z.string()),
});

// Schema for role assignment
export const roleAssignmentSchema = z.object({
  user_id: z.string().uuid(),
  role_id: z.string().uuid(),
});

// Role level constants for better type safety
export const RoleLevels = {
  AGENT: 1,
  TEAM_LEAD: 2,
  HEAD_OF_DEPARTMENT: 3,
  OPERATION_MANAGER: 4,
  SUPER_ADMIN: 5,
  PLATFORM_OWNER: 10,
} as const;

// Type for role levels
export type RoleLevel = typeof RoleLevels[keyof typeof RoleLevels];

// Helper function to validate role level
export const validateRoleLevel = (level: number): level is RoleLevel => {
  return Object.values(RoleLevels).includes(level as RoleLevel);
};

// Helper function to get role name from level
export const getRoleName = (level: RoleLevel): string => {
  switch (level) {
    case RoleLevels.PLATFORM_OWNER:
      return 'Platform Owner';
    case RoleLevels.SUPER_ADMIN:
      return 'Super Admin';
    case RoleLevels.OPERATION_MANAGER:
      return 'Operation Manager';
    case RoleLevels.HEAD_OF_DEPARTMENT:
      return 'Head of Department';
    case RoleLevels.TEAM_LEAD:
      return 'Team Lead';
    case RoleLevels.AGENT:
      return 'Agent';
    default:
      return 'Unknown Role';
  }
};

// Types inferred from the schemas
export type Role = z.infer<typeof roleSchema>;
export type RoleUpdate = z.infer<typeof roleUpdateSchema>;
export type RoleAssignment = z.infer<typeof roleAssignmentSchema>;
export type Permission = z.infer<typeof permissionSchema>;
