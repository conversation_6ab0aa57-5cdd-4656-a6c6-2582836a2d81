import * as z from 'zod';

// Create product schema
export const createProductSchema = z.object({
  name: z.string().min(1, 'Name is required').max(100, 'Name must be less than 100 characters'),
  description: z.string().max(500, 'Description must be less than 500 characters').optional(),
});

export type CreateProductFormValues = z.infer<typeof createProductSchema>;

// Update product schema
export const updateProductSchema = createProductSchema.extend({
  id: z.string(),
});

export type UpdateProductFormValues = z.infer<typeof updateProductSchema>;
