import * as z from 'zod';
import { TicketPriority, TicketStatus } from '../api';

// Category schemas
export const createCategorySchema = z.object({
  name: z.string().min(1, 'Name is required').max(100, 'Name must be less than 100 characters'),
  description: z.string().max(500, 'Description must be less than 500 characters').optional(),
});

export type CreateCategoryFormValues = z.infer<typeof createCategorySchema>;

export const updateCategorySchema = createCategorySchema.extend({
  parent_id: z.string().nullable(),
});

export type UpdateCategoryFormValues = z.infer<typeof updateCategorySchema>;

// Create ticket schema
export const createTicketSchema = z.object({
  description: z.string().min(1, "Description is required"),
  priority: z.enum(["LOW", "MEDIUM", "HIGH", "URGENT"]),
  status: z.enum(["OPEN", "IN_PROGRESS", "PENDING", "RESOLVED", "CLOSED"]),
  category_id: z.string().min(1, "Category is required"),
  contact_id: z.string().min(1, "Contact is required"),
  channel_id: z.string().min(1, "Channel is required"),
  product_id: z.string().min(1, "Product is required"),
  // assigned_to: z.string().min(1, "Agent assignment is required"),
  due_date: z.date({
    required_error: "Due date is required",
  }),
})

export type CreateTicketFormValues = z.infer<typeof createTicketSchema>

// Update ticket schema
export const updateTicketSchema = z.object({
  description: z.string()
    .min(1, 'Description is required')
    .max(2000, 'Description must be less than 2000 characters'),
  priority: z.nativeEnum(TicketPriority),
  status: z.nativeEnum(TicketStatus),
  category_id: z.string().min(1, 'Category is required'),
  contact_id: z.string().min(1, 'Contact is required'),
  channel_id: z.string().min(1, 'Channel is required'),
  product_id: z.string().min(1, 'Product is required'),
  // assigned_to: z.string().optional(),
  due_date: z.string().optional(),
});

export type UpdateTicketFormValues = z.infer<typeof updateTicketSchema>;

// Assign ticket schema
export const assignTicketSchema = z.object({
  assigned_agent_id: z.string().min(1, 'Agent is required'),
});

export type AssignTicketFormValues = z.infer<typeof assignTicketSchema>;

// Add ticket comment schema
export const addTicketCommentSchema = z.object({
  content: z.string()
    .min(1, 'Comment is required')
    .max(1000, 'Comment must be less than 1000 characters'),
});

export type AddTicketCommentFormValues = z.infer<typeof addTicketCommentSchema>;

// Change ticket status schema
export const changeTicketStatusSchema = z.object({
  status: z.nativeEnum(TicketStatus),
});

export type ChangeTicketStatusFormValues = z.infer<typeof changeTicketStatusSchema>;

// Change ticket priority schema
export const changeTicketPrioritySchema = z.object({
  priority: z.nativeEnum(TicketPriority),
});

export type ChangeTicketPriorityFormValues = z.infer<typeof changeTicketPrioritySchema>;
