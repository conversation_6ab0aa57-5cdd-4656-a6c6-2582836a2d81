import { z } from 'zod';

/**
 * Schema for validating FAQ data
 */
export const faqSchema = z.object({
  id: z.string().uuid().optional(),
  question: z.string().min(5, "Question must be at least 5 characters"),
  answer: z.string().min(10, "Answer must be at least 10 characters"),
  category_id: z.string().uuid("A valid category must be selected"),
  product_id: z.string().uuid("A valid product must be selected").nullable().optional(),
  is_flagged: z.boolean().optional().default(false),
  flag_reason: z.string().optional(),
  is_active: z.boolean().optional().default(true),
  is_published: z.boolean().optional().default(true),
});

/**
 * Schema for creating a new FAQ
 */
export const createFaqSchema = faqSchema.omit({
  id: true,
  is_flagged: true,
  flag_reason: true,
});

/**
 * Schema for updating an existing FAQ
 */
export const updateFaqSchema = faqSchema
  .omit({ id: true })
  .partial();

/**
 * Schema for flagging/unflagging an FAQ
 */
export const flagFaqSchema = z.object({
  is_flagged: z.boolean(),
  flag_reason: z.string().optional()
}).refine(
  (data) => {
    // If flagging (is_flagged is true), require a reason
    if (data.is_flagged && !data.flag_reason) {
      return false;
    }
    return true;
  },
  {
    message: "A reason is required when flagging an FAQ",
    path: ["flag_reason"] // This targets the error to the flag_reason field
  }
);

export type FAQ = z.infer<typeof faqSchema>;
export type CreateFAQInput = z.infer<typeof createFaqSchema>;
export type UpdateFAQInput = z.infer<typeof updateFaqSchema>;
export type FlagFAQInput = z.infer<typeof flagFaqSchema>;
