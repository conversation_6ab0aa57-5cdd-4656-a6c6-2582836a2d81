import * as z from 'zod';
import { UserRole } from '../api';

// Create user schema
export const createUserSchema = z.object({
  first_name: z.string()
    .min(1, 'First name is required')
    .max(50, 'First name must be less than 50 characters'),
  last_name: z.string()
    .min(1, 'Last name is required')
    .max(50, 'Last name must be less than 50 characters'),
  email: z.string()
    .email('Please enter a valid email address')
    .min(1, 'Email is required'),
  phone_number: z.string()
    .min(1, 'Phone number is required')
    .max(15, 'Phone number must be less than 15 characters'),
  role_id: z.string()
    .min(1, 'Role is required'),
  status: z.string().optional(),
});

export type CreateUserFormValues = z.infer<typeof createUserSchema>;

// Update user schema
export const updateUserSchema = z.object({
  first_name: z.string()
    .min(1, 'First name is required')
    .max(50, 'First name must be less than 50 characters'),
  last_name: z.string()
    .min(1, 'Last name is required')
    .max(50, 'Last name must be less than 50 characters'),
  email: z.string()
    .email('Please enter a valid email address')
    .min(1, 'Email is required'),
  phone_number: z.string()
    .min(1, 'Phone number is required')
    .max(15, 'Phone number must be less than 15 characters'),
  role_id: z.string()
    .min(1, 'Role is required'),
  status: z.string().optional(),
});

export type UpdateUserFormValues = z.infer<typeof updateUserSchema>;

// Change role schema
export const changeRoleSchema = z.object({
  role_id: z.string()
    .min(1, 'Role is required'),
});

export type ChangeRoleFormValues = z.infer<typeof changeRoleSchema>;

// Check if role is admin
export const isAdminRole = (role: UserRole): boolean => {
  return [
    UserRole.PLATFORM_OWNER,
    UserRole.SUPERVISOR,
  ].includes(role);
};
