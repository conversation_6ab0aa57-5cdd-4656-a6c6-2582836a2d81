import * as z from 'zod';

// Create channel schema
export const createChannelSchema = z.object({
  name: z.string().min(1, 'Name is required').max(100, 'Name must be less than 100 characters'),
  type: z.string().min(1, 'Type is required').max(100, 'Type must be less than 100 characters'),
  description: z.string().max(500, 'Description must be less than 500 characters').optional(),
  is_active: z.boolean().default(true),
});

export type CreateChannelFormValues = z.infer<typeof createChannelSchema>;

// Update channel schema
export const updateChannelSchema = createChannelSchema.extend({
  id: z.string(),
});

export type UpdateChannelFormValues = z.infer<typeof updateChannelSchema>;
