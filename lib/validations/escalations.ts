import { z } from 'zod';

/**
 * Schema for validating escalation data
 */
export const escalationSchema = z.object({
  id: z.string().uuid().optional(),
  ticket_id: z.string().uuid("A valid ticket must be selected"),
  level: z.number().int().min(1).max(3),
  reason: z.string().min(5, "Reason must be at least 5 characters"),
  resolved: z.boolean().optional().default(false),
  resolution_notes: z.string().nullable().optional(),
  escalate_to: z.string().uuid("A valid user must be selected").optional(),
});

/**
 * Schema for creating a new escalation
 */
export const createEscalationSchema = escalationSchema.pick({
  ticket_id: true,
  level: true,
  reason: true,
  escalate_to: true,
});

/**
 * Schema for resolving an escalation
 */
export const resolveEscalationSchema = z.object({
  resolution_notes: z.string().min(5, "Resolution notes must be at least 5 characters"),
});

/**
 * Schema for creating an escalation comment
 */
export const createEscalationCommentSchema = z.object({
  comment: z.string().min(1, "Comment cannot be empty"),
  is_internal: z.boolean().optional().default(false),
});

export type Escalation = z.infer<typeof escalationSchema>;
export type CreateEscalationInput = z.infer<typeof createEscalationSchema>;
export type ResolveEscalationInput = z.infer<typeof resolveEscalationSchema>;
export type CreateEscalationCommentInput = z.infer<typeof createEscalationCommentSchema>;
