import * as z from 'zod';
import { ContactCategory, ContactStatus, PreferredContactMethod } from '../api';

// Create contact schema
export const createContactSchema = z.object({
  first_name: z.string()
    .min(1, 'First name is required')
    .max(50, 'First name must be less than 50 characters'),
  last_name: z.string()
    .min(1, 'Last name is required')
    .max(50, 'Last name must be less than 50 characters'),
  email: z.string()
    .email('Please enter a valid email address')
    .min(1, 'Email is required'),
  phone: z.string()
    .min(1, 'Phone number is required')
    .regex(/^(\+?1\s?)?(\(\d{3}\)|\d{3})[\s.-]?\d{3}[\s.-]?\d{4}$|^(\+?254|0)?[17]\d{8}$/, 'Please enter a valid phone number'),
  company: z.string().optional(),
  address: z.string().optional(),
  city: z.string().optional(),
  country: z.string().optional(),
  notes: z.string().optional(),
  preferred_contact_method: z.nativeEnum(PreferredContactMethod).optional().default(PreferredContactMethod.EMAIL),
  status: z.nativeEnum(ContactStatus).optional().default(ContactStatus.ACTIVE),
  category: z.nativeEnum(ContactCategory),
});

export type CreateContactFormValues = z.infer<typeof createContactSchema>;

// Update contact schema
export const updateContactSchema = z.object({
  first_name: z.string()
    .min(1, 'First name is required')
    .max(50, 'First name must be less than 50 characters'),
  last_name: z.string()
    .min(1, 'Last name is required')
    .max(50, 'Last name must be less than 50 characters'),
  email: z.string()
    .email('Please enter a valid email address')
    .min(1, 'Email is required'),
  phone: z.string()
    .min(1, 'Phone number is required')
    .regex(/^[+]?[(]?[0-9]{3}[)]?[-\s.]?[0-9]{3}[-\s.]?[0-9]{4,6}$/, 'Please enter a valid phone number'),
  company: z.string().optional(),
  address: z.string().optional(),
  city: z.string().optional(),
  country: z.string().optional(),
  notes: z.string().optional(),
  preferred_contact_method: z.nativeEnum(PreferredContactMethod).optional(),
  status: z.nativeEnum(ContactStatus).optional(),
  category: z.nativeEnum(ContactCategory),
});

export type UpdateContactFormValues = z.infer<typeof updateContactSchema>;

// Search contacts schema
export const searchContactsSchema = z.object({
  query: z.string().min(2, 'Search query must be at least 2 characters'),
});

export type SearchContactsFormValues = z.infer<typeof searchContactsSchema>;
