/**
 * Audit action constants for the call center application
 * These constants define the standard action types that can be logged in the audit system
 */

export const AUDIT_ACTIONS = {
  // User actions
  USER_LOGIN: 'USER_LOGIN',
  USER_LOGOUT: 'USER_LOGOUT',
  USER_CREATED: 'USER_CREATED',
  USER_UPDATED: 'USER_UPDATED',
  USER_DELETED: 'USER_DELETED',
  USER_PASSWORD_CHANGED: 'USER_PASSWORD_CHANGED',
  USER_PASSWORD_RESET: 'USER_PASSWORD_RESET',
  USER_ROLE_ASSIGNED: 'USER_ROLE_ASSIGNED',
  
  // Role actions
  ROLE_CREATED: 'ROLE_CREATED',
  ROLE_UPDATED: 'ROLE_UPDATED',
  ROLE_DELETED: 'ROLE_DELETED',
  ROLE_PERMISSION_UPDATED: 'ROLE_PERMISSION_UPDATED',
  
  // Ticket actions
  TICKET_CREATED: 'TICKET_CREATED',
  TICKET_UPDATED: 'TICKET_UPDATED',
  TICKET_DELETED: 'TICKET_DELETED',
  TICKET_ASSIGNED: 'TICKET_ASSIGNED',
  TICKET_ESCALATED: 'TICKET_ESCALATED',
  TICKET_RESOLVED: 'TICKET_RESOLVED',
  TICKET_CLOSED: 'TICKET_CLOSED',
  TICKET_REOPENED: 'TICKET_REOPENED',
  TICKET_COMMENT_ADDED: 'TICKET_COMMENT_ADDED',
  
  // Contact actions
  CONTACT_CREATED: 'CONTACT_CREATED',
  CONTACT_UPDATED: 'CONTACT_UPDATED',
  CONTACT_DELETED: 'CONTACT_DELETED',
  
  // FAQ actions
  FAQ_CREATED: 'FAQ_CREATED',
  FAQ_UPDATED: 'FAQ_UPDATED',
  FAQ_DELETED: 'FAQ_DELETED',
  FAQ_FLAGGED: 'FAQ_FLAGGED',
  FAQ_APPROVED: 'FAQ_APPROVED',
  FAQ_REJECTED: 'FAQ_REJECTED',
  
  // Department actions
  DEPARTMENT_CREATED: 'DEPARTMENT_CREATED',
  DEPARTMENT_UPDATED: 'DEPARTMENT_UPDATED',
  DEPARTMENT_DELETED: 'DEPARTMENT_DELETED',
  
  // Category actions
  CATEGORY_CREATED: 'CATEGORY_CREATED',
  CATEGORY_UPDATED: 'CATEGORY_UPDATED',
  CATEGORY_DELETED: 'CATEGORY_DELETED',
  
  // Product actions
  PRODUCT_CREATED: 'PRODUCT_CREATED',
  PRODUCT_UPDATED: 'PRODUCT_UPDATED',
  PRODUCT_DELETED: 'PRODUCT_DELETED',
  
  // Call-related actions
  CALL_RECEIVED: 'CALL_RECEIVED',
  CALL_INITIATED: 'CALL_INITIATED',
  CALL_COMPLETED: 'CALL_COMPLETED',
  CALL_MISSED: 'CALL_MISSED',
  CALL_TRANSFERRED: 'CALL_TRANSFERRED',
  CALL_RECORDING_ACCESS: 'CALL_RECORDING_ACCESS',
  
  // Agent Status actions
  AGENT_STATUS_AVAILABLE: 'AGENT_STATUS_AVAILABLE',
  AGENT_STATUS_BREAK: 'AGENT_STATUS_BREAK',
  AGENT_STATUS_LUNCH: 'AGENT_STATUS_LUNCH',
  AGENT_STATUS_OFFLINE: 'AGENT_STATUS_OFFLINE',
  
  // System Settings actions
  SYSTEM_SETTINGS_UPDATE: 'SYSTEM_SETTINGS_UPDATE',
  SECURITY_SETTINGS_UPDATE: 'SECURITY_SETTINGS_UPDATE',
  KPI_SETTINGS_UPDATE: 'KPI_SETTINGS_UPDATE',
  NOTIFICATION_SETTINGS_UPDATE: 'NOTIFICATION_SETTINGS_UPDATE',
  
  // Report actions
  REPORT_GENERATED: 'REPORT_GENERATED',
  REPORT_EXPORTED: 'REPORT_EXPORTED',
  REPORT_SCHEDULED: 'REPORT_SCHEDULED',
  
  // Batch actions
  BATCH_IMPORT: 'BATCH_IMPORT',
  BATCH_EXPORT: 'BATCH_EXPORT',
  BATCH_UPDATE: 'BATCH_UPDATE',
} as const;

// Type for audit actions
export type AuditAction = keyof typeof AUDIT_ACTIONS;

// Resource types for audit logs
export const AUDIT_RESOURCE_TYPES = {
  USER: 'USER',
  ROLE: 'ROLE',
  TICKET: 'TICKET',
  CONTACT: 'CONTACT',
  FAQ: 'FAQ',
  DEPARTMENT: 'DEPARTMENT',
  CATEGORY: 'CATEGORY',
  PRODUCT: 'PRODUCT',
  CALL: 'CALL',
  AGENT: 'AGENT',
  SYSTEM: 'SYSTEM',
  REPORT: 'REPORT',
  BATCH: 'BATCH',
} as const;

// Type for audit resource types
export type AuditResourceType = keyof typeof AUDIT_RESOURCE_TYPES;

// Audit status types
export const AUDIT_STATUS = {
  SUCCESS: 'SUCCESS',
  ERROR: 'ERROR',
  WARNING: 'WARNING',
  INFO: 'INFO',
  PENDING: 'PENDING',
} as const;

// Type for audit status
export type AuditStatus = keyof typeof AUDIT_STATUS;
