"use client";

import { useCallback } from 'react';
import { IncomingCall } from '@/lib/services/socket-service';

export function useSocketService() {
  // Simulate an incoming call
  const simulateIncomingCall = useCallback((callData: Omit<IncomingCall, 'id' | 'status'>) => {
    // Create a new call object with a random ID and initial status
    const incomingCall: IncomingCall = {
      id: Math.random().toString(36).substring(2, 11),
      status: 'ringing',
      ...callData,
    };
    
    // Dispatch a custom event to notify the application of the incoming call
    const event = new CustomEvent('incomingCall', { detail: incomingCall });
    window.dispatchEvent(event);

    return incomingCall;
  }, []);

  return {
    simulateIncomingCall,
  };
}
