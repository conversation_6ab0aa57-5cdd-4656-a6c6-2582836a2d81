'use client';

import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import {
  contactService,
  CreateContactRequest,
  PaginationParams,
  UpdateContactRequest,
  ContactCategory,
  ContactStatus,
  Contact,
} from '../api';

export const useContacts = (params?: PaginationParams & {
  search?: string;
  status?: string;
  category?: string;
}) => {
  const queryClient = useQueryClient();

  // Get all contacts query
  const {
    data: contactsData,
    isLoading: isLoadingContacts,
    error: contactsError,
    refetch: refetchContacts,
  } = useQuery({
    queryKey: ['contacts', params],
    queryFn: async () => {
      console.log('Fetching contacts with params:', params);
      const response = await contactService.getAllContacts(params || {});
      console.log('Contacts API Response:', response);
      return response;
    },
  });

  // Get contact by ID query
  const getContactById = (id: string) => {
    // Use the queryClient directly to fetch data
    return queryClient.fetchQuery({
      queryKey: ['contact', id],
      queryFn: () => contactService.getContactById(id),
    });
  };

  // Create contact mutation
  const {
    mutate: createContact,
    isPending: isCreatingContact,
    error: createContactError,
  } = useMutation({
    mutationFn: (data: CreateContactRequest) => contactService.createContact(data),
    onSuccess: () => {
      // Invalidate contacts query to trigger a refetch
      queryClient.invalidateQueries({ queryKey: ['contacts'] });
    },
  });

  // Update contact mutation
  const {
    mutate: updateContact,
    isPending: isUpdatingContact,
    error: updateContactError,
  } = useMutation({
    mutationFn: ({ id, data }: { id: string; data: UpdateContactRequest }) => contactService.updateContact(id, data),
    onSuccess: (_, variables) => {
      // Invalidate specific contact query and contacts list
      queryClient.invalidateQueries({ queryKey: ['contact', variables.id] });
      queryClient.invalidateQueries({ queryKey: ['contacts'] });
    },
  });

  // Delete contact mutation
  const {
    mutate: deleteContact,
    isPending: isDeletingContact,
    error: deleteContactError,
  } = useMutation({
    mutationFn: (id: string) => contactService.deleteContact(id),
    onSuccess: (_, id) => {
      // Invalidate specific contact query and contacts list
      queryClient.invalidateQueries({ queryKey: ['contact', id] });
      queryClient.invalidateQueries({ queryKey: ['contacts'] });
    },
  });

  // Search contacts query
  const searchContacts = (query: string) => {
    if (!query || query.length < 3) {
      return Promise.resolve([]);
    }

    // Use the queryClient directly to fetch data
    return queryClient.fetchQuery({
      queryKey: ['contacts', 'search', query],
      queryFn: () => contactService.searchContacts(query),
    });
  };

  // Helper function to get contact category label
  const getCategoryLabel = (category: ContactCategory): string => {
    switch (category) {
      case ContactCategory.CUSTOMER:
        return 'Customer';
      case ContactCategory.LEAD:
        return 'Lead';
      case ContactCategory.PARTNER:
        return 'Partner';
      case ContactCategory.VENDOR:
        return 'Vendor';
      case ContactCategory.OTHER:
        return 'Other';
      default:
        return 'Unknown';
    }
  };

  // Helper function to get contact status label
  const getStatusLabel = (status: ContactStatus): string => {
    switch (status) {
      case ContactStatus.ACTIVE:
        return 'Active';
      case ContactStatus.INACTIVE:
        return 'Inactive';
      default:
        return 'Unknown';
    }
  };

  // Contact category options for forms
  const categoryOptions = [
    { value: ContactCategory.CUSTOMER, label: 'Customer' },
    { value: ContactCategory.LEAD, label: 'Lead' },
    { value: ContactCategory.PARTNER, label: 'Partner' },
    { value: ContactCategory.VENDOR, label: 'Vendor' },
    { value: ContactCategory.OTHER, label: 'Other' },
  ];

  // Contact status options for forms
  const statusOptions = [
    { value: ContactStatus.ACTIVE, label: 'Active' },
    { value: ContactStatus.INACTIVE, label: 'Inactive' },
  ];

  // Debug the contacts data structure
  console.log('contactsData structure:', contactsData);

  // Extract contacts from the response
  let contacts: Contact[] = [];
  let pagination: any = undefined;

  // Handle different response structures
  if (contactsData) {
    // Use a type assertion to avoid TypeScript errors
    const data: any = contactsData;
    console.log('Data keys:', Object.keys(data));

    // Case 1: Response with 'contacts' array (as seen in contacts.json)
    if (data && typeof data === 'object' && 'contacts' in data && Array.isArray(data.contacts)) {
      console.log('Found contacts array in response');
      contacts = data.contacts as Contact[];

      // Check for pagination
      if ('pagination' in data) {
        pagination = data.pagination;
      }
    }
    // Case 2: Standard API response with 'data' array
    else if (data && typeof data === 'object' && 'data' in data) {
      if (Array.isArray(data.data)) {
        contacts = data.data as Contact[];
      } else if (data.data && typeof data.data === 'object') {
        // Check for nested contacts array
        if ('contacts' in data.data && Array.isArray(data.data.contacts)) {
          contacts = data.data.contacts as Contact[];
        }
        // Check for nested data array
        else if ('data' in data.data && Array.isArray(data.data.data)) {
          contacts = data.data.data as Contact[];
        }
      }

      // Check for pagination
      if ('pagination' in data) {
        pagination = data.pagination;
      } else if (data.data && typeof data.data === 'object' && 'pagination' in data.data) {
        pagination = data.data.pagination;
      }
    }
    // Case 3: Direct array
    else if (Array.isArray(data)) {
      contacts = data as Contact[];
    }
  }

  console.log('Extracted contacts:', contacts);

  return {
    // Contacts data and queries
    contacts,
    pagination,
    isLoadingContacts,
    contactsError,
    refetchContacts,
    getContactById,
    searchContacts,

    // Contact mutations
    createContact,
    isCreatingContact,
    createContactError,

    updateContact,
    isUpdatingContact,
    updateContactError,

    deleteContact,
    isDeletingContact,
    deleteContactError,

    // Helper functions
    getCategoryLabel,
    getStatusLabel,
    categoryOptions,
    statusOptions,
  };
};
