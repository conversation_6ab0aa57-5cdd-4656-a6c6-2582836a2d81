import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import {
  getCategories,
  getSubcategories,
  createCategory,
  createSubcategory,
  updateCategory,
  deleteCategory,
} from "@/lib/api/categories";
import type {
  Category,
  CreateCategoryRequest,
  UpdateCategoryRequest,
} from "@/lib/api/types";
import { usePermissions } from "./usePermissions";

// Get all categories
export function useCategories() {
  return useQuery<Category[]>({
    queryKey: ["categories"],
    queryFn: async () => {
      const response = await getCategories();
      return response.data || [];
    },
  });
}

// Get subcategories for a specific category
export function useSubcategories(parentId: string) {
  return useQuery<Category[]>({
    queryKey: ["categories", parentId, "subcategories"],
    queryFn: async () => {
      const response = await getSubcategories(parentId);
      return response.data || [];
    },
    enabled: !!parentId,
  });
}

// Create a new category
export function useCreateCategory() {
  const queryClient = useQueryClient();
  const { hasPermission } = usePermissions();

  return useMutation({
    mutationFn: async (categoryData: CreateCategoryRequest) => {
      // Check if user has admin access
      if (!hasPermission('admin:access')) {
        throw new Error('Only administrators can manage categories');
      }

      const response = await createCategory(categoryData);
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["categories"] });
      toast.success("Category created successfully");
    },
    onError: (error: Error) => {
      toast.error("Failed to create category: " + error.message);
    },
  });
}

// Create a new subcategory
export function useCreateSubcategory(parentId: string) {
  const queryClient = useQueryClient();
  const { hasPermission } = usePermissions();

  return useMutation({
    mutationFn: async (categoryData: CreateCategoryRequest) => {
      // Check if user has admin access
      if (!hasPermission('admin:access')) {
        throw new Error('Only administrators can manage categories');
      }

      const response = await createSubcategory(parentId, categoryData);
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["categories", parentId, "subcategories"],
      });
      queryClient.invalidateQueries({ queryKey: ["categories"] });
      toast.success("Subcategory created successfully");
    },
    onError: (error: Error) => {
      toast.error("Failed to create subcategory: " + error.message);
    },
  });
}

// Update a category
export function useUpdateCategory() {
  const queryClient = useQueryClient();
  const { hasPermission } = usePermissions();

  return useMutation({
    mutationFn: async ({
      id,
      data,
    }: {
      id: string;
      data: UpdateCategoryRequest;
    }) => {
      // Check if user has admin access
      if (!hasPermission('admin:access')) {
        throw new Error('Only administrators can manage categories');
      }

      const response = await updateCategory(id, data);
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["categories"] });
      toast.success("Category updated successfully");
    },
    onError: (error: Error) => {
      toast.error("Failed to update category: " + error.message);
    },
  });
}

// Delete a category
export function useDeleteCategory() {
  const queryClient = useQueryClient();
  const { hasPermission } = usePermissions();

  return useMutation({
    mutationFn: async (id: string) => {
      // Check if user has admin access
      if (!hasPermission('admin:access')) {
        throw new Error('Only administrators can manage categories');
      }

      await deleteCategory(id);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["categories"] });
      toast.success("Category deleted successfully");
    },
    onError: (error: Error) => {
      toast.error("Failed to delete category: " + error.message);
    },
  });
}
