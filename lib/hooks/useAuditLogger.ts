/**
 * Hook for logging audit events
 */
import { useCallback } from 'react';
import { useAuth } from './useAuth';
import { auditService } from '@/lib/api/audit';
import { 
  AUDIT_ACTIONS, 
  AUDIT_RESOURCE_TYPES, 
  AUDIT_STATUS,
  type AuditAction,
  type AuditResourceType,
  type AuditStatus
} from '@/lib/constants/audit-actions';

export function useAuditLogger() {
  const { user } = useAuth();

  /**
   * Log an audit event
   * @param action The action being performed
   * @param resourceType The type of resource being acted upon
   * @param resourceId The ID of the resource being acted upon
   * @param details Additional details about the action
   * @param status The status of the action (default: SUCCESS)
   */
  const logAuditEvent = useCallback(
    async (
      action: AuditAction | string,
      resourceType: AuditResourceType | string,
      resourceId: string,
      details: Record<string, any> = {},
      status: AuditStatus | string = AUDIT_STATUS.SUCCESS
    ) => {
      try {
        if (!user) {
          console.warn('Cannot log audit event: No user is logged in');
          return;
        }

        await auditService.createAuditLog(
          action,
          resourceType,
          resourceId,
          details,
          status
        );
      } catch (error) {
        console.error('Failed to log audit event:', error);
      }
    },
    [user]
  );

  /**
   * Log a call-related audit event
   */
  const logCallEvent = useCallback(
    (
      action: 'CALL_RECEIVED' | 'CALL_INITIATED' | 'CALL_COMPLETED' | 'CALL_MISSED' | 'CALL_TRANSFERRED' | 'CALL_RECORDING_ACCESS',
      callId: string,
      details: Record<string, any> = {}
    ) => {
      return logAuditEvent(
        AUDIT_ACTIONS[action],
        AUDIT_RESOURCE_TYPES.CALL,
        callId,
        details
      );
    },
    [logAuditEvent]
  );

  /**
   * Log an agent status change audit event
   */
  const logAgentStatusEvent = useCallback(
    (
      action: 'AGENT_STATUS_AVAILABLE' | 'AGENT_STATUS_BREAK' | 'AGENT_STATUS_LUNCH' | 'AGENT_STATUS_OFFLINE',
      agentId: string,
      details: Record<string, any> = {}
    ) => {
      return logAuditEvent(
        AUDIT_ACTIONS[action],
        AUDIT_RESOURCE_TYPES.AGENT,
        agentId,
        details
      );
    },
    [logAuditEvent]
  );

  /**
   * Log a system settings update audit event
   */
  const logSettingsEvent = useCallback(
    (
      action: 'SYSTEM_SETTINGS_UPDATE' | 'SECURITY_SETTINGS_UPDATE' | 'KPI_SETTINGS_UPDATE' | 'NOTIFICATION_SETTINGS_UPDATE',
      settingsId: string,
      details: Record<string, any> = {}
    ) => {
      return logAuditEvent(
        AUDIT_ACTIONS[action],
        AUDIT_RESOURCE_TYPES.SYSTEM,
        settingsId,
        details
      );
    },
    [logAuditEvent]
  );

  /**
   * Log a report-related audit event
   */
  const logReportEvent = useCallback(
    (
      action: 'REPORT_GENERATED' | 'REPORT_EXPORTED' | 'REPORT_SCHEDULED',
      reportId: string,
      details: Record<string, any> = {}
    ) => {
      return logAuditEvent(
        AUDIT_ACTIONS[action],
        AUDIT_RESOURCE_TYPES.REPORT,
        reportId,
        details
      );
    },
    [logAuditEvent]
  );

  /**
   * Log a batch operation audit event
   */
  const logBatchEvent = useCallback(
    (
      action: 'BATCH_IMPORT' | 'BATCH_EXPORT' | 'BATCH_UPDATE',
      batchId: string,
      details: Record<string, any> = {}
    ) => {
      return logAuditEvent(
        AUDIT_ACTIONS[action],
        AUDIT_RESOURCE_TYPES.BATCH,
        batchId,
        details
      );
    },
    [logAuditEvent]
  );

  return {
    logAuditEvent,
    logCallEvent,
    logAgentStatusEvent,
    logSettingsEvent,
    logReportEvent,
    logBatchEvent,
    AUDIT_ACTIONS,
    AUDIT_RESOURCE_TYPES,
    AUDIT_STATUS
  };
}
