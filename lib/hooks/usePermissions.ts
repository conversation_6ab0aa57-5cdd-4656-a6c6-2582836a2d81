import { useAuth } from '@/lib/hooks/useAuth';
import { Permission, Role } from '@/lib/api/types';
import { useCallback } from 'react';
import { useRoles } from './useRoles';

export const usePermissions = () => {
  const { user } = useAuth();
  const { getRoleById } = useRoles();

  // Check if user has a specific permission
  const hasPermission = useCallback((permissionCode: string): boolean => {
    console.log(`Checking permission: ${permissionCode} for user:`, {
      userId: user?.id,
      email: user?.email,
      roleId: user?.role_id,
      roleName: user?.role?.name,
      permissionLevel: user?.role?.permission_level,
      hasRoleObject: !!user?.role
    });

    // If no user, return false
    if (!user) {
      console.log('No user found, returning false');
      return false;
    }

    // Define common permissions that should be available to all authenticated users
    const commonPermissions = [
      'faq:view',
      'tickets:read',
      'contacts:read'
    ];

    // If the permission is in the common permissions list, grant access to all authenticated users
    if (commonPermissions.includes(permissionCode)) {
      console.log(`Permission ${permissionCode} is a common permission, granting access to ${user.email}`);
      return true;
    }

    // First, check if the role object is directly available in the user object
    if (user.role) {
      console.log('Using role directly from user object:', user.role.name);

      // Platform Owner has all permissions
      if (user.role.name === 'platform_owner' || user.role.permission_level === 10) {
        console.log(`Platform owner detected (${user.role.name}), granting permission: ${permissionCode}`);
        return true;
      }

      // Admin roles have all permissions
      const adminRoleNames = ['admin', 'super_admin', 'Admin', 'Super Admin'];
      if (adminRoleNames.includes(user.role.name) || user.role.permission_level >= 5) {
        console.log(`Admin role detected (${user.role.name}), granting permission: ${permissionCode}`);
        return true;
      }

      // Define admin-only permissions that supervisors and agents don't have by default
      const adminOnlyPermissions = [
        'user:create', 'user:update', 'user:delete',
        'role:view', 'role:create', 'role:update', 'role:delete',
        'system:settings', 'system:kpis',
        'department:create', 'department:update', 'department:delete',
      ];

      // Supervisor roles have most permissions except admin-only ones (level 3+)
      const supervisorRoleNames = ['supervisor', 'team_lead', 'Team Lead'];
      if (supervisorRoleNames.includes(user.role.name) || user.role.permission_level >= 3) {
        // If the required permission is admin-only, check if they have it specifically
        if (adminOnlyPermissions.includes(permissionCode)) {
          const hasSpecificPermission = user.role.permissions.some(
            (permission: Permission) => permission.code === permissionCode
          );
          console.log(`Supervisor role (${user.role.name}) checking admin-only permission ${permissionCode}: ${hasSpecificPermission}`);
          return hasSpecificPermission;
        }

        // For non-admin-only permissions, grant access
        console.log(`Supervisor role (${user.role.name}) granted general permission: ${permissionCode}`);
        return true;
      }

      // For other roles, check if they have the specific permission
      const hasSpecificPermission = user.role.permissions.some(
        (permission: Permission) => permission.code === permissionCode
      );
      console.log(`Regular role (${user.role.name}) checking specific permission ${permissionCode}: ${hasSpecificPermission}`);
      return hasSpecificPermission;
    }

    // If role is not directly available, try to get it by ID
    if (!user.role_id) {
      console.log('No role_id found and no role object, returning false');
      return false;
    }

    const userRole = getRoleById(user.role_id);
    console.log('User role from getRoleById:', userRole);

    if (!userRole) {
      console.log('No user role found from getRoleById, returning false');
      return false;
    }

    // Check for common permissions first
    if (commonPermissions.includes(permissionCode)) {
      console.log(`Permission ${permissionCode} is a common permission, granting access to ${user.email}`);
      return true;
    }

    // Platform Owner has all permissions
    if (userRole.name === 'platform_owner' || userRole.permission_level === 10) {
      console.log(`Platform owner detected (${userRole.name}), granting permission: ${permissionCode}`);
      return true;
    }

    // Admin roles have all permissions
    const adminRoleNames = ['admin', 'super_admin', 'Admin', 'Super Admin'];
    if (adminRoleNames.includes(userRole.name) || userRole.permission_level >= 5) {
      console.log(`Admin role detected (${userRole.name}), granting permission: ${permissionCode}`);
      return true;
    }

    // Define admin-only permissions that supervisors and agents don't have by default
    const adminOnlyPermissions = [
      'user:create', 'user:update', 'user:delete',
      'role:view', 'role:create', 'role:update', 'role:delete',
      'system:settings', 'system:kpis',
      'department:create', 'department:update', 'department:delete',
    ];

    // Supervisor roles have most permissions except admin-only ones (level 3+)
    const supervisorRoleNames = ['supervisor', 'team_lead', 'Team Lead'];
    if (supervisorRoleNames.includes(userRole.name) || userRole.permission_level >= 3) {
      // If the required permission is admin-only, check if they have it specifically
      if (adminOnlyPermissions.includes(permissionCode)) {
        const hasSpecificPermission = userRole.permissions.some(
          (permission: Permission) => permission.code === permissionCode
        );
        console.log(`Supervisor role (${userRole.name}) checking admin-only permission ${permissionCode}: ${hasSpecificPermission}`);
        return hasSpecificPermission;
      }

      // For non-admin-only permissions, grant access
      console.log(`Supervisor role (${userRole.name}) granted general permission: ${permissionCode}`);
      return true;
    }

    // For other roles, check if they have the specific permission
    const hasSpecificPermission = userRole.permissions.some(
      (permission: Permission) => permission.code === permissionCode
    );
    console.log(`Regular role (${userRole.name}) checking specific permission ${permissionCode}: ${hasSpecificPermission}`);
    return hasSpecificPermission;
  }, [user, getRoleById]);

  // Check if user has any of the specified permissions
  const hasAnyPermission = useCallback((permissionCodes: string[]): boolean => {
    return permissionCodes.some(permissionCode => hasPermission(permissionCode));
  }, [hasPermission]);

  // Check if user has all of the specified permissions
  const hasAllPermissions = useCallback((permissionCodes: string[]): boolean => {
    return permissionCodes.every(permissionCode => hasPermission(permissionCode));
  }, [hasPermission]);

  // Get all permissions for a role
  const getRolePermissions = useCallback((role: Role): Permission[] => {
    return role.permissions || [];
  }, []);

  // Check if user has admin access
  const hasAdminAccess = useCallback((): boolean => {
    console.log('Checking admin access for user:', {
      userId: user?.id,
      email: user?.email,
      roleId: user?.role_id,
      roleName: user?.role?.name,
      permissionLevel: user?.role?.permission_level,
      hasRoleObject: !!user?.role
    });

    if (!user) return false;

    // First, check if the role object is directly available in the user object
    if (user.role) {
      console.log('Using role directly from user object for admin check:', user.role.name);

      // Platform Owner (level 10) has highest access
      if (user.role.name === 'platform_owner' || user.role.permission_level === 10) {
        console.log(`Platform owner detected (${user.role.name}), granting admin access`);
        return true;
      }

      // Admin roles have admin access
      const adminRoleNames = ['admin', 'super_admin', 'Admin', 'Super Admin'];
      if (adminRoleNames.includes(user.role.name) || user.role.permission_level >= 5) {
        console.log(`Admin role detected (${user.role.name}), granting admin access`);
        return true;
      }

      // Supervisor roles have admin access (level 3+)
      const supervisorRoleNames = ['supervisor', 'team_lead', 'Team Lead'];
      if (supervisorRoleNames.includes(user.role.name) || user.role.permission_level >= 3) {
        console.log(`Supervisor role detected (${user.role.name}), granting admin access`);
        return true;
      }

      // Agents (level 2) do not have admin access
      console.log(`Role (${user.role.name}) with permission level ${user.role.permission_level}, admin access: false`);
      return false;
    }

    // If role is not directly available, try to get it by ID
    if (!user.role_id) {
      console.log('No role_id found and no role object, returning false for admin access');
      return false;
    }

    const userRole = getRoleById(user.role_id);
    console.log('User role from getRoleById for admin check:', userRole);

    if (!userRole) {
      console.log('No user role found from getRoleById, returning false for admin access');
      return false;
    }

    // Platform Owner (level 10) has highest access
    if (userRole.name === 'platform_owner' || userRole.permission_level === 10) {
      console.log(`Platform owner detected (${userRole.name}), granting admin access`);
      return true;
    }

    // Admin roles have admin access
    const adminRoleNames = ['admin', 'super_admin', 'Admin', 'Super Admin'];
    if (adminRoleNames.includes(userRole.name) || userRole.permission_level >= 5) {
      console.log(`Admin role detected (${userRole.name}), granting admin access`);
      return true;
    }

    // Supervisor roles have admin access (level 3+)
    const supervisorRoleNames = ['supervisor', 'team_lead', 'Team Lead'];
    if (supervisorRoleNames.includes(userRole.name) || userRole.permission_level >= 3) {
      console.log(`Supervisor role detected (${userRole.name}), granting admin access`);
      return true;
    }

    // Agents (level 2) do not have admin access
    console.log(`Role (${userRole.name}) with permission level ${userRole.permission_level}, admin access: false`);
    return false;
  }, [user, getRoleById]);

  return {
    hasPermission,
    hasAnyPermission,
    hasAllPermissions,
    getRolePermissions,
    hasAdminAccess,
  };
};
