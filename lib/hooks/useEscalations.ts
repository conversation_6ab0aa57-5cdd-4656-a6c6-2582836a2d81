import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from '@/components/ui/use-toast';
import { escalationService } from '@/lib/api/escalations';
import {
  Escalation,
  EscalationComment,
  CreateEscalationRequest,
  CreateEscalationCommentRequest,
  ResolveEscalationRequest
} from '@/lib/api/types';
import { useCallback } from 'react';
import { usePermissions } from './usePermissions';
import { useNotifications } from './useNotifications';
import { useAuth } from './useAuth';

/**
 * Hook for managing escalations
 */
export const useEscalations = () => {
  const queryClient = useQueryClient();
  const { hasPermission } = usePermissions();
  const { addNotification } = useNotifications();
  const { user } = useAuth();

  // Fetch all escalations
  const {
    data: escalationsData,
    isLoading: isLoadingEscalations,
    error: escalationsError,
    refetch: refetchEscalations,
  } = useQuery({
    queryKey: ['escalations'],
    queryFn: async () => {
      const response = await escalationService.getAllEscalations();
      return response.data || [];
    },
  });

  // Get escalation by ID
  const getEscalationById = useCallback(async (id: string): Promise<Escalation | null> => {
    try {
      const response = await escalationService.getEscalationById(id);
      return response.data || null;
    } catch (error) {
      console.error('Error fetching escalation:', error);
      return null;
    }
  }, []);

  // Create a new escalation
  const createEscalationMutation = useMutation({
    mutationFn: (data: CreateEscalationRequest) => {
      if (!hasPermission('ticket:escalate')) {
        throw new Error('You do not have permission to create escalations');
      }
      return escalationService.createEscalation(data);
    },
    onSuccess: (response, variables) => {
      queryClient.invalidateQueries({ queryKey: ['escalations'] });
      queryClient.invalidateQueries({ queryKey: ['tickets'] });

      // Show toast notification
      toast({
        title: 'Success',
        description: 'Escalation created successfully',
      });

      // Add notification for supervisors and admins
      const escalationLevel = variables.level;
      const ticketId = variables.ticket_id;
      const reason = variables.reason;

      // Create a notification for the escalation
      addNotification({
        type: 'escalation',
        title: `Ticket Escalated to Level ${escalationLevel}`,
        message: `A ticket has been escalated. Reason: ${reason.substring(0, 50)}${reason.length > 50 ? '...' : ''}`,
        entityId: response.data?.id,
        entityType: 'escalation',
        link: `/dashboard/tickets/escalations`
      });
    },
    onError: (error: any) => {
      toast({
        title: 'Error',
        description: error.message || 'Failed to create escalation',
        variant: 'destructive',
      });
    },
  });

  // Resolve an escalation
  const resolveEscalationMutation = useMutation({
    mutationFn: ({ id, data }: { id: string; data: ResolveEscalationRequest }) => {
      if (!hasPermission('escalation:resolve')) {
        throw new Error('You do not have permission to resolve escalations');
      }
      return escalationService.resolveEscalation(id, data);
    },
    onSuccess: (response, variables) => {
      queryClient.invalidateQueries({ queryKey: ['escalations'] });
      queryClient.invalidateQueries({ queryKey: ['tickets'] });

      // Show toast notification
      toast({
        title: 'Success',
        description: 'Escalation resolved successfully',
      });

      // Add notification for the resolution
      addNotification({
        type: 'escalation',
        title: 'Escalation Resolved',
        message: `An escalation has been resolved. Notes: ${variables.data.resolution_notes.substring(0, 50)}${variables.data.resolution_notes.length > 50 ? '...' : ''}`,
        entityId: variables.id,
        entityType: 'escalation',
        link: `/dashboard/tickets/escalations`
      });
    },
    onError: (error: any) => {
      toast({
        title: 'Error',
        description: error.message || 'Failed to resolve escalation',
        variant: 'destructive',
      });
    },
  });

  // Get comments for an escalation
  const useEscalationComments = (escalationId?: string) => {
    return useQuery({
      queryKey: ['escalationComments', escalationId],
      queryFn: async () => {
        if (!escalationId) return [];
        const response = await escalationService.getEscalationComments(escalationId);
        return response.data || [];
      },
      enabled: !!escalationId,
    });
  };

  // Add a comment to an escalation
  const addEscalationCommentMutation = useMutation({
    mutationFn: ({ id, data }: { id: string; data: CreateEscalationCommentRequest }) => {
      if (!hasPermission('escalation:comment')) {
        throw new Error('You do not have permission to comment on escalations');
      }
      return escalationService.addEscalationComment(id, data);
    },
    onSuccess: (response, variables) => {
      queryClient.invalidateQueries({ queryKey: ['escalationComments', variables.id] });

      // Show toast notification
      toast({
        title: 'Success',
        description: 'Comment added successfully',
      });

      // Add notification for the comment
      if (!variables.data.is_internal || hasPermission('supervisor:access') || hasPermission('admin:access')) {
        addNotification({
          type: 'comment',
          title: 'New Escalation Comment',
          message: `A new comment was added to an escalation: ${variables.data.comment.substring(0, 50)}${variables.data.comment.length > 50 ? '...' : ''}`,
          entityId: variables.id,
          entityType: 'escalation',
          link: `/dashboard/tickets/escalations`
        });
      }
    },
    onError: (error: any) => {
      toast({
        title: 'Error',
        description: error.message || 'Failed to add comment',
        variant: 'destructive',
      });
    },
  });

  // Filter escalations by ticket
  const filterEscalationsByTicket = useCallback((ticketId: string): Escalation[] => {
    if (!escalationsData) return [];
    return escalationsData.filter(escalation => escalation.ticket_id === ticketId);
  }, [escalationsData]);

  // Filter unresolved escalations
  const getUnresolvedEscalations = useCallback((): Escalation[] => {
    if (!escalationsData) return [];
    return escalationsData.filter(escalation => !escalation.resolved);
  }, [escalationsData]);

  // Filter resolved escalations
  const getResolvedEscalations = useCallback((): Escalation[] => {
    if (!escalationsData) return [];
    return escalationsData.filter(escalation => escalation.resolved);
  }, [escalationsData]);

  // Get the latest escalation for a ticket
  const getLatestTicketEscalation = useCallback((ticketId: string): Escalation | undefined => {
    if (!escalationsData) return undefined;
    const ticketEscalations = filterEscalationsByTicket(ticketId);
    return ticketEscalations.sort((a, b) =>
      new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
    )[0];
  }, [escalationsData, filterEscalationsByTicket]);

  return {
    escalations: escalationsData || [],
    isLoading: isLoadingEscalations,
    error: escalationsError,
    refetchEscalations,
    getEscalationById,
    createEscalation: createEscalationMutation.mutate,
    resolveEscalation: resolveEscalationMutation.mutate,
    useEscalationComments,
    addEscalationComment: addEscalationCommentMutation.mutate,
    filterEscalationsByTicket,
    getUnresolvedEscalations,
    getResolvedEscalations,
    getLatestTicketEscalation,
    isCreatingEscalation: createEscalationMutation.isPending,
    isResolvingEscalation: resolveEscalationMutation.isPending,
    isAddingComment: addEscalationCommentMutation.isPending,
  };
};
