import { useEffect, useState, useCallback } from 'react';
import { useAuth } from '@/lib/hooks/useAuth';
import {
  socketService,
  useCallStore,
  SocketEvent,
  IncomingCall,
  CallLog,
  SipCallBridgedEvent,
  SipCallEndedEvent
} from '@/lib/services/socket-service';

import { useErrorHandling } from './useErrorHandling';
import { callMetricsService } from '@/lib/services/call-metrics-service';

export function useCallHandler() {
  const { user } = useAuth();

  // Normalize call object to ensure compatibility between simulated and real SIP calls
  const normalizeCallObject = useCallback((call: IncomingCall): IncomingCall => {
    return {
      ...call,
      // Ensure we have an ID
      id: call.id || call.callId || call.channelId || `call-${Date.now()}`,
      // Normalize channel number (use destinationNumber if channelNumber is missing)
      channelNumber: call.channelNumber || call.destinationNumber || 'Unknown Channel',
      // Normalize timestamp (use startTime if timestamp is missing)
      timestamp: call.timestamp || call.startTime || new Date().toISOString(),
      // Ensure caller name exists
      callerName: call.callerName || 'Unknown Caller',
      // Ensure status is set for new calls
      status: call.status || 'ringing',
    };
  }, []);
  const { handleError } = useErrorHandling();
  const [isConnected, setIsConnected] = useState(false);
  const [callTimer, setCallTimer] = useState<number>(0);
  const [timerInterval, setTimerInterval] = useState<NodeJS.Timeout | null>(null);

  const {
    currentCall,
    callHistory,
    isRecording,
    setCurrentCall,
    addToCallHistory,
    updateCurrentCall,
    setIsRecording,
    clearCurrentCall
  } = useCallStore();

  // Initialize WebSocket connection when component mounts
  useEffect(() => {
    if (!user) return;

    try {
      // Initialize WebSocket with user token
      socketService.initialize(user.id);

      // Listen for connection events
      socketService.on(SocketEvent.CONNECT, () => {
        setIsConnected(true);
        // Reduced logging - only log if needed for debugging
      });

      socketService.on(SocketEvent.DISCONNECT, () => {
        setIsConnected(false);
        // Reduced logging - only log if needed for debugging
      });

      // Handle incoming call events
      socketService.on(SocketEvent.INCOMING_CALL, (call: IncomingCall) => {
        console.log('📞 Raw incoming call data:', call);

        // Normalize the call object to ensure compatibility
        const normalizedCall = normalizeCallObject(call);
        console.log('📞 Normalized call data:', normalizedCall);

        setCurrentCall(normalizedCall);

        // Start tracking call metrics
        callMetricsService.startCallTracking(normalizedCall);

        // Note: Modal will be shown by GlobalCallNotificationProvider
      });

      // Handle call status events
      socketService.on(SocketEvent.CALL_ANSWERED, (call: IncomingCall) => {
        console.log('🔔 Socket: Call answered event received from SIP server');
        updateCurrentCall({ status: 'answered' });

        // Start the call timer when call is confirmed answered by SIP server
        startCallTimer();

        // Track call answer for metrics
        callMetricsService.answerCall(call.id);
      });

      // Handle SIP server call bridged event (the authoritative confirmation)
      socketService.on(SocketEvent.CALL_BRIDGED, (event: SipCallBridgedEvent) => {
        console.log('🌉 Socket: Call bridged event received from SIP server:', event);
        console.log('🔍 Current call state:', currentCall);

        if (currentCall) {
          // Validate call object has required properties
          if (!currentCall.id) {
            console.error('❌ Current call missing ID:', currentCall);
            // Try to use channelId as fallback ID
            if (currentCall.channelId) {
              currentCall.id = currentCall.channelId;
              console.log('🔧 Using channelId as call ID:', currentCall.id);
            } else {
              console.error('❌ Current call missing both ID and channelId');
              return;
            }
          }

          // Only process if call is not already answered
          if (currentCall.status !== 'answered') {
            // Update call status to answered
            updateCurrentCall({ status: 'answered' });

            // Start the call timer when SIP server confirms call is bridged
            startCallTimer();

            // Track call answer for metrics (with null check)
            if (currentCall.id) {
              callMetricsService.answerCall(currentCall.id);
            }

            console.log('✅ Call successfully bridged and timer started');
          } else {
            console.log('🔄 Call already answered, ignoring duplicate bridged event');
          }
        } else {
          console.warn('⚠️ Received call bridged event but no current call found');
        }
      });

      // Handle SIP server call ended event
      socketService.on(SocketEvent.SIP_CALL_ENDED, (event: SipCallEndedEvent) => {
        console.log('🔔 Socket: SIP call ended event received:', event);

        if (currentCall) {
          // Stop the call timer
          stopCallTimer();

          updateCurrentCall({
            status: 'ended',
            duration: event.duration || callTimer
          });

          const callLog: CallLog = {
            ...currentCall,
            status: 'ended',
            agentId: user.id,
            duration: event.duration || callTimer
          };
          addToCallHistory(callLog);

          // Track call end metrics
          callMetricsService.endCallTracking(currentCall, user.id, 'resolved');

          setTimeout(() => {
            clearCurrentCall();
            resetCallTimer();
          }, 2000);
        }
      });

      socketService.on(SocketEvent.CALL_REJECTED, (call: IncomingCall) => {
        console.log('🔔 Socket: Call rejected event received');
        updateCurrentCall({ status: 'missed' });

        // Stop timer if it was running
        stopCallTimer();

        const callLog: CallLog = {
          ...currentCall!,
          status: 'missed',
          agentId: user.id,
        };
        addToCallHistory(callLog);

        // Track missed call metrics
        callMetricsService.missCall(call, user.id);

        setTimeout(clearCurrentCall, 2000);
      });

      // Handle outbound call connected event
      socketService.on(SocketEvent.OUTBOUND_CALL_CONNECTED, (event: any) => {
        console.log('📞 Socket: Outbound call connected event received:', event);

        if (currentCall && currentCall.direction === 'Outbound') {
          // Update call status to answered
          updateCurrentCall({ status: 'answered' });

          // Start the call timer
          startCallTimer();

          // Track call answer for metrics
          if (currentCall.id) {
            callMetricsService.answerCall(currentCall.id);
          }

          console.log('✅ Outbound call successfully connected and timer started');
        }
      });

      // Handle outbound call failed event
      socketService.on(SocketEvent.OUTBOUND_CALL_FAILED, (event: any) => {
        console.log('❌ Socket: Outbound call failed event received:', event);

        if (currentCall && currentCall.direction === 'Outbound') {
          // Update call status to failed
          updateCurrentCall({ status: 'ended' });

          // Clear the call after a short delay
          setTimeout(() => {
            clearCurrentCall();
            resetCallTimer();
          }, 3000);
        }
      });

      socketService.on(SocketEvent.CALL_ENDED, (call: IncomingCall) => {
        console.log('🔔 Socket: Call ended event received from SIP server');

        // Stop the call timer
        stopCallTimer();

        updateCurrentCall({
          status: 'ended',
          duration: call.duration || callTimer
        });

        const callLog: CallLog = {
          ...currentCall!,
          status: 'ended',
          agentId: user.id,
          duration: call.duration || callTimer
        };
        addToCallHistory(callLog);

        // Track call end metrics
        callMetricsService.endCallTracking(call, user.id, 'resolved');

        setTimeout(() => {
          clearCurrentCall();
          resetCallTimer();
        }, 2000);
      });
    } catch (error) {
      handleError(error);
      console.error('Error initializing WebSocket:', error);
    }

    // Cleanup WebSocket and timer on unmount
    return () => {
      try {
        // Clean up timer
        if (timerInterval) {
          clearInterval(timerInterval);
          setTimerInterval(null);
        }

        // Reset timer state
        setCallTimer(0);

        // Disconnect socket service
        socketService.disconnect();

        console.log('🧹 useCallHandler cleanup completed');
      } catch (error) {
        console.error('Error during useCallHandler cleanup:', error);
      }
    };
  }, [user, setCurrentCall, updateCurrentCall, addToCallHistory, clearCurrentCall, handleError]);

  // Timer management functions
  const startCallTimer = useCallback(() => {
    // Prevent starting timer if already running
    if (timerInterval) {
      console.log('⏱️ Timer already running, skipping start');
      return;
    }

    // Clear any existing timer first
    if (timerInterval) {
      clearInterval(timerInterval);
      setTimerInterval(null);
    }

    console.log('⏱️ Starting call timer');
    setCallTimer(0);

    const interval = setInterval(() => {
      setCallTimer(prev => {
        const newValue = prev + 1;
        // Add safety check to prevent runaway timer
        if (newValue > 86400) { // 24 hours max
          console.warn('⚠️ Timer exceeded 24 hours, stopping');
          clearInterval(interval);
          return prev;
        }
        return newValue;
      });
    }, 1000);

    setTimerInterval(interval);
  }, [timerInterval]);

  const stopCallTimer = useCallback(() => {
    console.log('⏱️ Stopping call timer');
    if (timerInterval) {
      clearInterval(timerInterval);
      setTimerInterval(null);
    }
  }, [timerInterval]);

  const resetCallTimer = useCallback(() => {
    console.log('⏱️ Resetting call timer');
    setCallTimer(0);
    if (timerInterval) {
      clearInterval(timerInterval);
      setTimerInterval(null);
    }
  }, [timerInterval]);

  // Format timer display (MM:SS)
  const formatCallTimer = useCallback((seconds: number): string => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
  }, []);

  // Make outbound call
  const makeOutboundCall = useCallback(async (phoneNumber: string) => {
    if (!user) {
      throw new Error('User not authenticated');
    }

    if (currentCall && currentCall.status !== 'ended') {
      throw new Error('Another call is already in progress');
    }

    try {
      console.log('📞 Initiating outbound call to:', phoneNumber);

      // Create outbound call object
      const outboundCall: IncomingCall = {
        id: `outbound-${Date.now()}-${Math.random().toString(36).substring(2, 8)}`,
        callId: `outbound-${Date.now()}`,
        callerNumber: phoneNumber,
        callerName: 'Outbound Call',
        channelNumber: phoneNumber,
        destinationNumber: phoneNumber,
        timestamp: new Date().toISOString(),
        status: 'ringing',
        direction: 'Outbound'
      };

      // Set current call to show dialing state
      setCurrentCall(outboundCall);

      // Make API call to initiate outbound call
      const response = await fetch('/api/calls/outbound', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('accessToken')}`,
        },
        body: JSON.stringify({
          phoneNumber,
          agentId: user.id,
          callId: outboundCall.id
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      console.log('✅ Outbound call API response:', result);

      // Start tracking call metrics
      callMetricsService.startCallTracking(outboundCall);

      console.log('📞 Outbound call initiated successfully');

    } catch (error) {
      console.error('❌ Error making outbound call:', error);
      // Clear current call on error
      clearCurrentCall();
      throw error;
    }
  }, [user, currentCall, setCurrentCall, clearCurrentCall]);

  // Answer call
  const answerCall = useCallback(() => {
    if (!currentCall) return;

    try {
      // Send answer event to SIP server
      socketService.emit(SocketEvent.ANSWER_CALL, {
        callId: currentCall.id
      });

      // Update local state
      updateCurrentCall({ status: 'answered' });

      // Track call answer for metrics
      callMetricsService.answerCall(currentCall.id);

      // Notify UI components
      const event = new CustomEvent('callAnswered', {
        detail: { ...currentCall, status: 'answered' }
      });
      window.dispatchEvent(event);

    } catch (error) {
      handleError(error);
      console.error('Error answering call:', error);
    }
  }, [currentCall, updateCurrentCall, handleError]);

  // Reject call
  const rejectCall = useCallback(() => {
    if (!currentCall) return;

    try {
      // Send reject event to SIP server
      socketService.emit(SocketEvent.REJECT_CALL, { 
        callId: currentCall.id 
      });

      // Update local state
      updateCurrentCall({ status: 'missed' });

      // Add to call history
      const callLog: CallLog = {
        ...currentCall,
        status: 'missed',
        agentId: user?.id || 'unknown'
      };
      addToCallHistory(callLog);

      // Track missed call metrics
      if (user?.id) {
        callMetricsService.missCall(currentCall, user.id);
      }

      // Notify UI components
      const event = new CustomEvent('callRejected', {
        detail: { id: currentCall.id, status: 'missed' }
      });
      window.dispatchEvent(event);

      setTimeout(clearCurrentCall, 500);

    } catch (error) {
      handleError(error);
      console.error('Error rejecting call:', error);
    }
  }, [currentCall, updateCurrentCall, addToCallHistory, clearCurrentCall, user, handleError]);

  // End call
  const endCall = useCallback(() => {
    if (!currentCall) return;

    try {
      // Calculate call duration
      const timestamp = currentCall.timestamp || currentCall.startTime || new Date().toISOString();
      const startTime = new Date(timestamp).getTime();
      const endTime = Date.now();
      const duration = Math.floor((endTime - startTime) / 1000);

      // Send end call event to SIP server
      socketService.emit(SocketEvent.END_CALL, { 
        callId: currentCall.id,
        duration 
      });

      // Update local state
      updateCurrentCall({ 
        status: 'ended',
        duration 
      });

      // Add to call history
      const callLog: CallLog = {
        ...currentCall,
        status: 'ended',
        agentId: user?.id || 'unknown',
        duration
      };
      addToCallHistory(callLog);

      // Track call end metrics
      if (user?.id) {
        callMetricsService.endCallTracking(currentCall, user.id, 'resolved');
      }

      // Notify UI components
      const event = new CustomEvent('callEnded', {
        detail: { id: currentCall.id, status: 'ended', duration }
      });
      window.dispatchEvent(event);

      setTimeout(clearCurrentCall, 500);

    } catch (error) {
      handleError(error);
      console.error('Error ending call:', error);
    }
  }, [currentCall, updateCurrentCall, addToCallHistory, clearCurrentCall, user, handleError]);

  // Add notes to call
  const addNotesToCall = useCallback((callId: string, notes: string) => {
    try {
      // Update call in history
      const updatedHistory = callHistory.map(call => {
        if (call.id === callId) {
          return { ...call, notes };
        }
        return call;
      });

      // Update store
      useCallStore.setState({ callHistory: updatedHistory });
    } catch (error) {
      handleError(error);
      console.error('Error adding notes to call:', error);
    }
  }, [callHistory, handleError]);

  // Update call resolution (for ticket creation)
  const updateCallResolution = useCallback((
    callId: string,
    resolutionStatus: 'resolved' | 'escalated' | 'follow_up_needed' | 'transferred' | 'unresolved',
    customerSatisfaction?: number,
    notes?: string,
    ticketId?: string
  ) => {
    try {
      // Update call metrics
      callMetricsService.updateCallResolution(
        callId,
        resolutionStatus,
        customerSatisfaction,
        notes,
        ticketId
      );
    } catch (error) {
      handleError(error);
      console.error('Error updating call resolution:', error);
    }
  }, [handleError]);

  // Recording functions
  const startRecording = useCallback(() => {
    if (!currentCall) return;

    try {
      // Update recording state
      setIsRecording(true);

      // Emit start recording event to SIP server
      socketService.emit('start_recording', {
        callId: currentCall.id
      });

      console.log('🔴 Recording started for call:', currentCall.id);
    } catch (error) {
      handleError(error);
      console.error('Error starting recording:', error);
    }
  }, [currentCall, setIsRecording, handleError]);

  const stopRecording = useCallback(() => {
    if (!currentCall) return;

    try {
      // Update recording state
      setIsRecording(false);

      // Emit stop recording event to SIP server
      socketService.emit('stop_recording', {
        callId: currentCall.id
      });

      console.log('⏹️ Recording stopped for call:', currentCall.id);
    } catch (error) {
      handleError(error);
      console.error('Error stopping recording:', error);
    }
  }, [currentCall, setIsRecording, handleError]);

  return {
    isConnected,
    currentCall,
    callHistory,
    isRecording,
    answerCall,
    rejectCall,
    endCall,
    makeOutboundCall,
    addNotesToCall,
    updateCallResolution,
    // Timer functions
    callTimer,
    startCallTimer,
    stopCallTimer,
    resetCallTimer,
    formatCallTimer,
    // Recording functions
    startRecording,
    stopRecording
  };
}
