import { useState, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import { logError } from '@/lib/utils/error-logger';

interface ApiError extends Error {
  status?: number;
  statusCode?: number;
  data?: any;
}

interface UseApiErrorHandlerOptions {
  redirectTo?: string;
  shouldRedirectOn401?: boolean;
  shouldRedirectOn403?: boolean;
  onError?: (error: ApiError) => void;
}

export function useApiErrorHandler(options: UseApiErrorHandlerOptions = {}) {
  const {
    redirectTo = '/dashboard',
    shouldRedirectOn401 = true,
    shouldRedirectOn403 = false,
    onError,
  } = options;
  
  const router = useRouter();
  const [error, setError] = useState<ApiError | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  
  const handleApiError = useCallback((error: unknown) => {
    setIsLoading(false);
    
    // Convert to ApiError
    const apiError: ApiError = error instanceof Error 
      ? error as ApiError
      : new Error(String(error));
    
    // Set the error state
    setError(apiError);
    
    // Log the error
    logError({
      message: apiError.message,
      stack: apiError.stack,
      type: 'api',
      statusCode: apiError.status || apiError.statusCode,
      metadata: apiError.data,
      location: typeof window !== 'undefined' ? window.location.href : '',
    });
    
    // Handle specific status codes
    const status = apiError.status || apiError.statusCode;
    
    if (status === 401 && shouldRedirectOn401) {
      // Unauthorized - redirect to login
      router.push('/login');
      return;
    }
    
    if (status === 403 && shouldRedirectOn403) {
      // Forbidden - redirect to specified page
      router.push(redirectTo);
      return;
    }
    
    // Call the onError callback if provided
    if (onError) {
      onError(apiError);
    }
  }, [redirectTo, shouldRedirectOn401, shouldRedirectOn403, onError, router]);
  
  const clearError = useCallback(() => {
    setError(null);
  }, []);
  
  const executeApiCall = useCallback(async <T>(
    apiCall: () => Promise<T>
  ): Promise<T | null> => {
    setIsLoading(true);
    clearError();
    
    try {
      const result = await apiCall();
      setIsLoading(false);
      return result;
    } catch (error) {
      handleApiError(error);
      return null;
    }
  }, [handleApiError, clearError]);
  
  return {
    error,
    isLoading,
    handleApiError,
    clearError,
    executeApiCall,
  };
}
