/**
 * React Hook for WebRTC Voice Management
 * Provides state management and controls for WebRTC voice functionality
 */

import { useState, useEffect, useCallback } from 'react';
import { webrtcVoiceService, WebRTCVoiceState } from '@/lib/services/webrtc-voice-service';
import { useToast } from '@/hooks/use-toast';

export interface UseWebRTCVoiceReturn {
  // State
  voiceState: WebRTCVoiceState;
  isSupported: boolean;
  
  // Actions
  setupVoice: (channelId: string, agentId: string) => Promise<boolean>;
  toggleMute: () => boolean;
  cleanup: () => void;
  
  // Status helpers
  isVoiceReady: boolean;
  hasMicrophoneAccess: boolean;
  isMuted: boolean;
  error: string | null;
}

export function useWebRTCVoice(): UseWebRTCVoiceReturn {
  const { toast } = useToast();
  const [voiceState, setVoiceState] = useState<WebRTCVoiceState>(() => 
    webrtcVoiceService.getState()
  );

  // Check if WebRTC is supported
  const isSupported = webrtcVoiceService.constructor.isSupported();

  // Setup voice communication
  const setupVoice = useCallback(async (channelId: string, agentId: string): Promise<boolean> => {
    if (!isSupported) {
      const errorMessage = 'WebRTC is not supported in this browser';
      toast({
        variant: "destructive",
        title: "Voice Setup Failed",
        description: errorMessage,
      });
      return false;
    }

    try {
      const success = await webrtcVoiceService.setupVoice(channelId, agentId);
      
      if (success) {
        toast({
          title: "Voice Ready",
          description: "You can now talk to the customer",
        });
      } else {
        toast({
          variant: "destructive",
          title: "Voice Setup Failed",
          description: "Could not establish voice connection",
        });
      }
      
      return success;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      toast({
        variant: "destructive",
        title: "Voice Setup Error",
        description: errorMessage,
      });
      return false;
    }
  }, [isSupported, toast]);

  // Toggle microphone mute
  const toggleMute = useCallback((): boolean => {
    const newMutedState = webrtcVoiceService.toggleMute();
    
    toast({
      title: newMutedState ? "Microphone Muted" : "Microphone Unmuted",
      description: newMutedState 
        ? "Your microphone is now muted" 
        : "Your microphone is now active",
    });
    
    return newMutedState;
  }, [toast]);

  // Cleanup voice resources
  const cleanup = useCallback(() => {
    webrtcVoiceService.cleanup();
  }, []);

  // Setup service callbacks
  useEffect(() => {
    const callbacks = {
      onStateChange: (newState: WebRTCVoiceState) => {
        setVoiceState(newState);
      },
      onError: (error: string) => {
        toast({
          variant: "destructive",
          title: "Voice Error",
          description: error,
        });
      },
      onVoiceReady: () => {
        console.log('🎙️ Voice communication is ready');
      }
    };

    webrtcVoiceService.setCallbacks(callbacks);

    // Initial state sync
    setVoiceState(webrtcVoiceService.getState());

    // Cleanup on unmount
    return () => {
      webrtcVoiceService.setCallbacks({});
    };
  }, [toast]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      cleanup();
    };
  }, [cleanup]);

  return {
    // State
    voiceState,
    isSupported,
    
    // Actions
    setupVoice,
    toggleMute,
    cleanup,
    
    // Status helpers
    isVoiceReady: voiceState.isVoiceReady,
    hasMicrophoneAccess: voiceState.hasMicrophoneAccess,
    isMuted: voiceState.isMuted,
    error: voiceState.error,
  };
}
