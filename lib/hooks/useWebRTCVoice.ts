/**
 * React Hook for WebRTC Voice Management
 * Provides state management and controls for WebRTC voice functionality
 */

import { useState, useEffect, useCallback } from 'react';
import { webrtcVoiceService, WebRTCVoiceState } from '@/lib/services/webrtc-voice-service';
import { useToast } from '@/hooks/use-toast';

export interface UseWebRTCVoiceReturn {
  // State
  voiceState: WebRTCVoiceState;
  isSupported: boolean;

  // Actions
  initialize: () => Promise<boolean>;
  answerIncomingCall: (channelId: string, agentId: string) => Promise<boolean>;
  makeOutboundCall: (phoneNumber: string, agentId: string) => Promise<boolean>;
  hangupCurrentCall: () => boolean;
  toggleMute: () => boolean;
  cleanup: () => void;
  getStatus: () => any;

  // Status helpers
  isInitialized: boolean;
  isConnected: boolean;
  hasActiveCall: boolean;
  isMuted: boolean;
  error: string | null;
  extension: string;
}

export function useWebRTCVoice(): UseWebRTCVoiceReturn {
  const { toast } = useToast();
  const [voiceState, setVoiceState] = useState<WebRTCVoiceState>(() =>
    webrtcVoiceService.getState()
  );

  // Check if voice system is supported (client-side only)
  const [isSupported, setIsSupported] = useState(false);

  // Initialize voice system
  const initialize = useCallback(async (): Promise<boolean> => {
    if (!isSupported) {
      const errorMessage = 'Voice system is not supported. Please ensure SIP library is loaded.';
      toast({
        variant: "destructive",
        title: "Voice System Not Available",
        description: errorMessage,
      });
      return false;
    }

    try {
      const success = await webrtcVoiceService.initialize();

      if (success) {
        toast({
          title: "Voice System Ready",
          description: "Extension 1003 connected and ready for calls",
        });
      } else {
        toast({
          variant: "destructive",
          title: "Voice System Failed",
          description: "Could not initialize voice communication",
        });
      }

      return success;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      toast({
        variant: "destructive",
        title: "Voice System Error",
        description: errorMessage,
      });
      return false;
    }
  }, [isSupported, toast]);

  // Answer incoming call
  const answerIncomingCall = useCallback(async (channelId: string, agentId: string): Promise<boolean> => {
    try {
      const success = await webrtcVoiceService.answerIncomingCall(channelId, agentId);

      if (success) {
        toast({
          title: "Call Connected",
          description: "Voice communication established",
        });
      } else {
        toast({
          variant: "destructive",
          title: "Call Answer Failed",
          description: "Could not establish voice connection",
        });
      }

      return success;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      toast({
        variant: "destructive",
        title: "Call Answer Error",
        description: errorMessage,
      });
      return false;
    }
  }, [toast]);

  // Make outbound call
  const makeOutboundCall = useCallback(async (phoneNumber: string, agentId: string): Promise<boolean> => {
    try {
      const success = await webrtcVoiceService.makeOutboundCall(phoneNumber, agentId);

      if (success) {
        toast({
          title: "Outbound Call Connected",
          description: `Calling ${phoneNumber}`,
        });
      } else {
        toast({
          variant: "destructive",
          title: "Outbound Call Failed",
          description: "Could not establish outbound call",
        });
      }

      return success;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      toast({
        variant: "destructive",
        title: "Outbound Call Error",
        description: errorMessage,
      });
      return false;
    }
  }, [toast]);

  // Hang up current call
  const hangupCurrentCall = useCallback((): boolean => {
    const success = webrtcVoiceService.hangupCurrentCall();

    if (success) {
      toast({
        title: "Call Ended",
        description: "Call has been disconnected",
      });
    } else {
      toast({
        variant: "destructive",
        title: "Hangup Failed",
        description: "Could not end the call",
      });
    }

    return success;
  }, [toast]);

  // Toggle microphone mute
  const toggleMute = useCallback((): boolean => {
    const newMutedState = webrtcVoiceService.toggleMute();
    
    toast({
      title: newMutedState ? "Microphone Muted" : "Microphone Unmuted",
      description: newMutedState 
        ? "Your microphone is now muted" 
        : "Your microphone is now active",
    });
    
    return newMutedState;
  }, [toast]);

  // Get voice system status
  const getStatus = useCallback(() => {
    return webrtcVoiceService.getVoiceSystemStatus();
  }, []);

  // Cleanup voice resources
  const cleanup = useCallback(() => {
    webrtcVoiceService.cleanup();
  }, []);

  // Check voice system support on client side only
  useEffect(() => {
    // Only check support on client side to avoid SSR issues
    setIsSupported(webrtcVoiceService.constructor.isSupported());
  }, []);

  // Setup service callbacks
  useEffect(() => {
    const callbacks = {
      onStateChange: (newState: WebRTCVoiceState) => {
        setVoiceState(newState);
      },
      onError: (error: string) => {
        toast({
          variant: "destructive",
          title: "Voice Error",
          description: error,
        });
      },
      onVoiceReady: () => {
        console.log('🎙️ Voice communication is ready');
      }
    };

    webrtcVoiceService.setCallbacks(callbacks);

    // Initial state sync
    setVoiceState(webrtcVoiceService.getState());

    // Cleanup on unmount
    return () => {
      webrtcVoiceService.setCallbacks({});
    };
  }, [toast]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      cleanup();
    };
  }, [cleanup]);

  return {
    // State
    voiceState,
    isSupported,

    // Actions
    initialize,
    answerIncomingCall,
    makeOutboundCall,
    hangupCurrentCall,
    toggleMute,
    cleanup,
    getStatus,

    // Status helpers
    isInitialized: voiceState.isInitialized,
    isConnected: voiceState.isConnected,
    hasActiveCall: voiceState.hasActiveCall,
    isMuted: voiceState.isMuted,
    error: voiceState.error,
    extension: voiceState.extension,
  };
}
