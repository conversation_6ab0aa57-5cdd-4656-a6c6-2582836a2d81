import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import {
  getProducts,
  createProduct,
  updateProduct,
  deleteProduct,
} from "@/lib/api/products";
import type {
  Product,
  CreateProductRequest,
  UpdateProductRequest,
} from "@/lib/api/types";
import { usePermissions } from "./usePermissions";

// Get all products
export function useProducts() {
  return useQuery<Product[]>({
    queryKey: ["products"],
    queryFn: async () => {
      const response = await getProducts();
      // Ensure we're handling the API response structure correctly
      if (response.success && Array.isArray(response.data)) {
        return response.data;
      }
      console.warn('Invalid products response:', response);
      return [];
    },
    enabled: true,
  });
}

// Create a new product
export function useCreateProduct() {
  const queryClient = useQueryClient();
  const { hasPermission } = usePermissions();

  return useMutation({
    mutationFn: async (productData: CreateProductRequest) => {
      // Check if user has admin access
      if (!hasPermission('admin:access')) {
        throw new Error('Only administrators can manage products');
      }

      const response = await createProduct(productData);
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["products"] });
      toast.success("Product created successfully");
    },
    onError: (error: Error) => {
      toast.error("Failed to create product: " + error.message);
    },
  });
}

// Update a product
export function useUpdateProduct() {
  const queryClient = useQueryClient();
  const { hasPermission } = usePermissions();

  return useMutation({
    mutationFn: async ({
      id,
      data,
    }: {
      id: string;
      data: UpdateProductRequest;
    }) => {
      // Check if user has admin access
      if (!hasPermission('admin:access')) {
        throw new Error('Only administrators can manage products');
      }

      const response = await updateProduct(id, data);
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["products"] });
      toast.success("Product updated successfully");
    },
    onError: (error: Error) => {
      toast.error("Failed to update product: " + error.message);
    },
  });
}

// Delete a product
export function useDeleteProduct() {
  const queryClient = useQueryClient();
  const { hasPermission } = usePermissions();

  return useMutation({
    mutationFn: async (id: string) => {
      // Check if user has admin access
      if (!hasPermission('admin:access')) {
        throw new Error('Only administrators can manage products');
      }

      await deleteProduct(id);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["products"] });
      toast.success("Product deleted successfully");
    },
    onError: (error: Error) => {
      toast.error("Failed to delete product: " + error.message);
    },
  });
}
