import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from '@/components/ui/use-toast';
import { faqService } from '@/lib/api/faqs';
import {
  FAQ,
  CreateFAQRequest,
  UpdateFAQRequest,
  FlagFAQRequest,
  BatchCreateFAQResponse,
  FAQStatus
} from '@/lib/api/types';
import { useCallback } from 'react';
import { usePermissions } from './usePermissions';
import { useAuth } from './useAuth';

/**
 * Hook for managing FAQs
 */
export const useFAQs = () => {
  const queryClient = useQueryClient();
  const { hasPermission } = usePermissions();
  const { user } = useAuth();

  // Fetch all FAQs
  const {
    data: faqsData,
    isLoading: isLoadingFAQs,
    error: faqsError,
    refetch: refetchFAQs,
  } = useQuery({
    queryKey: ['faqs'],
    queryFn: async () => {
      const response = await faqService.getAllFAQs();
      return response.data || [];
    },
  });

  // Get FAQ by ID
  const getFAQById = useCallback(async (id: string) => {
    try {
      const response = await faqService.getFAQById(id);
      return response.data;
    } catch (error) {
      console.error('Error fetching FAQ:', error);
      return null;
    }
  }, []);

  // Create a new FAQ
  const createFAQMutation = useMutation({
    mutationFn: (data: CreateFAQRequest) => {
      if (!hasPermission('faq:create')) {
        throw new Error('You do not have permission to create FAQs');
      }
      return faqService.createFAQ(data);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['faqs'] });
      toast({
        title: 'Success',
        description: 'FAQ created successfully',
      });
    },
    onError: (error: any) => {
      toast({
        title: 'Error',
        description: error.message || 'Failed to create FAQ',
        variant: 'destructive',
      });
    },
  });

  // Update an existing FAQ
  const updateFAQMutation = useMutation({
    mutationFn: ({ id, data }: { id: string; data: UpdateFAQRequest }) => {
      if (!hasPermission('faq:update')) {
        throw new Error('You do not have permission to update FAQs');
      }
      return faqService.updateFAQ(id, data);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['faqs'] });
      toast({
        title: 'Success',
        description: 'FAQ updated successfully',
      });
    },
    onError: (error: any) => {
      toast({
        title: 'Error',
        description: error.message || 'Failed to update FAQ',
        variant: 'destructive',
      });
    },
  });

  // Delete an FAQ
  const deleteFAQMutation = useMutation({
    mutationFn: (id: string) => {
      if (!hasPermission('faq:delete')) {
        throw new Error('You do not have permission to delete FAQs');
      }
      return faqService.deleteFAQ(id);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['faqs'] });
      toast({
        title: 'Success',
        description: 'FAQ deleted successfully',
      });
    },
    onError: (error: any) => {
      toast({
        title: 'Error',
        description: error.message || 'Failed to delete FAQ',
        variant: 'destructive',
      });
    },
  });

  // Flag an FAQ
  const flagFAQMutation = useMutation({
    mutationFn: ({ id, data, faq }: { id: string; data: FlagFAQRequest; faq?: FAQ }) => {
      // Check permission to flag
      if (!hasPermission('faq:flag')) {
        throw new Error('You do not have permission to flag FAQs');
      }

      // If trying to unflag, check if current user is the one who flagged it
      if (faq && faq.is_flagged && !data.is_flagged) {
        // If user is not admin and not the one who flagged it, prevent unflagging
        const isAdmin = hasPermission('faq:admin') || hasPermission('admin:access');
        if (!isAdmin && faq.flagged_by && faq.flagged_by !== user?.id) {
          throw new Error('You can only unflag FAQs that you have flagged');
        }
      }

      return faqService.flagFAQ(id, data);
    },
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: ['faqs'] });
      toast({
        title: 'Success',
        description: variables.data.is_flagged
          ? 'FAQ flagged successfully'
          : 'FAQ unflagged successfully',
      });
    },
    onError: (error: any) => {
      toast({
        title: 'Error',
        description: error.message || 'Failed to update FAQ flag status',
        variant: 'destructive',
      });
    },
  });

  // Create FAQs in batch
  const createFAQsBatchMutation = useMutation({
    mutationFn: (data: CreateFAQRequest[]) => {
      // Check for both create permission and admin access
      if (!hasPermission('faq:create')) {
        throw new Error('You do not have permission to create FAQs');
      }

      // Only admin users can use batch upload
      if (!hasPermission('admin:access')) {
        throw new Error('Only administrators can use batch upload');
      }

      return faqService.createFAQsBatch(data);
    },
    onSuccess: (response) => {
      queryClient.invalidateQueries({ queryKey: ['faqs'] });

      if (response.data) {
        const { success, failed } = response.data;

        if (success > 0 && failed === 0) {
          toast({
            title: 'Success',
            description: `Successfully created ${success} FAQs`,
          });
        } else if (success > 0 && failed > 0) {
          toast({
            title: 'Partial Success',
            description: `Created ${success} FAQs, but ${failed} failed`,
            variant: 'default',
          });
        } else {
          toast({
            title: 'Error',
            description: `Failed to create any FAQs`,
            variant: 'destructive',
          });
        }
      }
    },
    onError: (error: any) => {
      toast({
        title: 'Error',
        description: error.message || 'Failed to create FAQs in batch',
        variant: 'destructive',
      });
    },
  });

  // Filter FAQs by category
  const filterFAQsByCategory = useCallback((categoryId: string): FAQ[] => {
    if (!faqsData) return [];
    return faqsData.filter(faq => faq.category_id === categoryId);
  }, [faqsData]);

  // Filter FAQs by product
  const filterFAQsByProduct = useCallback((productId: string): FAQ[] => {
    if (!faqsData) return [];
    return faqsData.filter(faq => faq.product_id === productId);
  }, [faqsData]);

  // Filter flagged FAQs
  const getFlaggedFAQs = useCallback((): FAQ[] => {
    if (!faqsData) return [];
    return faqsData.filter(faq => faq.is_flagged);
  }, [faqsData]);

  // Approve an FAQ
  const approveFAQMutation = useMutation({
    mutationFn: ({ id, notes }: { id: string; notes?: string }) => {
      if (!hasPermission('supervisor:access') && !hasPermission('admin:access')) {
        throw new Error('You do not have permission to approve FAQs');
      }

      const data = {
        status: FAQStatus.APPROVED,
        approved_by: user?.id,
        approved_at: new Date().toISOString(),
      };

      return faqService.updateFAQ(id, data);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['faqs'] });
      toast({
        title: 'Success',
        description: 'FAQ approved successfully',
      });
    },
    onError: (error: any) => {
      toast({
        title: 'Error',
        description: error.message || 'Failed to approve FAQ',
        variant: 'destructive',
      });
    },
  });

  // Reject an FAQ
  const rejectFAQMutation = useMutation({
    mutationFn: ({ id, reason }: { id: string; reason: string }) => {
      if (!hasPermission('supervisor:access') && !hasPermission('admin:access')) {
        throw new Error('You do not have permission to reject FAQs');
      }

      const data = {
        status: FAQStatus.REJECTED,
        rejected_reason: reason,
      };

      return faqService.updateFAQ(id, data);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['faqs'] });
      toast({
        title: 'Success',
        description: 'FAQ rejected successfully',
      });
    },
    onError: (error: any) => {
      toast({
        title: 'Error',
        description: error.message || 'Failed to reject FAQ',
        variant: 'destructive',
      });
    },
  });

  return {
    faqs: faqsData || [],
    isLoading: isLoadingFAQs,
    error: faqsError,
    refetchFAQs,
    getFAQById,
    createFAQ: createFAQMutation.mutate,
    updateFAQ: updateFAQMutation.mutate,
    deleteFAQ: deleteFAQMutation.mutate,
    flagFAQ: flagFAQMutation.mutate,
    createFAQsBatch: createFAQsBatchMutation.mutate,
    approveFAQ: approveFAQMutation.mutate,
    rejectFAQ: rejectFAQMutation.mutate,
    filterFAQsByCategory,
    filterFAQsByProduct,
    getFlaggedFAQs,
    isCreatingFAQ: createFAQMutation.isPending,
    isUpdatingFAQ: updateFAQMutation.isPending,
    isDeletingFAQ: deleteFAQMutation.isPending,
    isFlaggingFAQ: flagFAQMutation.isPending,
    isCreatingFAQsBatch: createFAQsBatchMutation.isPending,
    isApprovingFAQ: approveFAQMutation.isPending,
    isRejectingFAQ: rejectFAQMutation.isPending,
  };
};
