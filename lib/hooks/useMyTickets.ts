'use client';

import { useAuth } from "./useAuth";
import { useTickets } from "./useTickets";
import { PaginationParams } from "../api";

/**
 * Custom hook to fetch tickets assigned to the current user
 * Combines useAuth and useTickets to automatically filter tickets by the logged-in user's ID
 */
export const useMyTickets = (
  params?: Omit<PaginationParams, "assigned_to"> & {
    status?: string;
    priority?: string;
    category_id?: string;
    contact_id?: string;
    channel_id?: string;
    product_id?: string;
    escalated?: boolean; // Parameter to filter by escalation status
    is_escalated?: boolean; // Legacy parameter, will be converted to escalated
    [key: string]: any; // Allow any additional parameters
  }
) => {
  // Get current user from auth hook
  const { user, isLoadingUser } = useAuth();

  // Get tickets with the assigned_to filter set to current user's ID
  const {
    tickets,
    isLoadingTickets,
    ticketsError,
    refetchTickets,
    pagination,
    ...otherTicketMethods
  } = useTickets({
    ...params,
    assigned_to: user?.id,
  });

  // Determine if we're in a loading state
  const isLoading = isLoadingUser || isLoadingTickets;

  // Custom error message for API connection issues
  let errorMessage = "Error loading your tickets. Please try again.";

  if (ticketsError) {
    if (
      ticketsError instanceof TypeError &&
      (String(ticketsError).includes("Failed to fetch") ||
        String(ticketsError).includes("NetworkError") ||
        String(ticketsError).includes("ERR_CONNECTION_REFUSED"))
    ) {
      errorMessage = "System is offline. Please check your connection or contact support.";
    }
  }

  return {
    // Data
    myTickets: tickets,
    user,
    pagination,

    // Status
    isLoading,
    error: ticketsError,
    errorMessage,

    // Actions
    refetchTickets,

    // Pass through all other ticket methods
    ...otherTicketMethods,
  };
};
