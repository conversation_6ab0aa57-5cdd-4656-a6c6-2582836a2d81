'use client';

import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { useRouter } from 'next/navigation';
import { AuthResponse, authService, LoginRequest, UpdatePasswordRequest } from '../api';

export const useAuth = () => {
  const router = useRouter();
  const queryClient = useQueryClient();

  // Get current user query
  const {
    data: user,
    isLoading: isLoadingUser,
    error: userError,
    refetch: refetchUser,
  } = useQuery({
    queryKey: ['currentUser'],
    queryFn: () => authService.getCurrentUser(),
    retry: 1,
    enabled: authService.isAuthenticated(),
  });

  // Login mutation
  const {
    mutate: login,
    isPending: isLoggingIn,
    error: loginError,
  } = useMutation<AuthResponse, string, LoginRequest>({
    mutationFn: async (data: LoginRequest) => {
      try {
        const response = await authService.login(data);

        // If login failed, throw an error with the message
        if (!response.success) {
          if (response.message?.includes("device is already being used")) {
            throw new Error("This device is already being used by another account. Please log out from the other account first.");
          }
          throw new Error(response.message || 'Login failed. Please check your credentials.');
        }

        return response;
      } catch (error: any) {
        // Ensure we throw a string error message
        throw error.message || 'An error occurred during login';
      }
    },
    onSuccess: (response) => {
      if (response.success) {
        // Check if account requires password change
        if (response.data?.requiresPasswordChange && response.data.temporaryToken) {
          // Store the temporary token for the password change flow
          localStorage.setItem('temporaryToken', response.data.temporaryToken);

          // Redirect to change password page
          router.push('/auth/change-password');
        } else if (response.tokens) {
          // Normal login flow - store tokens and redirect to dashboard
          localStorage.setItem('accessToken', response.tokens.accessToken);
          localStorage.setItem('refreshToken', response.tokens.refreshToken);

          // Invalidate current user query to trigger a refetch
          queryClient.invalidateQueries({ queryKey: ['currentUser'] });

          // Redirect to dashboard
          router.push('/dashboard');
        }
      }
    },
  });

  // Logout mutation
  const {
    mutate: logout,
    isPending: isLoggingOut,
  } = useMutation({
    mutationFn: () => authService.logout(),
    onSuccess: () => {
      // Clear user from query cache
      queryClient.setQueryData(['currentUser'], null);
      queryClient.clear();
      router.push('/auth/login');
    },
  });

  // Forgot password mutation
  const {
    mutate: forgotPassword,
    isPending: isForgotPasswordPending,
    error: forgotPasswordError,
  } = useMutation({
    mutationFn: (phone_number: string) => authService.requestPasswordReset(phone_number),
  });

  // Reset password mutation
  const {
    mutate: resetPassword,
    isPending: isResettingPassword,
    error: resetPasswordError,
  } = useMutation({
    mutationFn: ({
      token,
      password,
      email,
    }: {
      token: string;
      password: string;
      email: string;
    }) => authService.resetPassword(token, password, email),
    onSuccess: () => {
      router.push('/auth/login');
    },
  });

  // Update password mutation
  const {
    mutate: updatePassword,
    isPending: isUpdatingPassword,
    error: updatePasswordError,
  } = useMutation({
    mutationFn: (data: UpdatePasswordRequest) => authService.updatePassword(data),
  });

  return {
    user,
    isLoadingUser,
    userError,
    refetchUser,
    isAuthenticated: authService.isAuthenticated(),

    login,
    isLoggingIn,
    loginError,

    logout,
    isLoggingOut,

    forgotPassword,
    isForgotPasswordPending,
    forgotPasswordError,

    resetPassword,
    isResettingPassword,
    resetPasswordError,

    updatePassword,
    isUpdatingPassword,
    updatePasswordError,
  };
};
