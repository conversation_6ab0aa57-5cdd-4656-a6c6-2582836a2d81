import { useQuery } from '@tanstack/react-query';
import { contactService, ContactAnalyticsParams, ContactAnalyticsResponse } from '@/lib/api/contacts';
import { useAuth } from './useAuth';
import { useDashboardPermissions } from './useDashboardPermissions';

export const useContactAnalytics = (params: ContactAnalyticsParams) => {
  const { user } = useAuth();
  const permissions = useDashboardPermissions();

  // Adjust params based on user permissions
  const adjustedParams = {
    ...params,
    // If user can only see own data, filter by their agent ID
    ...(permissions.dataScope === 'own' && user?.id ? { agentId: user.id } : {}),
  };

  return useQuery<ContactAnalyticsResponse>({
    queryKey: ['contact-analytics', adjustedParams],
    queryFn: () => contactService.getContactAnalytics(adjustedParams),
    enabled: true,
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchOnWindowFocus: false,
  });
};

// Mock data generator for development/testing
export const generateMockContactAnalytics = (params: ContactAnalyticsParams): ContactAnalyticsResponse => {
  const now = new Date();

  // Safely parse dates with fallbacks
  let startDate: Date;
  let endDate: Date;

  try {
    startDate = params.startDate ? new Date(params.startDate) : new Date(now.getFullYear(), now.getMonth(), 1);
    endDate = params.endDate ? new Date(params.endDate) : now;

    // Validate dates
    if (isNaN(startDate.getTime())) {
      startDate = new Date(now.getFullYear(), now.getMonth(), 1);
    }
    if (isNaN(endDate.getTime())) {
      endDate = now;
    }
  } catch (error) {
    console.warn('Error parsing dates in generateMockContactAnalytics:', error);
    startDate = new Date(now.getFullYear(), now.getMonth(), 1);
    endDate = now;
  }
  
  // Calculate days between dates
  const daysDiff = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));
  
  // Generate trend data
  const trends = Array.from({ length: Math.min(daysDiff, 30) }, (_, i) => {
    const date = new Date(startDate);
    date.setDate(date.getDate() + i);
    return {
      date: date.toISOString().split('T')[0],
      count: Math.floor(Math.random() * 20) + 5,
    };
  });

  const totalContacts = trends.reduce((sum, trend) => sum + trend.count, 0);
  const previousPeriodTotal = Math.floor(totalContacts * (0.8 + Math.random() * 0.4));
  const growthRate = previousPeriodTotal > 0 ? ((totalContacts - previousPeriodTotal) / previousPeriodTotal) * 100 : 0;

  return {
    metrics: {
      totalContacts,
      newContacts: totalContacts,
      growthRate: Math.round(growthRate * 100) / 100,
      previousPeriodTotal,
    },
    trends,
    productBreakdown: [
      { productId: '1', productName: 'Internet Fiber', count: Math.floor(totalContacts * 0.4), percentage: 40 },
      { productId: '2', productName: 'Mobile Plans', count: Math.floor(totalContacts * 0.3), percentage: 30 },
      { productId: '3', productName: 'TV Package', count: Math.floor(totalContacts * 0.2), percentage: 20 },
      { productId: '4', productName: 'Business Solutions', count: Math.floor(totalContacts * 0.1), percentage: 10 },
    ],
    categoryBreakdown: [
      { category: 'customer', count: Math.floor(totalContacts * 0.5), percentage: 50 },
      { category: 'lead', count: Math.floor(totalContacts * 0.3), percentage: 30 },
      { category: 'partner', count: Math.floor(totalContacts * 0.15), percentage: 15 },
      { category: 'vendor', count: Math.floor(totalContacts * 0.05), percentage: 5 },
    ],
  };
};
