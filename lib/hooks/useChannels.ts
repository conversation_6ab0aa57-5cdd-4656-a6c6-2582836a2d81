import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import {
  getChannels,
  createChannel,
  updateChannel,
  deleteChannel,
} from "@/lib/api/channels";
import type {
  Channel,
  CreateChannelRequest,
  UpdateChannelRequest,
} from "@/lib/api/types";

// Get all channels
export function useChannels() {
  return useQuery<Channel[]>({
    queryKey: ["channels"],
    queryFn: async () => {
      const response = await getChannels();
      return response.data || [];
    },
  });
}

// Create a new channel
export function useCreateChannel() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (channelData: CreateChannelRequest) => {
      const response = await createChannel(channelData);
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["channels"] });
      toast.success("Channel created successfully");
    },
    onError: (error: Error) => {
      toast.error("Failed to create channel: " + error.message);
    },
  });
}

// Update a channel
export function useUpdateChannel() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({
      id,
      data,
    }: {
      id: string;
      data: UpdateChannelRequest;
    }) => {
      const response = await updateChannel(id, data);
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["channels"] });
      toast.success("Channel updated successfully");
    },
    onError: (error: Error) => {
      toast.error("Failed to update channel: " + error.message);
    },
  });
}

// Delete a channel
export function useDeleteChannel() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (id: string) => {
      await deleteChannel(id);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["channels"] });
      toast.success("Channel deleted successfully");
    },
    onError: (error: Error) => {
      toast.error("Failed to delete channel: " + error.message);
    },
  });
}
