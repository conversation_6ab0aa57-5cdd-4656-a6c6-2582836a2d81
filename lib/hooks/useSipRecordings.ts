/**
 * React Hook for SIP Recordings
 * 
 * Provides easy access to SIP recording data with caching and error handling
 */

import { useState, useEffect, useCallback } from 'react';
import { sipRecordingService, SipRecording } from '@/lib/services/sip-recording-service';
import { CallLog } from '@/lib/services/socket-service';

interface UseSipRecordingsReturn {
  recordings: SipRecording[];
  loading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
  getRecordingForCall: (call: CallLog) => SipRecording | null;
  hasRecording: (call: CallLog) => boolean;
  stats: {
    total: number;
    inbound: number;
    outbound: number;
    today: number;
  } | null;
}

export function useSipRecordings(): UseSipRecordingsReturn {
  const [recordings, setRecordings] = useState<SipRecording[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [stats, setStats] = useState<{
    total: number;
    inbound: number;
    outbound: number;
    today: number;
  } | null>(null);

  const fetchRecordings = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      
      console.log('Fetching SIP recordings...');
      const [recordingsData, statsData] = await Promise.all([
        sipRecordingService.getAllRecordings(),
        sipRecordingService.getRecordingStats()
      ]);
      
      console.log('Fetched recordings:', recordingsData.length);
      setRecordings(recordingsData);
      setStats(statsData);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch recordings';
      console.error('Error fetching SIP recordings:', err);
      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  }, []);

  // Initial fetch
  useEffect(() => {
    fetchRecordings();
  }, [fetchRecordings]);

  // Refetch function
  const refetch = useCallback(async () => {
    await fetchRecordings();
  }, [fetchRecordings]);

  // Find recording for a specific call
  const getRecordingForCall = useCallback((call: CallLog): SipRecording | null => {
    if (!recordings.length) return null;

    // Clean phone numbers for comparison
    const cleanCallerNumber = call.callerNumber.replace(/\D/g, '');
    
    const matchingRecording = recordings.find(recording => {
      const cleanRecordingNumber = recording.callerNumber.replace(/\D/g, '');
      
      // Primary match: phone number
      const phoneMatch = cleanRecordingNumber === cleanCallerNumber ||
                        cleanRecordingNumber.includes(cleanCallerNumber) ||
                        cleanCallerNumber.includes(cleanRecordingNumber);
      
      if (!phoneMatch) return false;

      // Secondary match: call ID if available
      if (call.callId && recording.callId) {
        return recording.callId === call.callId;
      }

      // Tertiary match: timestamp proximity (within 5 minutes)
      if (call.timestamp && recording.timestamp) {
        const callTime = new Date(call.timestamp).getTime();
        const recordingTime = new Date(recording.timestamp).getTime();
        const timeDiff = Math.abs(callTime - recordingTime);
        const fiveMinutes = 5 * 60 * 1000;
        
        return timeDiff <= fiveMinutes;
      }

      // If no additional criteria, accept phone match
      return true;
    });

    return matchingRecording || null;
  }, [recordings]);

  // Check if a call has a recording
  const hasRecording = useCallback((call: CallLog): boolean => {
    return getRecordingForCall(call) !== null;
  }, [getRecordingForCall]);

  return {
    recordings,
    loading,
    error,
    refetch,
    getRecordingForCall,
    hasRecording,
    stats
  };
}

/**
 * Hook for a specific call's recording
 */
export function useCallRecording(call: CallLog | null) {
  const { recordings, loading, error, getRecordingForCall } = useSipRecordings();
  const [recording, setRecording] = useState<SipRecording | null>(null);

  useEffect(() => {
    if (call && recordings.length > 0) {
      const foundRecording = getRecordingForCall(call);
      setRecording(foundRecording);
    } else {
      setRecording(null);
    }
  }, [call, recordings, getRecordingForCall]);

  return {
    recording,
    loading,
    error,
    hasRecording: recording !== null
  };
}
