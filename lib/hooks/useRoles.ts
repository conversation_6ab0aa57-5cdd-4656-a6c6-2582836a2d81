import { useQuery } from '@tanstack/react-query';
import { roleService } from '@/lib/api/roles';
import { Role } from '@/lib/api/types';
import { useCallback, useMemo } from 'react';
import {
  roleSchema,
  RoleLevels,
  validateRoleLevel,
  getRoleName,
  type RoleLevel
} from '@/lib/validations/roles';

export const useRoles = () => {
  // Fetch all roles
  const {
    data: rolesData,
    isLoading: isLoadingRoles,
    error: rolesError,
    refetch: refetchRoles,
  } = useQuery({
    queryKey: ['roles'],
    queryFn: async () => {
      const response = await roleService.getAllRoles();
      // Validate the response data
      const validatedRoles = (response.data || []).map(role => {
        try {
          // Fix invalid permission levels before validation
          if (typeof role.permission_level === 'number') {
            // Ensure permission_level is within valid range (1-10)
            if (role.permission_level > 10) {
              console.warn(`Role ${role.id} has permission_level > 10, capping to 10 (Platform Owner)`);
              role.permission_level = 10; // Cap at Platform Owner level
            } else if (role.permission_level < 1) {
              console.warn(`Role ${role.id} has permission_level < 1, setting to 1 (Agent)`);
              role.permission_level = 1; // Set to Agent level
            }
          }
          return roleSchema.parse(role);
        } catch (error) {
          console.error(`Invalid role data for role ${role.id}:`, error);
          return null;
        }
      }).filter((role): role is Role => role !== null);
      return validatedRoles;
    },
  });

  // Get role by level (useful for permission checks)
  const getRoleByLevel = useCallback((level: number) => {
    if (!rolesData || !validateRoleLevel(level)) return null;
    return rolesData.find(role => role.permission_level === level);
  }, [rolesData]);

  // Check if a role is an admin role
  const isAdminRole = useCallback((role: Role) => {
    return role.permission_level >= RoleLevels.TEAM_LEAD;
  }, []);

  // Get role by ID
  const getRoleById = useCallback((id: string) => {
    console.log(`Getting role by ID: ${id}`);

    if (!rolesData) {
      console.log('No roles data available');
      return null;
    }

    console.log('Available roles:', rolesData.map(r => ({ id: r.id, name: r.name })));

    const role = rolesData.find(role => role.id === id);
    console.log('Found role:', role);

    return role;
  }, [rolesData]);

  // Get role name by level
  const getRoleNameByLevel = useCallback((level: number) => {
    if (!validateRoleLevel(level)) return 'Unknown Role';
    return getRoleName(level as RoleLevel);
  }, []);

  // Sort roles by permission level
  const sortedRoles = useMemo(() =>
    rolesData?.slice().sort((a, b) => b.permission_level - a.permission_level) || [],
    [rolesData]
  );

  // Get available role levels
  const availableRoleLevels = useMemo(() =>
    Object.values(RoleLevels).filter(level => typeof level === 'number'),
    []
  );

  return {
    roles: sortedRoles,
    isLoading: isLoadingRoles,
    error: rolesError,
    refetchRoles,
    getRoleByLevel,
    getRoleById,
    isAdminRole,
    getRoleNameByLevel,
    availableRoleLevels,
  };
};
