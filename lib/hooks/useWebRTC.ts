/**
 * React Hook for WebRTC Integration
 * 
 * Provides easy access to WebRTC calling functionality with automatic
 * agent registration based on current user authentication.
 */

import { useState, useEffect, useCallback } from 'react';
import { useAuth } from './useAuth';
import { webrtcService, WebRTCCallSession, WebRTCEventData, AgentSipCredentials } from '@/lib/services/webrtc-service';

interface UseWebRTCReturn {
  // Registration state
  isEnabled: boolean;
  isRegistered: boolean;
  isRegistering: boolean;
  registrationError: string | null;
  currentAgent: AgentSipCredentials | null;

  // Call state
  activeCalls: WebRTCCallSession[];
  incomingCall: WebRTCCallSession | null;
  
  // Actions
  registerAgent: () => Promise<boolean>;
  unregisterAgent: () => Promise<void>;
  answerCall: (sessionId: string) => boolean;
  rejectCall: (sessionId: string) => boolean;
  endCall: (sessionId: string) => boolean;
  
  // Connection state
  isConnected: boolean;
}

export function useWebRTC(): UseWebRTCReturn {
  const { user } = useAuth();
  
  // Registration state
  const [isEnabled] = useState(() => webrtcService.isEnabled());
  const [isRegistered, setIsRegistered] = useState(false);
  const [isRegistering, setIsRegistering] = useState(false);
  const [registrationError, setRegistrationError] = useState<string | null>(null);
  const [currentAgent, setCurrentAgent] = useState<AgentSipCredentials | null>(null);
  
  // Call state
  const [activeCalls, setActiveCalls] = useState<WebRTCCallSession[]>([]);
  const [incomingCall, setIncomingCall] = useState<WebRTCCallSession | null>(null);
  
  // Connection state
  const [isConnected, setIsConnected] = useState(false);

  // Generate agent credentials from user data
  const generateAgentCredentials = useCallback((user: any): AgentSipCredentials => {
    // For now, we'll use the base extension (1001) for all agents
    // In the future, this could be mapped to user.id or user.extension
    const extension = process.env.NEXT_PUBLIC_SIP_EXTENSION_BASE || '1001';
    const password = process.env.NEXT_PUBLIC_SIP_PASSWORD || '';
    
    return {
      extension,
      password,
      displayName: user.name || user.email || 'Agent',
      userId: user.id || user.email
    };
  }, []);

  // Register agent with SIP server
  const registerAgent = useCallback(async (): Promise<boolean> => {
    if (!user || !isEnabled) {
      console.warn('WebRTC: Cannot register - user not authenticated or WebRTC not enabled');
      return false;
    }

    if (isRegistered || isRegistering) {
      console.log('WebRTC: Already registered or registering');
      return isRegistered;
    }

    try {
      setIsRegistering(true);
      setRegistrationError(null);

      const credentials = generateAgentCredentials(user);
      setCurrentAgent(credentials);

      console.log('WebRTC: Attempting to register agent:', credentials.extension);
      const success = await webrtcService.registerAgent(credentials);
      
      if (!success) {
        setRegistrationError('Failed to start registration process');
        setCurrentAgent(null);
      }

      return success;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown registration error';
      console.error('WebRTC: Registration error:', errorMessage);
      setRegistrationError(errorMessage);
      setCurrentAgent(null);
      return false;
    } finally {
      setIsRegistering(false);
    }
  }, [user, isEnabled, isRegistered, isRegistering, generateAgentCredentials]);

  // Unregister agent from SIP server
  const unregisterAgent = useCallback(async (): Promise<void> => {
    try {
      await webrtcService.unregisterAgent();
      setCurrentAgent(null);
      setRegistrationError(null);
    } catch (error) {
      console.error('WebRTC: Unregistration error:', error);
    }
  }, []);

  // Call control functions
  const answerCall = useCallback((sessionId: string): boolean => {
    return webrtcService.answerCall(sessionId);
  }, []);

  const rejectCall = useCallback((sessionId: string): boolean => {
    return webrtcService.rejectCall(sessionId);
  }, []);

  const endCall = useCallback((sessionId: string): boolean => {
    return webrtcService.endCall(sessionId);
  }, []);

  // Set up WebRTC event listeners
  useEffect(() => {
    if (!isEnabled) return;

    const handleRegistered = (data: WebRTCEventData) => {
      console.log('WebRTC Hook: Agent registered');
      setIsRegistered(true);
      setIsRegistering(false);
      setRegistrationError(null);
    };

    const handleUnregistered = (data: WebRTCEventData) => {
      console.log('WebRTC Hook: Agent unregistered');
      setIsRegistered(false);
      setIsRegistering(false);
      setCurrentAgent(null);
    };

    const handleRegistrationFailed = (data: WebRTCEventData) => {
      console.error('WebRTC Hook: Registration failed:', data.error);
      setIsRegistered(false);
      setIsRegistering(false);
      setRegistrationError(data.error || 'Registration failed');
      setCurrentAgent(null);
    };

    const handleIncomingCall = (data: WebRTCEventData) => {
      console.log('WebRTC Hook: Incoming call received');
      if (data.session) {
        setIncomingCall(data.session);
        setActiveCalls(prev => [...prev, data.session!]);
      }
    };

    const handleCallAnswered = (data: WebRTCEventData) => {
      console.log('WebRTC Hook: Call answered');
      setIncomingCall(null); // Clear incoming call when answered
      if (data.session) {
        setActiveCalls(prev => 
          prev.map(call => 
            call.id === data.session!.id 
              ? { ...call, status: 'answered' }
              : call
          )
        );
      }
    };

    const handleCallEnded = (data: WebRTCEventData) => {
      console.log('WebRTC Hook: Call ended');
      if (data.session) {
        setActiveCalls(prev => prev.filter(call => call.id !== data.session!.id));
        if (incomingCall?.id === data.session.id) {
          setIncomingCall(null);
        }
      }
    };

    const handleCallFailed = (data: WebRTCEventData) => {
      console.log('WebRTC Hook: Call failed');
      if (data.session) {
        setActiveCalls(prev => prev.filter(call => call.id !== data.session!.id));
        if (incomingCall?.id === data.session.id) {
          setIncomingCall(null);
        }
      }
    };

    const handleConnectionStateChanged = (data: WebRTCEventData) => {
      console.log('WebRTC Hook: Connection state changed:', data.data?.connected);
      setIsConnected(data.data?.connected || false);
    };

    // Register event listeners
    webrtcService.on('registered', handleRegistered);
    webrtcService.on('unregistered', handleUnregistered);
    webrtcService.on('registrationFailed', handleRegistrationFailed);
    webrtcService.on('incomingCall', handleIncomingCall);
    webrtcService.on('callAnswered', handleCallAnswered);
    webrtcService.on('callEnded', handleCallEnded);
    webrtcService.on('callFailed', handleCallFailed);
    webrtcService.on('connectionStateChanged', handleConnectionStateChanged);

    // Cleanup function
    return () => {
      webrtcService.off('registered', handleRegistered);
      webrtcService.off('unregistered', handleUnregistered);
      webrtcService.off('registrationFailed', handleRegistrationFailed);
      webrtcService.off('incomingCall', handleIncomingCall);
      webrtcService.off('callAnswered', handleCallAnswered);
      webrtcService.off('callEnded', handleCallEnded);
      webrtcService.off('callFailed', handleCallFailed);
      webrtcService.off('connectionStateChanged', handleConnectionStateChanged);
    };
  }, [isEnabled, incomingCall]);

  // Auto-register when user logs in
  useEffect(() => {
    if (user && isEnabled && !isRegistered && !isRegistering) {
      console.log('WebRTC Hook: User authenticated, attempting auto-registration');
      registerAgent();
    }
  }, [user, isEnabled, isRegistered, isRegistering, registerAgent]);

  // Auto-unregister when user logs out
  useEffect(() => {
    if (!user && isRegistered) {
      console.log('WebRTC Hook: User logged out, unregistering agent');
      unregisterAgent();
    }
  }, [user, isRegistered, unregisterAgent]);

  return {
    // Registration state
    isEnabled,
    isRegistered,
    isRegistering,
    registrationError,
    currentAgent,

    // Call state
    activeCalls,
    incomingCall,

    // Actions
    registerAgent,
    unregisterAgent,
    answerCall,
    rejectCall,
    endCall,

    // Connection state
    isConnected,
  };
}
