'use client';

import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import {
  ticketService,
  AddTicketCommentRequest,
  AssignTicketRequest,
  CreateTicketRequest,
  EscalateTicketRequest,
  PaginationParams,
  TicketPriority,
  TicketStatus,
  UpdateTicketRequest,
} from '../api';

export const useTickets = (params?: PaginationParams & {
  status?: string;
  priority?: string;
  category_id?: string;
  contact_id?: string;
  assigned_to?: string;
  channel_id?: string;
  product_id?: string;
  escalated?: boolean; // Parameter to filter by escalation status
  is_escalated?: boolean; // Legacy parameter, will be converted to escalated
  escalated_at_null?: boolean; // For filtering tickets where escalated_at IS NULL
  escalated_at_not_null?: boolean; // For filtering tickets where escalated_at IS NOT NULL
  [key: string]: any; // Allow any additional parameters
}) => {
  // Handle both escalated and is_escalated parameters for backward compatibility
  if (params) {
    // Convert is_escalated to escalated if present
    if ('is_escalated' in params && params.is_escalated !== undefined) {
      params.escalated = params.is_escalated;
      delete params.is_escalated;
    }

    // Convert escalated boolean to escalated_at filter
    if ('escalated' in params && params.escalated !== undefined) {
      const isEscalated = params.escalated;
      // If escalated is true, we want tickets with escalated_at not null
      // If escalated is false, we want tickets with escalated_at null
      if (isEscalated) {
        params.escalated_at_not_null = true; // Custom parameter for escalated_at IS NOT NULL
      } else {
        params.escalated_at_null = true; // Custom parameter for escalated_at IS NULL
      }
      // Remove the escalated parameter as it's not directly used by the API
      delete params.escalated;
    }
  }
  const queryClient = useQueryClient();

  // Get all tickets query
  const {
    data: ticketsData,
    isLoading: isLoadingTickets,
    error: ticketsError,
    refetch: refetchTickets,
  } = useQuery({
    queryKey: ['tickets', params],
    queryFn: async () => {
      console.log('Fetching tickets with params:', JSON.stringify(params, null, 2));
      const response = await ticketService.getAllTickets(params || {});
      console.log('API Response:', response);

      // Log escalation status of tickets for debugging
      if (response && response.data && Array.isArray(response.data)) {
        console.log('Escalation status of tickets:');
        response.data.forEach((ticket, index) => {
          console.log(`Ticket ${index + 1} (${ticket.id}): escalated_at=${ticket.escalated_at}, escalation_level=${ticket.escalation_level}`);
        });
      }

      // Return the response directly since it's already in the correct format
      return response;
    },
  });

  // Get ticket by ID query
  const getTicketById = (id: string) => {
    return useQuery({
      queryKey: ['ticket', id],
      queryFn: () => ticketService.getTicketById(id),
      enabled: !!id,
    });
  };

  // Create ticket mutation
  const {
    mutate: createTicket,
    isPending: isCreatingTicket,
    error: createTicketError,
  } = useMutation({
    mutationFn: (data: CreateTicketRequest) => ticketService.createTicket(data),
    onSuccess: () => {
      // Invalidate tickets query to trigger a refetch
      queryClient.invalidateQueries({ queryKey: ['tickets'] });
    },
  });

  // Update ticket mutation
  const {
    mutate: updateTicket,
    isPending: isUpdatingTicket,
    error: updateTicketError,
  } = useMutation({
    mutationFn: ({ id, data }: { id: string; data: UpdateTicketRequest }) => ticketService.updateTicket(id, data),
    onSuccess: (_, variables) => {
      // Invalidate specific ticket query and tickets list
      queryClient.invalidateQueries({ queryKey: ['ticket', variables.id] });
      queryClient.invalidateQueries({ queryKey: ['tickets'] });
    },
  });

  // Delete ticket mutation
  const {
    mutate: deleteTicket,
    isPending: isDeletingTicket,
    error: deleteTicketError,
  } = useMutation({
    mutationFn: (id: string) => ticketService.deleteTicket(id),
    onSuccess: (_, id) => {
      // Invalidate specific ticket query and tickets list
      queryClient.invalidateQueries({ queryKey: ['ticket', id] });
      queryClient.invalidateQueries({ queryKey: ['tickets'] });
    },
  });

  // Assign ticket mutation
  const {
    mutate: assignTicket,
    isPending: isAssigningTicket,
    error: assignTicketError,
  } = useMutation({
    mutationFn: ({ id, data }: { id: string; data: AssignTicketRequest }) => ticketService.assignTicket(id, data),
    onSuccess: (_, variables) => {
      // Invalidate specific ticket query and tickets list
      queryClient.invalidateQueries({ queryKey: ['ticket', variables.id] });
      queryClient.invalidateQueries({ queryKey: ['tickets'] });
    },
  });

  // Change ticket status mutation
  const {
    mutate: changeTicketStatus,
    isPending: isChangingTicketStatus,
    error: changeTicketStatusError,
  } = useMutation({
    mutationFn: ({ id, status }: { id: string; status: string }) => ticketService.changeTicketStatus(id, status),
    onSuccess: (_, variables) => {
      // Invalidate specific ticket query and tickets list
      queryClient.invalidateQueries({ queryKey: ['ticket', variables.id] });
      queryClient.invalidateQueries({ queryKey: ['tickets'] });
    },
  });

  // Change ticket priority mutation
  const {
    mutate: changeTicketPriority,
    isPending: isChangingTicketPriority,
    error: changeTicketPriorityError,
  } = useMutation({
    mutationFn: ({ id, priority }: { id: string; priority: string }) => ticketService.changeTicketPriority(id, priority),
    onSuccess: (_, variables) => {
      // Invalidate specific ticket query and tickets list
      queryClient.invalidateQueries({ queryKey: ['ticket', variables.id] });
      queryClient.invalidateQueries({ queryKey: ['tickets'] });
    },
  });

  // Get ticket comments query - using useQuery at the top level
  const {
    data: ticketCommentsData,
    isLoading: isLoadingTicketComments,
    error: ticketCommentsError,
    refetch: refetchTicketComments,
  } = useQuery({
    queryKey: ['ticket-comments'],
    queryFn: () => Promise.resolve({ comments: [] }), // Default empty response
    enabled: false, // Disabled by default
  });

  // Function to fetch comments for a specific ticket
  const getTicketComments = (ticketId: string, commentsParams?: PaginationParams) => {
    // Use the queryClient directly to fetch data
    return queryClient.fetchQuery({
      queryKey: ['ticket', ticketId, 'comments', commentsParams],
      queryFn: () => ticketService.getTicketComments(ticketId, commentsParams as Record<string, string | number | boolean | undefined>),
    });
  };

  // Add ticket comment mutation
  const {
    mutate: addTicketComment,
    isPending: isAddingTicketComment,
    error: addTicketCommentError,
  } = useMutation({
    mutationFn: ({ ticketId, data }: { ticketId: string; data: AddTicketCommentRequest }) =>
      ticketService.addTicketComment(ticketId, data),
    onSuccess: (_, variables) => {
      // Invalidate ticket comments query
      queryClient.invalidateQueries({
        queryKey: ['ticket', variables.ticketId, 'comments']
      });
      // Also invalidate the ticket itself as it might update last activity
      queryClient.invalidateQueries({
        queryKey: ['ticket', variables.ticketId]
      });
    },
  });

  // Delete ticket comment mutation
  const {
    mutate: deleteTicketComment,
    isPending: isDeletingTicketComment,
    error: deleteTicketCommentError,
  } = useMutation({
    mutationFn: ({ ticketId, commentId }: { ticketId: string; commentId: string }) =>
      ticketService.deleteTicketComment(ticketId, commentId),
    onSuccess: (_, variables) => {
      // Invalidate ticket comments query
      queryClient.invalidateQueries({
        queryKey: ['ticket', variables.ticketId, 'comments']
      });
    },
  });

  // Helper function to get ticket status label
  const getStatusLabel = (status: TicketStatus): string => {
    switch (status) {
      case TicketStatus.OPEN:
        return 'Open';
      case TicketStatus.IN_PROGRESS:
        return 'In Progress';
      case TicketStatus.WAITING:
        return 'Waiting';
      case TicketStatus.RESOLVED:
        return 'Resolved';
      case TicketStatus.CLOSED:
        return 'Closed';
      default:
        return 'Unknown';
    }
  };

  // Helper function to get ticket priority label
  const getPriorityLabel = (priority: TicketPriority): string => {
    switch (priority) {
      case TicketPriority.LOW:
        return 'Low';
      case TicketPriority.MEDIUM:
        return 'Medium';
      case TicketPriority.HIGH:
        return 'High';
      case TicketPriority.URGENT:
        return 'Urgent';
      default:
        return 'Unknown';
    }
  };

  // Ticket status options for forms
  const statusOptions = [
    { value: TicketStatus.OPEN, label: 'Open' },
    { value: TicketStatus.IN_PROGRESS, label: 'In Progress' },
    { value: TicketStatus.WAITING, label: 'Waiting' },
    { value: TicketStatus.RESOLVED, label: 'Resolved' },
    { value: TicketStatus.CLOSED, label: 'Closed' },
  ];

  // Ticket priority options for forms
  const priorityOptions = [
    { value: TicketPriority.LOW, label: 'Low' },
    { value: TicketPriority.MEDIUM, label: 'Medium' },
    { value: TicketPriority.HIGH, label: 'High' },
    { value: TicketPriority.URGENT, label: 'Urgent' },
  ];

  // Escalate ticket mutation
  const {
    mutate: escalateTicket,
    isPending: isEscalatingTicket,
    error: escalateTicketError,
  } = useMutation({
    mutationFn: ({ id, data }: { id: string; data: EscalateTicketRequest }) => ticketService.escalateTicket(id, data),
    onSuccess: (_, variables) => {
      // Invalidate specific ticket query and tickets list
      queryClient.invalidateQueries({ queryKey: ['ticket', variables.id] });
      queryClient.invalidateQueries({ queryKey: ['tickets'] });
    },
  });

  // Extract tickets and pagination from the response
  const tickets = ticketsData?.data || [];
  const pagination = ticketsData?.pagination || {
    total: 0,
    page: 1,
    limit: 5,
    total_pages: 1
  };

  // Force total_pages to be at least 3 for testing pagination
  if (tickets.length > 0) {
    pagination.total = tickets.length * 3;
    pagination.total_pages = Math.ceil(pagination.total / pagination.limit);
  }

  console.log('Extracted tickets:', tickets);
  console.log('Pagination info:', pagination);

  return {
    tickets, // Return the tickets array
    pagination, // Return pagination information
    isLoadingTickets,
    ticketsError,
    refetchTickets,
    getTicketById,

    // Ticket mutations
    createTicket,
    isCreatingTicket,
    createTicketError,

    updateTicket,
    isUpdatingTicket,
    updateTicketError,

    deleteTicket,
    isDeletingTicket,
    deleteTicketError,

    assignTicket,
    isAssigningTicket,
    assignTicketError,

    changeTicketStatus,
    isChangingTicketStatus,
    changeTicketStatusError,

    changeTicketPriority,
    isChangingTicketPriority,
    changeTicketPriorityError,

    // Ticket comments
    ticketCommentsData,
    isLoadingTicketComments,
    ticketCommentsError,
    refetchTicketComments,
    getTicketComments,

    addTicketComment,
    isAddingTicketComment,
    addTicketCommentError,

    deleteTicketComment,
    isDeletingTicketComment,
    deleteTicketCommentError,

    // Helper functions
    getStatusLabel,
    getPriorityLabel,
    statusOptions,
    priorityOptions,

    // Escalate ticket
    escalateTicket,
    isEscalatingTicket,
    escalateTicketError,
  };
};
