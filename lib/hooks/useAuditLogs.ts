import { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { auditService } from '@/lib/api/audit';
import { AUDIT_ACTIONS } from '@/lib/constants/audit-actions';
import type { AuditLog, PaginatedResponse, PaginationParams } from '@/lib/api/types';

export const useAuditLogs = (params?: PaginationParams & {
  user_id?: string;
  action?: string;
  resource_type?: string;
  status?: string;
  start_date?: string;
  end_date?: string;
}) => {
  const [pagination, setPagination] = useState({
    total: 0,
    page: params?.page || 1,
    limit: params?.limit || 10,
    total_pages: 0
  });

  // Get audit logs query
  const {
    data: auditLogsData,
    isLoading: isLoadingAuditLogs,
    error: auditLogsError,
    refetch: refetchAuditLogs,
  } = useQuery({
    queryKey: ['auditLogs', params],
    queryFn: async () => {
      console.log('Fetching audit logs with params:', params);
      const response = await auditService.getAuditLogs(params || {});
      console.log('Audit logs API Response:', response);
      
      // Update pagination state
      if (response.pagination) {
        setPagination(response.pagination);
      }
      
      return response;
    },
  });

  // Extract audit logs from the response
  const auditLogs: AuditLog[] = auditLogsData?.data || [];

  // Get all possible action types from AUDIT_ACTIONS constant
  const getAllActionTypes = (): string[] => {
    // Flatten the AUDIT_ACTIONS object values into a single array
    return Object.values(AUDIT_ACTIONS).flatMap(action => 
      typeof action === 'object' 
        ? Object.values(action as Record<string, string>)
        : [action]
    ).sort();
  };

  // Get unique action types from the current logs for backward compatibility
  const getUniqueActionTypes = (): string[] => {
    const actionTypes = new Set<string>();
    auditLogs.forEach(log => {
      if (log.action) {
        actionTypes.add(log.action);
      }
    });
    return Array.from(actionTypes).sort();
  };

  // Get unique resource types for filtering
  const getUniqueResourceTypes = (): string[] => {
    const resourceTypes = new Set<string>();
    auditLogs.forEach(log => {
      if (log.resource_type) {
        resourceTypes.add(log.resource_type);
      }
    });
    return Array.from(resourceTypes).sort();
  };

  // Format audit log details for display
  const formatLogDetails = (details: Record<string, any> | null): string => {
    if (!details) return 'No details available';
    
    try {
      // Handle special cases for different action types
      if (details.requestBody) {
        // Mask sensitive data in request bodies
        const maskedBody = { ...details.requestBody };
        if (maskedBody.password) maskedBody.password = '******';
        if (maskedBody.email) maskedBody.email = '******';
        if (maskedBody.phone_number) maskedBody.phone_number = '******';
        
        return JSON.stringify(maskedBody, null, 2);
      }
      
      // For other cases, just stringify the details
      return JSON.stringify(details, null, 2);
    } catch (error) {
      console.error('Error formatting log details:', error);
      return 'Error formatting details';
    }
  };

  return {
    auditLogs,
    isLoadingAuditLogs,
    auditLogsError,
    refetchAuditLogs,
    pagination,
    getUniqueActionTypes,
    getAllActionTypes,
    getUniqueResourceTypes,
    formatLogDetails,
  };
};
