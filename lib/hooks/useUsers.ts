'use client';

import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import {
  userService,
  CreateUserRequest,
  PaginationParams,
  UpdateUserRequest,
  UserRole,
  UsersResponse,
} from '../api';

export const useUsers = (params?: PaginationParams & {
  search?: string;
  role_id?: string;
  department_id?: string;
  status?: string;
}) => {
  const queryClient = useQueryClient();

  // Get all users query
  const {
    data: usersData,
    isLoading: isLoadingUsers,
    error: usersError,
    refetch: refetchUsers,
  } = useQuery<UsersResponse>({
    queryKey: ['users', params],
    queryFn: () => userService.getAllUsers(params || {}),
  });

  // Get user by ID query
  const getUserById = (id: string) => {
    return useQuery({
      queryKey: ['user', id],
      queryFn: () => userService.getUserById(id),
      enabled: !!id,
    });
  };

  // Create user mutation
  const {
    mutate: createUser,
    isPending: isCreatingUser,
    error: createUserError,
  } = useMutation({
    mutationFn: async (data: CreateUserRequest) => {
      const response = await userService.createUser(data);
      // Check if the response indicates failure
      if (response && typeof response === 'object' && 'success' in response && response.success === false) {
        // Use type assertion to access the message property
        const errorResponse = response as { success: boolean; message?: string };
        throw new Error(errorResponse.message || 'Failed to create user');
      }
      return response;
    },
    onSuccess: () => {
      // Invalidate users query to trigger a refetch
      queryClient.invalidateQueries({ queryKey: ['users'] });
    },
  });

  // Update user mutation
  const {
    mutate: updateUser,
    isPending: isUpdatingUser,
    error: updateUserError,
  } = useMutation({
    mutationFn: async ({ id, data }: { id: string; data: UpdateUserRequest }) => {
      const response = await userService.updateUser(id, data);
      // Check if the response indicates failure
      if (response && typeof response === 'object' && 'success' in response && response.success === false) {
        // Use type assertion to access the message property
        const errorResponse = response as { success: boolean; message?: string };
        throw new Error(errorResponse.message || 'Failed to update user');
      }
      return response;
    },
    onSuccess: (_, variables) => {
      // Invalidate specific user query and users list
      queryClient.invalidateQueries({ queryKey: ['user', variables.id] });
      queryClient.invalidateQueries({ queryKey: ['users'] });
    },
  });

  // Delete user mutation
  const {
    mutate: deleteUser,
    isPending: isDeletingUser,
    error: deleteUserError,
  } = useMutation({
    mutationFn: (id: string) => userService.deleteUser(id),
    onSuccess: (_, id) => {
      // Invalidate specific user query and users list
      queryClient.invalidateQueries({ queryKey: ['user', id] });
      queryClient.invalidateQueries({ queryKey: ['users'] });
    },
  });

  // Get all agents query
  const {
    data: agentsData,
    isLoading: isLoadingAgents,
    error: agentsError,
    refetch: refetchAgents,
  } = useQuery({
    queryKey: ['agents', params],
    queryFn: () => userService.getAllAgents(params || {}),
    enabled: false, // Don't fetch automatically
  });

  // Reactivate user mutation
  const {
    mutate: reactivateUser,
    isPending: isReactivatingUser,
    error: reactivateUserError,
  } = useMutation({
    mutationFn: (id: string) => userService.reactivateUser(id),
    onSuccess: (_, id) => {
      // Invalidate specific user query and users list
      queryClient.invalidateQueries({ queryKey: ['user', id] });
      queryClient.invalidateQueries({ queryKey: ['users'] });
    },
  });

  // Suspend user mutation
  const {
    mutate: suspendUser,
    isPending: isSuspendingUser,
    error: suspendUserError,
  } = useMutation({
    mutationFn: (params: { id: string; reason: string }) => userService.suspendUser(params.id, params.reason),
    onSuccess: (_, variables) => {
      // Invalidate specific user query and users list
      queryClient.invalidateQueries({ queryKey: ['user', variables.id] });
      queryClient.invalidateQueries({ queryKey: ['users'] });
    },
  });

  // Change user role mutation
  const {
    mutate: changeUserRole,
    isPending: isChangingUserRole,
    error: changeUserRoleError,
  } = useMutation({
    mutationFn: ({ id, roleId }: { id: string; roleId: string }) => userService.changeUserRole(id, roleId),
    onSuccess: (_, variables) => {
      // Invalidate specific user query and users list
      queryClient.invalidateQueries({ queryKey: ['user', variables.id] });
      queryClient.invalidateQueries({ queryKey: ['users'] });
    },
  });

  // Helper function to get role label from role enum
  const getRoleLabel = (role: UserRole): string => {
    switch (role) {
      case UserRole.PLATFORM_OWNER:
        return 'Platform Owner';
      case UserRole.SUPERVISOR:
        return 'Supervisor';
      case UserRole.USER:
        return 'User';
      case UserRole.AGENT:
        return 'Agent';
      default:
        return 'Unknown';
    }
  };

  // Helper function to check if a role is an admin role
  const isAdminRole = (role: UserRole): boolean => {
    return [
      UserRole.PLATFORM_OWNER,
      UserRole.SUPERVISOR,
      UserRole.AGENT,
      UserRole.USER,
    ].includes(role);
  };

  return {
    // Users data and queries
    users: usersData?.data || [],
    // pagination is not available in the current API response
    isLoadingUsers,
    usersError,
    refetchUsers,
    getUserById,

    // User mutations
    createUser,
    isCreatingUser,
    createUserError,

    updateUser,
    isUpdatingUser,
    updateUserError,

    deleteUser,
    isDeletingUser,
    deleteUserError,

    // Agents data and queries
    agents: agentsData?.data || [],
    agentsPagination: agentsData?.pagination,
    isLoadingAgents,
    agentsError,
    refetchAgents,

    // User status mutations
    reactivateUser,
    isReactivatingUser,
    reactivateUserError,

    suspendUser,
    isSuspendingUser,
    suspendUserError,

    // Role mutations
    changeUserRole,
    isChangingUserRole,
    changeUserRoleError,

    // Helper functions
    getRoleLabel,
    isAdminRole,
  };
};
