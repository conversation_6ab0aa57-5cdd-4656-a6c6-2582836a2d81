import { useState, useCallback } from 'react';
import { useErrorContext } from '@/components/error/ErrorProvider';
import { logError } from '@/lib/utils/error-logger';

interface UseErrorHandlingOptions {
  logErrors?: boolean;
  showGlobalModal?: boolean;
}

export function useErrorHandling(options: UseErrorHandlingOptions = {}) {
  const { logErrors = true, showGlobalModal = true } = options;
  const errorContext = useErrorContext();
  const [localError, setLocalError] = useState<Error | null>(null);

  const handleError = useCallback((error: unknown) => {
    // Convert to Error if it's not already
    const errorObj = error instanceof Error 
      ? error 
      : new Error(typeof error === 'string' ? error : 'An unknown error occurred');
    
    // Log the error if enabled
    if (logErrors) {
      logError({
        message: errorObj.message,
        stack: errorObj.stack,
        type: 'other',
        location: typeof window !== 'undefined' ? window.location.href : '',
      });
    }
    
    // Set local error state
    setLocalError(errorObj);
    
    // Show in global modal if enabled
    if (showGlobalModal && errorContext) {
      errorContext.setError(errorObj);
    }
    
    return errorObj;
  }, [logErrors, showGlobalModal, errorContext]);

  const clearError = useCallback(() => {
    setLocalError(null);
    if (errorContext) {
      errorContext.clearError();
    }
  }, [errorContext]);

  return {
    error: localError,
    handleError,
    clearError,
    hasError: !!localError,
  };
}
