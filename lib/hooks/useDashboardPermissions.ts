import { useAuth } from '@/lib/hooks/useAuth';
import { usePermissions } from '@/lib/hooks/usePermissions';

export interface DashboardPermissions {
  // Basic permissions
  canViewOwnMetrics: boolean;
  canViewTeamMetrics: boolean;
  canViewSystemMetrics: boolean;
  canViewAllAgentPerformance: boolean;
  canViewAdvancedAnalytics: boolean;
  canViewReports: boolean;
  
  // Specific dashboard sections
  canViewCallVolumeAnalytics: boolean;
  canViewAgentComparisons: boolean;
  canViewSystemStats: boolean;
  canViewAllTickets: boolean;
  canViewDrillDownAnalytics: boolean;
  canViewTrendsAnalytics: boolean;
  canViewTargetsAnalytics: boolean;
  
  // Data access levels
  dataScope: 'own' | 'team' | 'system';
  maxAgentsToShow: number;
  showSensitiveMetrics: boolean;
}

/**
 * Hook to determine what dashboard content a user can see based on their role
 */
export const useDashboardPermissions = (): DashboardPermissions => {
  const { user } = useAuth();
  const { hasPermission, hasAdminAccess } = usePermissions();

  // Default permissions for unauthenticated users
  if (!user || !user.role) {
    return {
      canViewOwnMetrics: false,
      canViewTeamMetrics: false,
      canViewSystemMetrics: false,
      canViewAllAgentPerformance: false,
      canViewAdvancedAnalytics: false,
      canViewReports: false,
      canViewCallVolumeAnalytics: false,
      canViewAgentComparisons: false,
      canViewSystemStats: false,
      canViewAllTickets: false,
      canViewDrillDownAnalytics: false,
      canViewTrendsAnalytics: false,
      canViewTargetsAnalytics: false,
      dataScope: 'own',
      maxAgentsToShow: 0,
      showSensitiveMetrics: false,
    };
  }

  const roleName = user.role.name.toLowerCase();
  const permissionLevel = user.role.permission_level;

  // Platform Owner - Full access to everything
  if (roleName === 'platform_owner' || permissionLevel === 10) {
    return {
      canViewOwnMetrics: true,
      canViewTeamMetrics: true,
      canViewSystemMetrics: true,
      canViewAllAgentPerformance: true,
      canViewAdvancedAnalytics: true,
      canViewReports: true,
      canViewCallVolumeAnalytics: true,
      canViewAgentComparisons: true,
      canViewSystemStats: true,
      canViewAllTickets: true,
      canViewDrillDownAnalytics: true,
      canViewTrendsAnalytics: true,
      canViewTargetsAnalytics: true,
      dataScope: 'system',
      maxAgentsToShow: 50,
      showSensitiveMetrics: true,
    };
  }

  // Admin - Full access except some platform-specific features
  if (roleName === 'admin' || hasAdminAccess()) {
    return {
      canViewOwnMetrics: true,
      canViewTeamMetrics: true,
      canViewSystemMetrics: true,
      canViewAllAgentPerformance: true,
      canViewAdvancedAnalytics: true,
      canViewReports: true,
      canViewCallVolumeAnalytics: true,
      canViewAgentComparisons: true,
      canViewSystemStats: true,
      canViewAllTickets: true,
      canViewDrillDownAnalytics: true,
      canViewTrendsAnalytics: true,
      canViewTargetsAnalytics: true,
      dataScope: 'system',
      maxAgentsToShow: 30,
      showSensitiveMetrics: true,
    };
  }

  // Supervisor - Team management and oversight
  if (roleName === 'supervisor' || permissionLevel >= 3) {
    return {
      canViewOwnMetrics: true,
      canViewTeamMetrics: true,
      canViewSystemMetrics: true,
      canViewAllAgentPerformance: true,
      canViewAdvancedAnalytics: true,
      canViewReports: hasPermission('report:view'),
      canViewCallVolumeAnalytics: true,
      canViewAgentComparisons: true,
      canViewSystemStats: true,
      canViewAllTickets: true,
      canViewDrillDownAnalytics: true,
      canViewTrendsAnalytics: true,
      canViewTargetsAnalytics: true,
      dataScope: 'team',
      maxAgentsToShow: 20,
      showSensitiveMetrics: true,
    };
  }

  // Agent - Limited to own performance and basic system info
  if (roleName === 'agent' || permissionLevel === 2) {
    return {
      canViewOwnMetrics: true,
      canViewTeamMetrics: false,
      canViewSystemMetrics: false,
      canViewAllAgentPerformance: false,
      canViewAdvancedAnalytics: false,
      canViewReports: false,
      canViewCallVolumeAnalytics: false,
      canViewAgentComparisons: false,
      canViewSystemStats: false,
      canViewAllTickets: false,
      canViewDrillDownAnalytics: false,
      canViewTrendsAnalytics: false,
      canViewTargetsAnalytics: false,
      dataScope: 'own',
      maxAgentsToShow: 1,
      showSensitiveMetrics: false,
    };
  }

  // User/Default - Very limited access
  return {
    canViewOwnMetrics: true,
    canViewTeamMetrics: false,
    canViewSystemMetrics: false,
    canViewAllAgentPerformance: false,
    canViewAdvancedAnalytics: false,
    canViewReports: false,
    canViewCallVolumeAnalytics: false,
    canViewAgentComparisons: false,
    canViewSystemStats: false,
    canViewAllTickets: false,
    canViewDrillDownAnalytics: false,
    canViewTrendsAnalytics: false,
    canViewTargetsAnalytics: false,
    dataScope: 'own',
    maxAgentsToShow: 1,
    showSensitiveMetrics: false,
  };
};

/**
 * Helper function to get appropriate dashboard title based on role
 */
export const getDashboardTitle = (permissions: DashboardPermissions): string => {
  if (permissions.canViewSystemMetrics) {
    return 'System Dashboard';
  } else if (permissions.canViewTeamMetrics) {
    return 'Team Dashboard';
  } else {
    return 'My Dashboard';
  }
};

/**
 * Helper function to get appropriate dashboard description based on role
 */
export const getDashboardDescription = (permissions: DashboardPermissions): string => {
  if (permissions.canViewSystemMetrics) {
    return 'Complete system overview with all metrics and analytics';
  } else if (permissions.canViewTeamMetrics) {
    return 'Team performance overview and management dashboard';
  } else {
    return 'Your personal performance dashboard and assigned tasks';
  }
};

/**
 * Helper function to determine if a specific dashboard component should be shown
 */
export const shouldShowComponent = (
  componentName: string,
  permissions: DashboardPermissions
): boolean => {
  switch (componentName) {
    case 'system-stats':
      return permissions.canViewSystemStats;
    case 'call-volume-analytics':
      return permissions.canViewCallVolumeAnalytics;
    case 'agent-performance':
      return permissions.canViewAllAgentPerformance || permissions.canViewOwnMetrics;
    case 'agent-comparisons':
      return permissions.canViewAgentComparisons;
    case 'advanced-analytics':
      return permissions.canViewAdvancedAnalytics;
    case 'drill-down-analytics':
      return permissions.canViewDrillDownAnalytics;
    case 'trends-analytics':
      return permissions.canViewTrendsAnalytics;
    case 'targets-analytics':
      return permissions.canViewTargetsAnalytics;
    case 'all-tickets':
      return permissions.canViewAllTickets;
    case 'reports':
      return permissions.canViewReports;
    default:
      return true; // Show by default for unknown components
  }
};

/**
 * Helper function to get filtered agent data based on permissions
 */
export const getFilteredAgentData = <T extends { agentId?: string }>(
  data: T[],
  permissions: DashboardPermissions,
  currentUserId?: string
): T[] => {
  if (permissions.dataScope === 'system') {
    return data.slice(0, permissions.maxAgentsToShow);
  } else if (permissions.dataScope === 'team') {
    // In a real implementation, you would filter by team membership
    // For now, we'll limit the number of agents shown
    return data.slice(0, permissions.maxAgentsToShow);
  } else if (permissions.dataScope === 'own' && currentUserId) {
    return data.filter(item => item.agentId === currentUserId);
  }
  
  return [];
};
