import { useState, useCallback } from 'react';

interface ErrorState {
  hasError: boolean;
  error: Error | null;
}

export function useErrorHandler() {
  const [errorState, setErrorState] = useState<ErrorState>({
    hasError: false,
    error: null,
  });

  // Handle an error
  const handleError = useCallback((error: Error) => {
    console.error('Error caught by useErrorHandler:', error);
    
    // You could add error logging to a service here
    // Example: logErrorToService(error);
    
    setErrorState({
      hasError: true,
      error,
    });
  }, []);

  // Clear the error state
  const clearError = useCallback(() => {
    setErrorState({
      hasError: false,
      error: null,
    });
  }, []);

  // Retry the operation that caused the error
  const retryOperation = useCallback((operation: () => void) => {
    clearError();
    try {
      operation();
    } catch (error) {
      handleError(error as Error);
    }
  }, [clearError, handleError]);

  return {
    ...errorState,
    handleError,
    clearError,
    retryOperation,
  };
}
