/**
 * Dashboard enhancement types and interfaces
 */

// Workspace Management Types
export interface WorkspaceContext {
  id: string;
  name: string;
  productId: string;
  productName: string;
  permissions: string[];
  isActive: boolean;
}

export interface WorkspaceUser {
  id: string;
  role: string;
  permissions: string[];
  availableWorkspaces: WorkspaceContext[];
  currentWorkspace?: WorkspaceContext;
}

// Filter System Types
export interface FilterPeriod {
  type: 'daily' | 'weekly' | 'monthly' | 'quarterly' | 'custom';
  startDate?: Date;
  endDate?: Date;
  label: string;
}

export interface AdvancedFilters {
  period: FilterPeriod;
  productIds: string[];
  categoryIds: string[];
  agentIds: string[];
  customFilters: Record<string, any>;
}

// Call Analytics Types
export interface CallVolumeData {
  timestamp: string;
  inbound: number;
  outbound: number;
  missed: number;
  abandoned: number;
  totalDuration: number;
  averageWaitTime: number;
}

export interface CallAnalytics {
  hourly: CallVolumeData[];
  daily: CallVolumeData[];
  weekly: CallVolumeData[];
  monthly: CallVolumeData[];
  comparative: {
    currentPeriod: CallVolumeData[];
    previousPeriod: CallVolumeData[];
    growthRate: number;
  };
}

// Agent Performance Types
export interface TicketResolutionMetrics {
  solvedTickets: number;
  totalTickets: number;
  averageResolutionTime: number; // in minutes
  firstContactResolution: number;
  escalationRate: number;
  customerSatisfaction?: number;
}

export interface AgentPerformanceData {
  agentId: string;
  agentName: string;
  avatar?: string;
  ticketMetrics: TicketResolutionMetrics;
  callMetrics: {
    totalCalls: number;
    averageHandleTime: number;
    callsPerHour: number;
  };
  trends: {
    daily: number[];
    weekly: number[];
    monthly: number[];
  };
  targets: {
    dailyTickets: number;
    weeklyTickets: number;
    monthlyTickets: number;
  };
}

// Attendance Management Types
export interface BreakRecord {
  id: string;
  type: 'tea' | 'lunch' | 'other';
  startTime: Date;
  endTime?: Date;
  duration?: number; // in minutes
  reason?: string;
}

export interface AttendanceRecord {
  id: string;
  agentId: string;
  agentName: string;
  date: string;
  loginTime: Date;
  logoutTime?: Date;
  breaks: BreakRecord[];
  totalWorkTime: number; // in minutes
  totalBreakTime: number; // in minutes
  productivity: number; // percentage
  status: 'active' | 'on-break' | 'offline';
  shift: {
    scheduled: {
      start: Date;
      end: Date;
    };
    actual: {
      start: Date;
      end?: Date;
    };
  };
}

export interface AttendanceMetrics {
  totalAgents: number;
  activeAgents: number;
  onBreakAgents: number;
  offlineAgents: number;
  averageWorkTime: number;
  averageBreakTime: number;
  productivityScore: number;
  attendanceRate: number;
}

// Reports Types
export interface ReportFilter {
  dateRange: {
    start: Date;
    end: Date;
  };
  products: string[];
  categories: string[];
  agents: string[];
  channels: string[];
  customParameters: Record<string, any>;
}

export interface ReportData {
  id: string;
  title: string;
  type: 'call-analytics' | 'product-performance' | 'ticket-analytics' | 'agent-performance';
  data: any;
  generatedAt: Date;
  filters: ReportFilter;
  exportFormats: ('pdf' | 'excel' | 'csv')[];
}

// Draggable Modal Types
export interface DraggableModalPosition {
  x: number;
  y: number;
}

export interface DraggableModalConstraints {
  top: number;
  left: number;
  right: number;
  bottom: number;
}

export interface FAQModalData {
  id: string;
  question: string;
  answer: string;
  category: string;
  productId?: string;
  tags: string[];
  isBookmarked: boolean;
}

// Dashboard Layout Types
export interface DashboardWidget {
  id: string;
  type: 'stats' | 'chart' | 'table' | 'custom';
  title: string;
  size: 'small' | 'medium' | 'large' | 'full';
  position: {
    row: number;
    col: number;
    width: number;
    height: number;
  };
  data: any;
  refreshInterval?: number;
  isVisible: boolean;
}

export interface DashboardLayout {
  id: string;
  name: string;
  widgets: DashboardWidget[];
  isDefault: boolean;
  createdBy: string;
  createdAt: Date;
  updatedAt: Date;
}

// API Response Types
export interface DashboardApiResponse<T> {
  success: boolean;
  data: T;
  message?: string;
  pagination?: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

// All types are already exported above with their interface declarations