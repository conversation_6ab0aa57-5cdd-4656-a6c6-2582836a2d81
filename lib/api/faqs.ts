import { apiClient } from './client';
import {
  ApiResponse,
  FAQ,
  CreateFAQRequest,
  UpdateFAQRequest,
  FlagFAQRequest,
  FAQResponse,
  FAQsResponse,
  PaginatedResponse,
  PaginationParams,
  BatchCreateFAQResponse
} from './types';

/**
 * Service for managing FAQs
 */
export const faqService = {
  /**
   * Get all FAQs
   * @returns Promise with FAQs data
   */
  getAllFAQs: async (): Promise<FAQsResponse> => {
    return apiClient.get<FAQsResponse>('/faqs');
  },

  /**
   * Get paginated FAQs
   * @param params Pagination parameters
   * @returns Promise with paginated FAQs data
   */
  getPaginatedFAQs: async (params: PaginationParams): Promise<PaginatedResponse<FAQ>> => {
    return apiClient.get<PaginatedResponse<FAQ>>('/faqs/paginated', {
      params: params as Record<string, string | number | boolean | undefined>
    });
  },

  /**
   * Get FAQ by ID
   * @param id FAQ ID
   * @returns Promise with FAQ data
   */
  getFAQById: async (id: string): Promise<FAQResponse> => {
    return apiClient.get<FAQResponse>(`/faqs/${id}`);
  },

  /**
   * Create a new FAQ
   * @param faqData FAQ data to create
   * @returns Promise with created FAQ data
   */
  createFAQ: async (faqData: CreateFAQRequest): Promise<FAQResponse> => {
    return apiClient.post<FAQResponse>('/faqs', faqData);
  },

  /**
   * Update an existing FAQ
   * @param id FAQ ID to update
   * @param faqData Updated FAQ data
   * @returns Promise with updated FAQ data
   */
  updateFAQ: async (id: string, faqData: UpdateFAQRequest): Promise<FAQResponse> => {
    return apiClient.put<FAQResponse>(`/faqs/${id}`, faqData);
  },

  /**
   * Delete an FAQ
   * @param id FAQ ID to delete
   * @returns Promise with success status
   */
  deleteFAQ: async (id: string): Promise<ApiResponse<void>> => {
    return apiClient.delete<ApiResponse<void>>(`/faqs/${id}`);
  },

  /**
   * Flag or unflag an FAQ
   * @param id FAQ ID to flag/unflag
   * @param flagData Flag data including reason
   * @returns Promise with success status
   */
  flagFAQ: async (id: string, flagData: FlagFAQRequest): Promise<ApiResponse<void>> => {
    return apiClient.patch<ApiResponse<void>>(`/faqs/${id}/flag`, flagData);
  },

  /**
   * Create multiple FAQs in batch
   * @param faqsData Array of FAQ data to create
   * @returns Promise with batch creation results
   */
  createFAQsBatch: async (faqsData: CreateFAQRequest[]): Promise<BatchCreateFAQResponse> => {
    // Since there's no batch endpoint, we'll create FAQs one by one
    const results = {
      success: 0,
      failed: 0,
      errors: {} as Record<string, string>
    };

    // Process each FAQ sequentially
    for (let i = 0; i < faqsData.length; i++) {
      try {
        await apiClient.post<FAQResponse>('/faqs', faqsData[i]);
        results.success++;
      } catch (error: any) {
        results.failed++;
        results.errors[`row_${i + 2}`] = error.message || 'Unknown error';
      }
    }

    return { success: true, data: results };
  },
};
