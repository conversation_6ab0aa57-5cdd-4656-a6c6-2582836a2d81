/**
 * User Management API service
 */
import { apiClient } from './client';
import {
  ApiResponse,
  CreateUserRequest,
  PaginatedResponse,
  PaginationParams,
  UpdateUserRequest,
  User,
  UsersResponse,
} from './types';

/**
 * User management service for the call center application
 */
export const userService = {
  /**
   * Get all users with pagination and filtering
   */
  getAllUsers: async (params: PaginationParams & {
    search?: string;
    role_id?: string;
    department_id?: string;
    status?: string;
  }): Promise<UsersResponse> => {
    return apiClient.get<UsersResponse>('/unified/users', { params: params as Record<string, string | number | boolean | undefined> });
  },

  /**
   * Get user by ID
   */
  getUserById: async (id: string): Promise<User> => {
    const response = await apiClient.get<ApiResponse<User>>(`/unified/users/${id}`);
    return response.data as User;
  },

  /**
   * Create a new user
   */
  createUser: async (data: CreateUserRequest): Promise<User | ApiResponse<User>> => {
    const response = await apiClient.post<ApiResponse<User>>('/unified/users', data);

    // Check if the response indicates failure
    if (response && typeof response === 'object' && 'success' in response && response.success === false) {
      // Return the full response so the error can be handled
      return response;
    }

    return response.data as User;
  },

  /**
   * Update an existing user
   */
  updateUser: async (id: string, data: UpdateUserRequest): Promise<User | ApiResponse<User>> => {
    const response = await apiClient.put<ApiResponse<User>>(`/unified/users/${id}`, data);

    // Check if the response indicates failure
    if (response && typeof response === 'object' && 'success' in response && response.success === false) {
      // Return the full response so the error can be handled
      return response;
    }

    return response.data as User;
  },

  /**
   * Delete a user
   */
  deleteUser: async (id: string): Promise<void> => {
    await apiClient.delete<ApiResponse<void>>(`/unified/users/${id}`);
  },

  /**
   * Get all agents
   */
  getAllAgents: async (params: PaginationParams & {
    search?: string;
    department_id?: string;
    is_active?: boolean;
  }): Promise<PaginatedResponse<User>> => {
    return apiClient.get<PaginatedResponse<User>>('/unified/agents', { params: params as Record<string, string | number | boolean | undefined> });
  },

  /**
   * Reactivate a user
   */
  reactivateUser: async (id: string): Promise<ApiResponse<null>> => {
    return apiClient.put<ApiResponse<null>>(`/unified/users/reactivate/${id}`, {});
  },

  /**
   * Suspend a user
   */
  suspendUser: async (id: string, reason: string): Promise<ApiResponse<null>> => {
    return apiClient.put<ApiResponse<null>>(`/unified/users/suspend/${id}`, { reason });
  },

  /**
   * Change user role
   */
  changeUserRole: async (id: string, roleId: string): Promise<User> => {
    const response = await apiClient.put<ApiResponse<User>>(`/unified/users/${id}/role`, { role_id: roleId });
    return response.data as User;
  },
};
