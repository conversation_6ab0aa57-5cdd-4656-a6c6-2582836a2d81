import {
  ProductResponse,
  ProductsResponse,
  CreateProductRequest,
  UpdateProductRequest
} from './types';
import { apiClient } from './client';

export async function getProducts(): Promise<ProductsResponse> {
  return apiClient.get<ProductsResponse>('/products');
}

export async function createProduct(data: CreateProductRequest): Promise<ProductResponse> {
  return apiClient.post<ProductResponse>('/products', data);
}


export async function updateProduct(
  id: string,
  data: UpdateProductRequest
): Promise<ProductResponse> {
  return apiClient.put<ProductResponse>(`/products/${id}`, data);
}

export async function deleteProduct(id: string): Promise<void> {
  await apiClient.delete<void>(`/products/${id}`);
}
