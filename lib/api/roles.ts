import { apiClient } from './client';
import { ApiResponse, PaginatedResponse, PaginationParams, Role } from './types';

// Role creation/update payload interface
export interface RolePayload {
  name: string;
  description: string;
  permissions: string[];
  permission_level?: number; // Optional, will be set based on role type
}

/**
 * Service for managing roles
 */
export const roleService = {
  /**
   * Get all roles
   * @returns Promise with roles data
   */
  getAllRoles: async (): Promise<ApiResponse<Role[]>> => {
    console.log('Getting all roles with API client');

    // DEVELOPMENT MOCK - Remove in production
    // This mock simulates a successful API response for testing
    if (process.env.NODE_ENV === 'development') {
      console.log('Using mock implementation for getAllRoles');
      const timestamp = new Date().toISOString();

      return {
        data: [
          {
            id: '1',
            name: 'Agent',
            description: 'Call center agent with basic permissions',
            permission_level: 1,
            is_system_role: true,
            created_at: timestamp,
            updated_at: timestamp,
            createdAt: timestamp,
            updatedAt: timestamp,
            permissions: [
              {
                id: 'perm-1',
                name: 'View Calls',
                code: 'view_calls',
                description: 'Permission to view call history',
                category: 'calls',
                required_level: 1,
                created_at: timestamp,
                updated_at: timestamp,
                createdAt: timestamp,
                updatedAt: timestamp,
                role_permissions: {
                  role_id: '1',
                  permission_id: 'perm-1'
                }
              }
            ]
          },
          {
            id: '2',
            name: 'Supervisor',
            description: 'Team supervisor with management permissions',
            permission_level: 2,
            is_system_role: true,
            created_at: timestamp,
            updated_at: timestamp,
            createdAt: timestamp,
            updatedAt: timestamp,
            permissions: [
              {
                id: 'perm-2',
                name: 'Manage Team',
                code: 'manage_team',
                description: 'Permission to manage team members',
                category: 'management',
                required_level: 2,
                created_at: timestamp,
                updated_at: timestamp,
                createdAt: timestamp,
                updatedAt: timestamp,
                role_permissions: {
                  role_id: '2',
                  permission_id: 'perm-2'
                }
              }
            ]
          },
          {
            id: '3',
            name: 'Admin',
            description: 'System administrator with full permissions',
            permission_level: 3,
            is_system_role: true,
            created_at: timestamp,
            updated_at: timestamp,
            createdAt: timestamp,
            updatedAt: timestamp,
            permissions: [
              {
                id: 'perm-3',
                name: 'System Admin',
                code: 'system_admin',
                description: 'Full system administration permissions',
                category: 'admin',
                required_level: 3,
                created_at: timestamp,
                updated_at: timestamp,
                createdAt: timestamp,
                updatedAt: timestamp,
                role_permissions: {
                  role_id: '3',
                  permission_id: 'perm-3'
                }
              }
            ]
          },
          {
            id: '4',
            name: 'Platform Owner',
            description: 'Platform owner with highest level permissions',
            permission_level: 4,
            is_system_role: true,
            created_at: timestamp,
            updated_at: timestamp,
            createdAt: timestamp,
            updatedAt: timestamp,
            permissions: [
              {
                id: 'perm-4',
                name: 'Platform Owner',
                code: 'platform_owner',
                description: 'Platform owner permissions',
                category: 'platform',
                required_level: 4,
                created_at: timestamp,
                updated_at: timestamp,
                createdAt: timestamp,
                updatedAt: timestamp,
                role_permissions: {
                  role_id: '4',
                  permission_id: 'perm-4'
                }
              }
            ]
          },
          {
            id: '5',
            name: 'User',
            description: 'Regular user with limited permissions',
            permission_level: 0,
            is_system_role: true,
            created_at: timestamp,
            updated_at: timestamp,
            createdAt: timestamp,
            updatedAt: timestamp,
            permissions: [
              {
                id: 'perm-5',
                name: 'Basic Access',
                code: 'basic_access',
                description: 'Basic system access permissions',
                category: 'basic',
                required_level: 0,
                created_at: timestamp,
                updated_at: timestamp,
                createdAt: timestamp,
                updatedAt: timestamp,
                role_permissions: {
                  role_id: '5',
                  permission_id: 'perm-5'
                }
              }
            ]
          }
        ],
        message: 'Roles retrieved successfully',
        success: true
      };
    }

    // Actual API call
    return apiClient.get<ApiResponse<Role[]>>('/roles');
  },

  /**
   * Get role by ID
   * @param id Role ID
   * @returns Promise with role data
   */
  getRoleById: async (id: string): Promise<ApiResponse<Role>> => {
    return apiClient.get<ApiResponse<Role>>(`/roles/${id}`);
  },

  /**
   * Get paginated roles
   * @param params Pagination parameters
   * @returns Promise with paginated roles data
   */
  getPaginatedRoles: async (params: PaginationParams): Promise<PaginatedResponse<Role>> => {
    return apiClient.get<PaginatedResponse<Role>>('/roles/paginated', { 
      params: params as Record<string, string | number | boolean | undefined>
    });
  },

  /**
   * Create a new role
   * @param roleData Role data to create
   * @returns Promise with created role data
   */
  createRole: async (roleData: RolePayload): Promise<ApiResponse<Role>> => {
    console.log('Creating role with API client, payload:', roleData);
  
    // DEVELOPMENT MOCK - Remove in production
    // This mock simulates a successful API response for testing
    if (process.env.NODE_ENV === 'development') {
      console.log('Using mock implementation for createRole');
      const mockRoleId = `role-${Date.now()}`;
      return {
        data: {
          id: mockRoleId,
          name: roleData.name,
          description: roleData.description,
          permission_level: roleData.permission_level || 1,
          is_system_role: false,
          permissions: roleData.permissions.map(p => {
            const [category, action] = p.split(':');
            const timestamp = new Date().toISOString();
            return { 
              id: `perm-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`,
              name: `${category} ${action}`,
              code: action,
              description: `Permission to ${action} ${category}`,
              category: category,
              required_level: 1,
              created_at: timestamp,
              updated_at: timestamp,
              createdAt: timestamp,
              updatedAt: timestamp,
              role_permissions: {
                role_id: mockRoleId,
                permission_id: `perm-${Date.now()}`
              }
            };
          }),
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        },
        message: 'Role created successfully',
        success: true
      };
    }
  
    // Actual API call
    return apiClient.post<ApiResponse<Role>>('/roles', roleData);
  },

  /**
   * Update an existing role
   * @param id Role ID to update
   * @param roleData Updated role data
   * @returns Promise with updated role data
   */
  updateRole: async (id: string, roleData: RolePayload): Promise<ApiResponse<Role>> => {
    console.log(`Updating role ${id} with API client, payload:`, roleData);
  
    // DEVELOPMENT MOCK - Remove in production
    // This mock simulates a successful API response for testing
    if (process.env.NODE_ENV === 'development') {
      console.log('Using mock implementation for updateRole');
      return {
        data: {
          id: id,
          name: roleData.name,
          description: roleData.description,
          permission_level: roleData.permission_level || 1,
          is_system_role: false,
          permissions: roleData.permissions.map(p => {
            const [category, action] = p.split(':');
            const timestamp = new Date().toISOString();
            return { 
              id: `perm-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`,
              name: `${category} ${action}`,
              code: action,
              description: `Permission to ${action} ${category}`,
              category: category,
              required_level: 1,
              created_at: timestamp,
              updated_at: timestamp,
              createdAt: timestamp,
              updatedAt: timestamp,
              role_permissions: {
                role_id: id,
                permission_id: `perm-${Date.now()}`
              }
            };
          }),
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        },
        message: 'Role updated successfully',
        success: true
      };
    }
  
    // Actual API call
    return apiClient.put<ApiResponse<Role>>(`/roles/${id}`, roleData);
  },

  /**
   * Delete a role
   * @param id Role ID to delete
   * @returns Promise with success status
   */
  deleteRole: async (id: string): Promise<ApiResponse<void>> => {
    console.log(`Deleting role ${id} with API client`);
  
    // DEVELOPMENT MOCK - Remove in production
    // This mock simulates a successful API response for testing
    if (process.env.NODE_ENV === 'development') {
      console.log('Using mock implementation for deleteRole');
      return {
        data: undefined,
        message: 'Role deleted successfully',
        success: true
      };
    }
  
    // Actual API call
    return apiClient.delete<ApiResponse<void>>(`/roles/${id}`);
  },
};
