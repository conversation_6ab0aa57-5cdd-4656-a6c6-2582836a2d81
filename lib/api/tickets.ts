/**
 * Tickets API service
 */
import { apiClient } from './client';
import {
  AddTicketCommentRequest,
  ApiResponse,
  AssignTicketRequest,
  CreateTicketRequest,
  EscalateTicketRequest,
  PaginatedResponse,
  PaginationParams,
  Ticket,
  TicketComment,
  UpdateTicketRequest,
} from './types';


/**
 * Ticket management service for the call center application
 */
export const ticketService = {
  /**
   * Get all tickets with pagination and filtering
   */
  getAllTickets: async (params: PaginationParams = {}): Promise<PaginatedResponse<Ticket>> => {
    const response = await apiClient.get('/tickets', {
      params: {
        page: params.page || 1,
        limit: params.limit || 10,
        sortBy: params.sortBy || 'created_at',
        sortOrder: params.sortOrder || 'DESC',
        assigned_to: params.assigned_to,
        ...params
      }
    });

    console.log('Raw tickets API response:', response);

    // Handle different response structures
    if (response && typeof response === 'object') {
      // Use type assertion to avoid TypeScript errors
      const resp = response as any;

      // Standard API response with data property
      if ('data' in resp && Array.isArray(resp.data)) {
        return {
          data: resp.data,
          pagination: resp.pagination || {
            total: resp.data.length,
            page: params.page || 1,
            limit: params.limit || 10,
            total_pages: Math.ceil(resp.data.length / (params.limit || 10))
          }
        };
      }

      // Response with tickets property
      if ('tickets' in resp && Array.isArray(resp.tickets)) {
        return {
          data: resp.tickets,
          pagination: resp.pagination || {
            total: resp.tickets.length,
            page: params.page || 1,
            limit: params.limit || 10,
            total_pages: Math.ceil(resp.tickets.length / (params.limit || 10))
          }
        };
      }

      // Response with nested data structure
      if ('data' in resp && typeof resp.data === 'object' && resp.data) {
        const nestedData = resp.data as any;

        if ('data' in nestedData && Array.isArray(nestedData.data)) {
          return {
            data: nestedData.data,
            pagination: nestedData.pagination || {
              total: nestedData.data.length,
              page: params.page || 1,
              limit: params.limit || 10,
              total_pages: Math.ceil(nestedData.data.length / (params.limit || 10))
            }
          };
        }

        if ('tickets' in nestedData && Array.isArray(nestedData.tickets)) {
          return {
            data: nestedData.tickets,
            pagination: nestedData.pagination || {
              total: nestedData.tickets.length,
              page: params.page || 1,
              limit: params.limit || 10,
              total_pages: Math.ceil(nestedData.tickets.length / (params.limit || 10))
            }
          };
        }
      }
    }

    // If no recognized structure, return empty data
    console.error('Unrecognized API response structure:', response);
    return {
      data: [],
      pagination: {
        total: 0,
        page: params.page || 1,
        limit: params.limit || 10,
        total_pages: 0
      }
    };
  },

  /**
   * Get ticket by ID
   */
  getTicketById: async (id: string): Promise<Ticket> => {
    const response = await apiClient.get<ApiResponse<Ticket>>(`/tickets/${id}`);
    return response.data as Ticket;
  },

  /**
   * Create a new ticket
   */
  createTicket: async (data: CreateTicketRequest): Promise<Ticket> => {
    console.log('Ticket service: Creating ticket with data:', data);
    try {
      const response = await apiClient.post<ApiResponse<Ticket>>('/tickets', data);
      console.log('Ticket service: API response:', response);

      // Handle different response structures
      if (response && typeof response === 'object') {
        // Use type assertion to avoid TypeScript errors
        const resp = response as any;

        // Standard API response with data property
        if ('data' in resp && resp.data) {
          console.log('Ticket service: Returning data property:', resp.data);
          return resp.data as Ticket;
        }

        // Response with ticket property
        if ('ticket' in resp) {
          console.log('Ticket service: Returning ticket property:', resp.ticket);
          return resp.ticket as Ticket;
        }

        // Response is the ticket itself
        if ('id' in resp) {
          console.log('Ticket service: Response is the ticket itself:', resp);
          return resp as Ticket;
        }
      }

      console.log('Ticket service: Returning full response as ticket:', response);
      return response as unknown as Ticket;
    } catch (error) {
      console.error('Ticket service: Error creating ticket:', error);
      throw error;
    }
  },

  /**
   * Update an existing ticket
   */
  updateTicket: async (id: string, data: UpdateTicketRequest): Promise<Ticket> => {
    const response = await apiClient.put<ApiResponse<Ticket>>(`/tickets/${id}`, data);
    return response.data as Ticket;
  },

  /**
   * Delete a ticket
   */
  deleteTicket: async (id: string): Promise<void> => {
    await apiClient.delete<ApiResponse<void>>(`/tickets/${id}`);
  },

  /**
   * Assign a ticket to an agent
   */
  assignTicket: async (id: string, data: AssignTicketRequest): Promise<Ticket> => {
    const response = await apiClient.put<ApiResponse<Ticket>>(`/tickets/${id}/assign`, data);
    return response.data as Ticket;
  },

  /**
   * Change ticket status
   */
  changeTicketStatus: async (id: string, status: string): Promise<Ticket> => {
    const response = await apiClient.put<ApiResponse<Ticket>>(`/tickets/${id}/status`, { status });
    return response.data as Ticket;
  },

  /**
   * Change ticket priority
   */
  changeTicketPriority: async (id: string, priority: string): Promise<Ticket> => {
    const response = await apiClient.put<ApiResponse<Ticket>>(`/tickets/${id}/priority`, { priority });
    return response.data as Ticket;
  },

  /**
   * Get all comments for a ticket
   */
  getTicketComments: async (ticketId: string, params?: Record<string, string | number | boolean | undefined>): Promise<PaginatedResponse<TicketComment>> => {
    return apiClient.get<PaginatedResponse<TicketComment>>(`/tickets/${ticketId}/comments`, { params });
  },

  /**
   * Add a comment to a ticket
   */
  addTicketComment: async (ticketId: string, data: AddTicketCommentRequest): Promise<TicketComment> => {
    const response = await apiClient.post<ApiResponse<TicketComment>>(`/tickets/${ticketId}/comments`, data);
    return response.data as TicketComment;
  },

  /**
   * Delete a ticket comment
   */
  deleteTicketComment: async (ticketId: string, commentId: string): Promise<void> => {
    await apiClient.delete<ApiResponse<void>>(`/tickets/${ticketId}/comments/${commentId}`);
  },

  /**
   * Escalate a ticket
   */
  escalateTicket: async (id: string, data: EscalateTicketRequest): Promise<Ticket> => {
    const response = await apiClient.put(`/tickets/${id}/escalate`, data);
    console.log('Escalate ticket response:', response);

    // Handle different response structures
    if (response && typeof response === 'object') {
      // Use type assertion to avoid TypeScript errors
      const resp = response as any;

      // Standard API response with data property
      if ('data' in resp && resp.data) {
        return resp.data as Ticket;
      }

      // Response with ticket property
      if ('ticket' in resp) {
        return resp.ticket as Ticket;
      }
    }

    // If no recognized structure, return the response itself
    return response as unknown as Ticket;
  },
};
