import { apiClient } from './client';
import {
  Agent,
  AgentsResponse,
  ApiResponse,
  PaginatedResponse,
  PaginationParams,
} from './types';

/**
 * Agent management service for the call center application
 */
export const agentService = {
  /**
   * Get all agents with pagination and filtering
   */
  getAllAgents: async (params?: PaginationParams & {
    search?: string;
    department_id?: string;
    status?: string;
    availability?: string;
  }): Promise<AgentsResponse> => {
    return apiClient.get<AgentsResponse>('/unified/agents', { 
      params: params as Record<string, string | number | boolean | undefined> 
    });
  },

  /**
   * Get agent by ID
   */
  getAgentById: async (id: string): Promise<Agent> => {
    const response = await apiClient.get<ApiResponse<Agent>>(`/unified/agents/${id}`);
    return response.data as Agent;
  },

  /**
   * Update agent status
   */
  updateAgentStatus: async (id: string, status: string): Promise<ApiResponse<Agent>> => {
    return apiClient.put<ApiResponse<Agent>>(`/unified/agents/${id}/status`, { status });
  },

  /**
   * Update agent skills
   */
  updateAgentSkills: async (id: string, skills: string[]): Promise<ApiResponse<Agent>> => {
    return apiClient.put<ApiResponse<Agent>>(`/unified/agents/${id}/skills`, { skills });
  },

  /**
   * Update agent availability
   */
  updateAgentAvailability: async (id: string, availability: string): Promise<ApiResponse<Agent>> => {
    return apiClient.put<ApiResponse<Agent>>(`/unified/agents/${id}/availability`, { availability });
  },
};
