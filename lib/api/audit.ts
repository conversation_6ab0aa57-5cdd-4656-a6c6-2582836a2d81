/**
 * <PERSON>t logs API service
 */
import { apiClient } from './client';
import {
  ApiResponse,
  AuditLog,
  PaginatedResponse,
  PaginationParams,
} from './types';
import {
  AUDIT_ACTIONS,
  AUDIT_RESOURCE_TYPES,
  AUDIT_STATUS,
  type AuditAction,
  type AuditResourceType,
  type AuditStatus
} from '@/lib/constants/audit-actions';

/**
 * Audit logs service for the call center application
 */
export const auditService = {
  /**
   * Create an audit log entry
   * @param action The action being performed
   * @param resourceType The type of resource being acted upon
   * @param resourceId The ID of the resource being acted upon
   * @param details Additional details about the action
   * @param status The status of the action (default: SUCCESS)
   * @returns Promise with the created audit log
   */
  createAuditLog: async (
    action: AuditAction | string,
    resourceType: AuditResourceType | string,
    resourceId: string,
    details: Record<string, any> = {},
    status: AuditStatus | string = AUDIT_STATUS.SUCCESS
  ): Promise<ApiResponse<AuditLog>> => {
    return apiClient.post('/audit/logs', {
      action,
      resource_type: resourceType,
      resource_id: resourceId,
      details,
      status
    });
  },

  /**
   * Get all audit logs with pagination and filtering
   */
  getAuditLogs: async (params: PaginationParams & {
    user_id?: string;
    action?: string;
    resource_type?: string;
    status?: string;
    start_date?: string;
    end_date?: string;
  } = {}): Promise<PaginatedResponse<AuditLog>> => {
    const response = await apiClient.get('/audit/logs', {
      params: {
        page: params.page || 1,
        limit: params.limit || 10,
        sortBy: params.sortBy || 'created_at',
        sortOrder: params.sortOrder || 'DESC',
        ...params
      }
    });

    console.log('Raw audit logs API response:', response);

    // Handle different response structures
    if (response && typeof response === 'object') {
      // Use type assertion to avoid TypeScript errors
      const resp = response as any;

      // Standard API response with data property
      if ('data' in resp && Array.isArray(resp.data)) {
        return {
          data: resp.data,
          pagination: resp.pagination || {
            total: resp.data.length,
            page: params.page || 1,
            limit: params.limit || 10,
            total_pages: Math.ceil(resp.data.length / (params.limit || 10))
          }
        };
      }

      // Response with logs property
      if ('logs' in resp && Array.isArray(resp.logs)) {
        return {
          data: resp.logs,
          pagination: resp.pagination || {
            total: resp.logs.length,
            page: params.page || 1,
            limit: params.limit || 10,
            total_pages: Math.ceil(resp.logs.length / (params.limit || 10))
          }
        };
      }

      // Response with nested data structure
      if ('data' in resp && typeof resp.data === 'object' && resp.data) {
        const nestedData = resp.data as any;

        if ('data' in nestedData && Array.isArray(nestedData.data)) {
          return {
            data: nestedData.data,
            pagination: nestedData.pagination || {
              total: nestedData.data.length,
              page: params.page || 1,
              limit: params.limit || 10,
              total_pages: Math.ceil(nestedData.data.length / (params.limit || 10))
            }
          };
        }

        if ('logs' in nestedData && Array.isArray(nestedData.logs)) {
          return {
            data: nestedData.logs,
            pagination: nestedData.pagination || {
              total: nestedData.logs.length,
              page: params.page || 1,
              limit: params.limit || 10,
              total_pages: Math.ceil(nestedData.logs.length / (params.limit || 10))
            }
          };
        }
      }
    }

    // Fallback to empty response
    console.error('Unexpected audit logs API response format:', response);
    return {
      data: [],
      pagination: {
        total: 0,
        page: params.page || 1,
        limit: params.limit || 10,
        total_pages: 0
      }
    };
  },
};
