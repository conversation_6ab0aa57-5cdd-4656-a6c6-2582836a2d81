/**
 * Authentication API service
 */
import { apiClient } from './client';
import { AuthResponse, LoginRequest, Role, UpdatePasswordRequest, User, UserRole } from './types';

// Cache for roles to avoid repeated API calls
let rolesCache: Record<string, Role> = {};

// Interface for account activation request
interface ActivateAccountRequest {
  currentPassword: string;
  newPassword: string;
  temporaryToken: string;
}

/**
 * Get role by ID from API or cache
 */
async function getRoleById(roleId: string): Promise<Role | null> {
  try {
    // Check cache first
    if (rolesCache[roleId]) {
      console.log('Using cached role data for ID:', roleId);
      return rolesCache[roleId];
    }

    console.log('Fetching role data for ID:', roleId);

    // Get the access token
    const accessToken = localStorage.getItem('accessToken');
    if (!accessToken) {
      console.error('No access token found');
      return null;
    }

    // If not in cache, fetch from API with authorization header
    const response = await apiClient.get<{ success: boolean, data: Role }>(
      `/roles/${roleId}`,
      {
        headers: {
          'Authorization': `Bearer ${accessToken}`
        }
      }
    );

    if (response.success && response.data) {
      console.log('Successfully fetched role:', response.data.name);
      // Cache the result for future use
      rolesCache[roleId] = response.data;
      return response.data;
    }

    console.error('Role data not found in response');
    return null;
  } catch (error) {
    console.error('Error fetching role:', error);
    return null;
  }
}

/**
 * Authentication service for the call center application
 */
export const authService = {
  /**
   * Login with email and password
   */
  login: async (data: LoginRequest): Promise<AuthResponse> => {
    try {
      const response = await apiClient.post<AuthResponse>('/auth/login', data);

      // If login successful, store tokens in both localStorage and cookies
      if (response.success && response.tokens) {
        // Store in localStorage
        localStorage.setItem('accessToken', response.tokens.accessToken);
        localStorage.setItem('refreshToken', response.tokens.refreshToken);

        // Store in cookies with proper attributes
        document.cookie = `token=${response.tokens.accessToken}; path=/; secure; samesite=strict`;
      }

      return response;
    } catch (error: any) {
      // Extract error message from the response if available
      let errorMessage = 'Login failed. Please check your credentials.';

      if (error.response?.data?.message) {
        errorMessage = error.response.data.message;
      } else if (error.message) {
        errorMessage = error.message;
      }

      // Return a structured error response
      return {
        success: false,
        message: errorMessage
      };
    }
  },

  /**
   * Get current user info
   */
  getCurrentUser: async (): Promise<User> => {
    console.log('Fetching current user data');
    const response = await apiClient.get<{ success: boolean, data: any }>('/auth/me');

    console.log('Auth/me response:', response);

    if (!response.success || !response.data) {
      console.error('No user data returned from /auth/me');
      throw new Error('No user data returned from API');
    }

    const userData = response.data;

    // Create user object using the role data directly from /auth/me response
    const formattedUser: User = {
      id: userData.id,
      email: userData.email,
      first_name: userData.first_name,
      last_name: userData.last_name,
      status: userData.status,
      // Set role_id from the role object if available
      role_id: userData.role?.id || userData.role_id || '',
      phone_number: userData.phone_number || '',
      created_at: userData.createdAt || userData.created_at,
      updated_at: userData.updatedAt || userData.updated_at,
      firstName: userData.first_name,
      lastName: userData.last_name,
      username: userData.username || userData.email.split('@')[0],
      // Use the role data that comes with the /auth/me response
      role: userData.role || null
    };

    console.log('Formatted user with role:', {
      id: formattedUser.id,
      email: formattedUser.email,
      roleId: formattedUser.role_id,
      roleName: formattedUser.role?.name,
      permissionLevel: formattedUser.role?.permission_level,
      permissionCount: formattedUser.role?.permissions?.length,
      permissionCodes: formattedUser.role?.permissions?.map(p => p.code)
    });

    return formattedUser;
  },

  /**
   * Logout current user
   */
  logout: async (): Promise<void> => {
    try {
      await apiClient.post('/auth/logout');
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      // Clear tokens from localStorage
      localStorage.removeItem('accessToken');
      localStorage.removeItem('refreshToken');

      // Clear token from cookies
      document.cookie = 'token=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT';
    }
  },

  /**
   * Request password reset via phone number
   */
  requestPasswordReset: async (phone_number: string): Promise<{ success: boolean, message: string }> => {
    return apiClient.post('/auth/forgot-password', { phone_number });
  },

  /**
   * Reset password using token (OTP) and email
   */
  resetPassword: async (token: string, password: string, email: string): Promise<{ success: boolean, message: string }> => {
    return apiClient.post('/auth/reset-password', {
      email,
      token,
      new_password: password,
    });
  },

  /**
   * Update current user's password
   */
  updatePassword: async (data: UpdatePasswordRequest): Promise<{ success: boolean, message: string }> => {
    return apiClient.put('/auth/update-password', data);
  },

  /**
   * Refresh access token using refresh token
   */
  refreshToken: async (refreshToken: string): Promise<AuthResponse> => {
    const response = await apiClient.post<AuthResponse>('/auth/refresh', { refreshToken });

    // Update tokens in localStorage
    if (response.success && response.tokens) {
      localStorage.setItem('accessToken', response.tokens.accessToken);
      localStorage.setItem('refreshToken', response.tokens.refreshToken);
    }

    return response;
  },

  /**
   * Check if user is authenticated
   */
  isAuthenticated: (): boolean => {
    if (typeof window === 'undefined') return false;
    return !!localStorage.getItem('accessToken');
  },

  /**
   * Activate account by changing password
   * This is used for new users with INACTIVE status and force_password_change flag
   */
  activateAccount: async (data: ActivateAccountRequest): Promise<{ success: boolean, message: string }> => {
    try {
      // Get the temporary token from localStorage
      const temporaryToken = localStorage.getItem('temporaryToken');

      if (!temporaryToken) {
        throw new Error('No temporary token found. Please try logging in again.');
      }

      // Include the temporary token in the request body and convert to snake_case
      const requestData = {
        currentPassword: data.currentPassword,
        newPassword: data.newPassword,
        temporaryToken: temporaryToken,
      };

      // Make the API call with the temporary token in the body
      const response = await apiClient.post<{ success: boolean, message: string }>('/auth/change-password', requestData);

      // Clear the temporary token after successful activation
      if (response.success) {
        localStorage.removeItem('temporaryToken');
      }

      return response;
    } catch (error: any) {
      return {
        success: false,
        message: error.message || 'Account activation failed. Please try again.'
      };
    }
  },
};
