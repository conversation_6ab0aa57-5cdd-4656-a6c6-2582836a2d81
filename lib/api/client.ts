/**
 * API client for the call center application
 */
import { logError } from '@/lib/utils/error-logger'

type FetchOptions = RequestInit & {
  params?: Record<string, string | number | boolean | undefined>;
  token?: string;
};

class ApiClient {
  private baseUrl: string;

  constructor() {
    this.baseUrl = process.env.NEXT_PUBLIC_API_URL || 'http://197.248.11.234:8000/api';
  }

  /**
   * Get Access Token from local storage or cookies
   */
  private getToken(): string | null {
    if (typeof window === 'undefined') return null;

    // First check localStorage
    const token = localStorage.getItem('accessToken');
    if (token) {
      // Also ensure it's in cookies for server-side middleware
      this.setCookie('token', token);
      return token;
    }

    // If not in localStorage, try to get from cookies
    return this.getCookie('token');
  }

  /**
   * Set a cookie
   */
  private setCookie(name: string, value: string, days = 7): void {
    if (typeof window === 'undefined') return;

    const expires = new Date();
    expires.setTime(expires.getTime() + days * 24 * 60 * 60 * 1000);
    document.cookie = `${name}=${value};expires=${expires.toUTCString()};path=/;SameSite=Strict`;
  }

  /**
   * Get a cookie value
   */
  private getCookie(name: string): string | null {
    if (typeof window === 'undefined') return null;

    const nameEQ = name + '=';
    const ca = document.cookie.split(';');

    for (let i = 0; i < ca.length; i++) {
      let c = ca[i];
      while (c.charAt(0) === ' ') c = c.substring(1, c.length);
      if (c.indexOf(nameEQ) === 0) return c.substring(nameEQ.length, c.length);
    }

    return null;
  }

  /**
   * Handle API response
   */
  private async handleResponse<T>(response: Response): Promise<T> {
    const contentType = response.headers.get('content-type');
    console.log(`API Client: Handling response with content-type: ${contentType}`);

    if (contentType && contentType.includes('application/json')) {
      try {
        const data = await response.json();
        console.log('API Client: Parsed JSON response:', data);

        if (!response.ok) {
          console.warn(`API Client: Response not OK (${response.status}):`, data);

          // Handle token expiration
          if (response.status === 401) {
            console.log('API Client: Handling 401 Unauthorized - attempting token refresh');
            // Try to refresh token if available
            const refreshed = await this.refreshToken();
            if (refreshed) {
              console.log('API Client: Token refreshed successfully, retrying request');
              // Retry the original request with the new token
              const retryResponse = await fetch(response.url, {
                ...response,
                headers: {
                  ...response.headers,
                  Authorization: `Bearer ${this.getToken()}`,
                },
              });
              return this.handleResponse(retryResponse);
            } else {
              console.error('API Client: Token refresh failed, redirecting to login');
              // Clear tokens and redirect to login
              this.clearTokens();
              window.location.href = '/auth/login';
              throw new Error('Authentication failed. Please log in again.');
            }
          }

          // Handle permission errors (403 Forbidden)
          if (response.status === 403) {
            const errorMessage = data.message || 'You do not have the required permissions to perform this action';
            console.error(`API Client: Permission error (403): ${errorMessage}`);
            const error = new Error(errorMessage);
            error.name = 'ForbiddenError';

            // Log the permission error
            logError({
              message: errorMessage,
              type: 'permission',
              location: response.url,
              statusCode: 403,
              metadata: { path: response.url }
            });

            throw error;
          }

          // Log other API errors
          const errorMessage = data.message || 'An error occurred';
          console.error(`API Client: API error (${response.status}): ${errorMessage}`);
          logError({
            message: errorMessage,
            type: 'api',
            location: response.url,
            statusCode: response.status,
            metadata: {
              path: response.url,
              status: response.status,
              statusText: response.statusText
            }
          });

          throw new Error(errorMessage);
        }

        console.log('API Client: Returning successful response data');
        return data as T;
      } catch (error) {
        console.error('API Client: Error parsing JSON response:', error);
        throw error;
      }
    }

    if (!response.ok) {
      // Handle specific HTTP status codes
      if (response.status === 403) {
        const errorMessage = 'You do not have the required permissions to perform this action';
        const error = new Error(errorMessage);
        error.name = 'ForbiddenError';

        // Log the permission error
        logError({
          message: errorMessage,
          type: 'permission',
          location: response.url,
          statusCode: 403,
          metadata: { path: response.url }
        });

        throw error;
      }

      // Log the error
      const errorMessage = `Request failed with status ${response.status}`;
      logError({
        message: errorMessage,
        type: 'api',
        location: response.url,
        statusCode: response.status,
        metadata: {
          path: response.url,
          status: response.status,
          statusText: response.statusText
        }
      });

      throw new Error(errorMessage);
    }

    return {} as T;
  }

  /**
   * Clear tokens from storage
   */
  private clearTokens(): void {
    if (typeof window === 'undefined') return;

    // Clear from localStorage
    localStorage.removeItem('accessToken');
    localStorage.removeItem('refreshToken');

    // Clear from cookies
    document.cookie = 'token=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/;';
    document.cookie = 'accessToken=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/;';
    document.cookie = 'refreshToken=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/;';

    console.log('All tokens cleared from storage');
  }

  /**
   * Refresh access token using refresh token
   */
  private async refreshToken(): Promise<boolean> {
    if (typeof window === 'undefined') return false;

    const refreshToken = localStorage.getItem('refreshToken');
    if (!refreshToken) return false;

    try {
      const response = await fetch(`${this.baseUrl}/auth/refresh`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ refreshToken }),
      });

      if (!response.ok) {
        this.clearTokens();
        return false;
      }

      const data = await response.json();

      // Store in localStorage
      localStorage.setItem('accessToken', data.tokens.accessToken);
      localStorage.setItem('refreshToken', data.tokens.refreshToken);

      // Store in cookies for server-side middleware
      this.setCookie('token', data.tokens.accessToken);
      this.setCookie('refreshToken', data.tokens.refreshToken);

      console.log('Tokens refreshed and stored in both localStorage and cookies');
      return true;
    } catch (error) {
      this.clearTokens();
      return false;
    }
  }

  /**
   * Build URL with query parameters
   */
  private buildUrl(path: string, params?: Record<string, string | number | boolean | undefined>): string {
    const url = new URL(`${this.baseUrl}${path}`);

    if (params) {
      console.log('Building URL with params:', JSON.stringify(params, null, 2));

      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined) {
          // Handle special parameters
          if (key === 'escalated_at_not_null') {
            // Filter for tickets where escalated_at IS NOT NULL
            url.searchParams.append('escalated_at_filter', 'not_null');
            console.log(`Added special parameter: escalated_at_filter=not_null`);
          } else if (key === 'escalated_at_null') {
            // Filter for tickets where escalated_at IS NULL
            url.searchParams.append('escalated_at_filter', 'null');
            console.log(`Added special parameter: escalated_at_filter=null`);
          } else if (key === 'escalation_level_gt') {
            // Convert to a filter parameter that the API understands
            url.searchParams.append('escalation_level_filter', 'gt');
            url.searchParams.append('escalation_level', String(value));
            console.log(`Added special parameters: escalation_level_filter=gt, escalation_level=${value}`);
          } else {
            url.searchParams.append(key, String(value));
            console.log(`Added parameter: ${key}=${value}`);
          }
        }
      });
    }

    const finalUrl = url.toString();
    console.log('Final URL:', finalUrl);
    return finalUrl;
  }

  /**
   * HTTP GET request
   */
  public async get<T>(path: string, options: FetchOptions = {}): Promise<T> {
    const { params, token, ...fetchOptions } = options;
    const url = this.buildUrl(path, params);

    const authToken = token || this.getToken();
    const headers = new Headers(fetchOptions.headers);

    if (authToken) {
      headers.set('Authorization', `Bearer ${authToken}`);
    }

    const response = await fetch(url, {
      ...fetchOptions,
      method: 'GET',
      headers,
    });

    return this.handleResponse<T>(response);
  }

  /**
   * HTTP POST request
   */
  public async post<T>(path: string, body?: any, options: FetchOptions = {}): Promise<T> {
    const { params, token, ...fetchOptions } = options;
    const url = this.buildUrl(path, params);

    console.log(`API Client: POST request to ${url}`);

    const authToken = token || this.getToken();
    const headers = new Headers(fetchOptions.headers);

    if (authToken) {
      headers.set('Authorization', `Bearer ${authToken}`);
      console.log('API Client: Using auth token:', authToken.substring(0, 10) + '...');
    } else {
      console.warn('API Client: No auth token available for request');
    }

    if (body && !(body instanceof FormData)) {
      headers.set('Content-Type', 'application/json');
      console.log('API Client: Request body:', body);
    }

    try {
      console.log('API Client: Sending POST request with headers:', Object.fromEntries(headers.entries()));

      const response = await fetch(url, {
        ...fetchOptions,
        method: 'POST',
        headers,
        body: body instanceof FormData ? body : JSON.stringify(body),
      });

      console.log(`API Client: Received response with status ${response.status}`);

      return this.handleResponse<T>(response);
    } catch (error) {
      console.error('API Client: Error during POST request:', error);
      throw error;
    }
  }

  /**
   * HTTP PUT request
   */
  public async put<T>(path: string, body?: any, options: FetchOptions = {}): Promise<T> {
    const { params, token, ...fetchOptions } = options;
    const url = this.buildUrl(path, params);

    const authToken = token || this.getToken();
    const headers = new Headers(fetchOptions.headers);

    if (authToken) {
      headers.set('Authorization', `Bearer ${authToken}`);
    }

    if (body && !(body instanceof FormData)) {
      headers.set('Content-Type', 'application/json');
    }

    const response = await fetch(url, {
      ...fetchOptions,
      method: 'PUT',
      headers,
      body: body instanceof FormData ? body : JSON.stringify(body),
    });

    return this.handleResponse<T>(response);
  }

  /**
   * HTTP PATCH request
   */
  public async patch<T>(path: string, body?: any, options: FetchOptions = {}): Promise<T> {
    const { params, token, ...fetchOptions } = options;
    const url = this.buildUrl(path, params);

    const authToken = token || this.getToken();
    const headers = new Headers(fetchOptions.headers);

    if (authToken) {
      headers.set('Authorization', `Bearer ${authToken}`);
    }

    if (body && !(body instanceof FormData)) {
      headers.set('Content-Type', 'application/json');
    }

    const response = await fetch(url, {
      ...fetchOptions,
      method: 'PATCH',
      headers,
      body: body instanceof FormData ? body : JSON.stringify(body),
    });

    return this.handleResponse<T>(response);
  }

  /**
   * HTTP DELETE request
   */
  public async delete<T>(path: string, options: FetchOptions = {}): Promise<T> {
    const { params, token, ...fetchOptions } = options;
    const url = this.buildUrl(path, params);

    const authToken = token || this.getToken();
    const headers = new Headers(fetchOptions.headers);

    if (authToken) {
      headers.set('Authorization', `Bearer ${authToken}`);
    }

    const response = await fetch(url, {
      ...fetchOptions,
      method: 'DELETE',
      headers,
    });

    return this.handleResponse<T>(response);
  }
}

// Create a singleton instance
export const apiClient = new ApiClient();
