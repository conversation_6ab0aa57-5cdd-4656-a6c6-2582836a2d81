/**
 * Contacts API service
 */
import { apiClient } from './client';
import {
  ApiResponse,
  Contact,
  CreateContactRequest,
  PaginatedResponse,
  PaginationParams,
  UpdateContactRequest,
} from './types';

// Analytics types
export interface ContactAnalyticsParams {
  startDate?: string;
  endDate?: string;
  productId?: string;
  category?: string;
  agentId?: string;
}

export interface ContactMetrics {
  totalContacts: number;
  newContacts: number;
  growthRate: number;
  previousPeriodTotal: number;
}

export interface ContactTrendData {
  date: string;
  count: number;
  category?: string;
  product?: string;
}

export interface ContactAnalyticsResponse {
  metrics: ContactMetrics;
  trends: ContactTrendData[];
  productBreakdown: Array<{
    productId: string;
    productName: string;
    count: number;
    percentage: number;
  }>;
  categoryBreakdown: Array<{
    category: string;
    count: number;
    percentage: number;
  }>;
}

/**
 * Contact management service for the call center application
 */
export const contactService = {
  /**
   * Get all contacts with pagination and filtering
   */
  getAllContacts: async (params: PaginationParams & {
    search?: string;
    status?: string;
    category?: string;
    sortBy?: string;
    sortOrder?: 'ASC' | 'DESC';
  }) => {
    // Use a more generic type to handle different response structures
    const response = await apiClient.get('/contacts', { params });
    console.log('Raw contacts API response:', response);
    return response;
  },

  /**
   * Get contact analytics data
   */
  getContactAnalytics: async (params: ContactAnalyticsParams): Promise<ContactAnalyticsResponse> => {
    const response = await apiClient.get('/contacts/analytics', { params });
    return response.data || response;
  },

  /**
   * Get contact by ID
   */
  getContactById: async (id: string): Promise<Contact> => {
    const response = await apiClient.get(`/contacts/${id}`);
    console.log('Get contact by ID response:', response);

    // Handle different response structures
    if (response && typeof response === 'object') {
      if ('contact' in response) {
        return response.contact as Contact;
      } else if ('data' in response) {
        return response.data as Contact;
      }
    }

    throw new Error('Invalid response format');
  },

  /**
   * Create a new contact
   */
  createContact: async (data: CreateContactRequest): Promise<Contact> => {
    const response = await apiClient.post('/contacts', data);
    console.log('Create contact response:', response);

    // Handle different response structures
    if (response && typeof response === 'object') {
      if ('contact' in response) {
        return response.contact as Contact;
      } else if ('data' in response) {
        return response.data as Contact;
      }
    }

    throw new Error('Invalid response format');
  },

  /**
   * Update an existing contact
   */
  updateContact: async (id: string, data: UpdateContactRequest): Promise<Contact> => {
    const response = await apiClient.put(`/contacts/${id}`, data);
    console.log('Update contact response:', response);

    // Handle different response structures
    if (response && typeof response === 'object') {
      if ('contact' in response) {
        return response.contact as Contact;
      } else if ('data' in response) {
        return response.data as Contact;
      }
    }

    throw new Error('Invalid response format');
  },

  /**
   * Delete a contact
   */
  deleteContact: async (id: string): Promise<void> => {
    await apiClient.delete<ApiResponse<void>>(`/contacts/${id}`);
  },

  /**
   * Search contacts
   */
  searchContacts: async (query: string): Promise<Contact[]> => {
    const response = await apiClient.get<ApiResponse<Contact[]>>('/contacts/search-contacts', {
      params: { query },
    });
    return response.data as Contact[];
  },
};
