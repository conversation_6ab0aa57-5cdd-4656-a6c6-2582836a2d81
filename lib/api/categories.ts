import {
  CategoryResponse,
  CategoriesResponse,
  CreateCategoryRequest,
  UpdateCategoryRequest
} from './types';
import { apiClient } from './client';

export async function getCategories(): Promise<CategoriesResponse> {
  return apiClient.get<CategoriesResponse>('/tickets/categories');
}

export async function getSubcategories(parentId: string): Promise<CategoriesResponse> {
  return apiClient.get<CategoriesResponse>(`/tickets/categories/${parentId}/subcategories`);
}

export async function createCategory(data: CreateCategoryRequest): Promise<CategoryResponse> {
  return apiClient.post<CategoryResponse>('/tickets/categories', data);
}

export async function createSubcategory(
  parentId: string,
  data: CreateCategoryRequest
): Promise<CategoryResponse> {
  return apiClient.post<CategoryResponse>(`/tickets/categories/${parentId}/subcategories`, data);
}

export async function updateCategory(
  id: string,
  data: UpdateCategoryRequest
): Promise<CategoryResponse> {
  return apiClient.put<CategoryResponse>(`/tickets/categories/${id}`, data);
}

export async function deleteCategory(id: string): Promise<void> {
  await apiClient.delete<void>(`/tickets/categories/${id}`);
}
