import { apiClient } from './client';
import { 
  ApiResponse,
  Escalation,
  EscalationComment,
  CreateEscalationRequest,
  CreateEscalationCommentRequest,
  ResolveEscalationRequest,
  EscalationResponse,
  EscalationsResponse,
  EscalationCommentResponse,
  EscalationCommentsResponse,
  PaginatedResponse,
  PaginationParams
} from './types';

/**
 * Service for managing escalations
 */
export const escalationService = {
  /**
   * Get all escalations
   * @returns Promise with escalations data
   */
  getAllEscalations: async (): Promise<EscalationsResponse> => {
    return apiClient.get<EscalationsResponse>('/escalations');
  },

  /**
   * Get paginated escalations
   * @param params Pagination parameters
   * @returns Promise with paginated escalations data
   */
  getPaginatedEscalations: async (params: PaginationParams): Promise<PaginatedResponse<Escalation>> => {
    return apiClient.get<PaginatedResponse<Escalation>>('/escalations/paginated', { 
      params: params as Record<string, string | number | boolean | undefined>
    });
  },

  /**
   * Get escalation by ID
   * @param id Escalation ID
   * @returns Promise with escalation data
   */
  getEscalationById: async (id: string): Promise<EscalationResponse> => {
    return apiClient.get<EscalationResponse>(`/escalations/${id}`);
  },

  /**
   * Create a new escalation
   * @param escalationData Escalation data to create
   * @returns Promise with created escalation data
   */
  createEscalation: async (escalationData: CreateEscalationRequest): Promise<EscalationResponse> => {
    return apiClient.post<EscalationResponse>('/escalations', escalationData);
  },

  /**
   * Resolve an escalation
   * @param id Escalation ID to resolve
   * @param resolutionData Resolution data
   * @returns Promise with updated escalation data
   */
  resolveEscalation: async (id: string, resolutionData: ResolveEscalationRequest): Promise<EscalationResponse> => {
    return apiClient.put<EscalationResponse>(`/escalations/${id}/resolve`, resolutionData);
  },

  /**
   * Get comments for an escalation
   * @param id Escalation ID
   * @returns Promise with comments data
   */
  getEscalationComments: async (id: string): Promise<EscalationCommentsResponse> => {
    return apiClient.get<EscalationCommentsResponse>(`/escalations/${id}/comments`);
  },

  /**
   * Add a comment to an escalation
   * @param id Escalation ID
   * @param commentData Comment data
   * @returns Promise with created comment data
   */
  addEscalationComment: async (id: string, commentData: CreateEscalationCommentRequest): Promise<EscalationCommentResponse> => {
    return apiClient.post<EscalationCommentResponse>(`/escalations/${id}/comments`, commentData);
  }
};
