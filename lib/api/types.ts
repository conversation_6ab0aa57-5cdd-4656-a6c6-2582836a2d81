
/**
 * API types for the call center application
 */

// Common types
export interface ApiResponse<T> {
  success: boolean;
  message?: string;
  data?: T;
}

// Auth types
export interface LoginRequest {
  email: string;
  password: string;
}

export interface TokenResponse {
  accessToken: string;
  refreshToken: string;
}

export interface AuthResponse {
  success: boolean;
  message?: string;
  tokens?: TokenResponse;
  user?: User;
  data?: {
    requiresPasswordChange?: boolean;
    temporaryToken?: string;
  };
}

// User types
export enum UserRole {
  PLATFORM_OWNER = 'Platform Owner',
  SUPERVISOR = 'Supervisor',
  AGENT = 'Agent',
  USER = 'User',
}

// Category types
export interface Category {
  id: string;
  name: string;
  description?: string;
  is_active: boolean;
  parent_id: string | null;
  created_at: string;
  updated_at: string;
}

export interface CreateCategoryRequest {
  name: string;
  description?: string;
  is_active?: boolean;
}

export interface UpdateCategoryRequest extends CreateCategoryRequest {
  parent_id?: string | null;
}

export interface CategoryResponse extends ApiResponse<Category> {}
export interface CategoriesResponse extends ApiResponse<Category[]> {}

// Product types
export interface Product {
  id: string;
  name: string;
  description?: string;
  created_at: string;
  updated_at: string;
}

export interface CreateProductRequest {
  name: string;
  description?: string;
}

export interface UpdateProductRequest extends CreateProductRequest {}

export interface ProductResponse extends ApiResponse<Product> {}
export interface ProductsResponse extends ApiResponse<Product[]> {}

// Channel types
export interface Channel {
  id: string;
  name: string;
  type: string;
  description?: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface CreateChannelRequest {
  name: string;
  type: string;
  description?: string;
  is_active?: boolean;
}

export interface UpdateChannelRequest extends CreateChannelRequest {}

export interface ChannelResponse extends ApiResponse<Channel> {}
export interface ChannelsResponse extends ApiResponse<Channel[]> {}

export interface User {
  id: string;
  email: string;
  first_name: string;
  last_name: string;
  phone_number: string;
  role_id: string;
  role?: Role | null; // Role can be null as seen in the API response
  created_at: string;
  updated_at: string;
  status: string; // Can be 'ACTIVE' or 'INACTIVE'
  force_password_change?: boolean; // Flag indicating if user needs to change password
  product?: Product;

  // UI-specific properties (camelCase versions for frontend use)
  firstName?: string;
  lastName?: string;
  username?: string; // Usually derived from email
}

export interface CreateUserRequest {
  first_name: string;
  last_name: string;
  email: string;
  role_id: string;
}

export interface UpdateUserRequest {
  first_name?: string;
  last_name?: string;
  email?: string;
  role_id?: string;
  is_active?: boolean;
}

export interface UpdatePasswordRequest {
  current_password: string;
  new_password: string;
  confirm_password: string;
}

// Role types
export interface Permission {
  id: string;
  name: string;
  code: string;
  description: string;
  category: string;
  required_level: number;
  created_at: string;
  updated_at: string;
  createdAt: string;
  updatedAt: string;
  role_permissions: {
    role_id: string;
    permission_id: string;
  };
}

export interface Role {
  id: string;
  name: string;
  description: string;
  permission_level: number;
  is_system_role: boolean;
  created_at: string;
  updated_at: string;
  createdAt: string;
  updatedAt: string;
  permissions: Permission[];  // Make sure this is not optional
}

// Contact types
export enum ContactCategory {
  CUSTOMER = 'customer',
  LEAD = 'lead',
  PARTNER = 'partner',
  VENDOR = 'vendor',
  OTHER = 'other',
}

export enum ContactStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
}

export enum PreferredContactMethod {
  EMAIL = 'email',
  PHONE = 'phone',
  SMS = 'sms',
  WHATSAPP = 'whatsapp',
}

export interface Contact {
  id: string;
  first_name: string;
  last_name: string;
  email: string;
  phone: string;
  company?: string;
  address?: string;
  city?: string;
  country?: string;
  notes?: string;
  preferred_contact_method: PreferredContactMethod;
  status: ContactStatus;
  category: ContactCategory;
  created_at: string;
  updated_at: string;
  created_by: string;
}

export interface CreateContactRequest {
  first_name: string;
  last_name: string;
  email: string;
  phone: string;
  company?: string;
  address?: string;
  city?: string;
  country?: string;
  notes?: string;
  preferred_contact_method?: PreferredContactMethod;
  status?: ContactStatus;
  category: ContactCategory;
}

export interface UpdateContactRequest {
  first_name?: string;
  last_name?: string;
  email?: string;
  phone?: string;
  company?: string;
  address?: string;
  city?: string;
  country?: string;
  notes?: string;
  preferred_contact_method?: PreferredContactMethod;
  status?: ContactStatus;
  category?: ContactCategory;
}

// Ticket types
export enum TicketStatus {
  OPEN = 'open',
  IN_PROGRESS = 'in_progress',
  WAITING = 'waiting',
  RESOLVED = 'resolved',
  CLOSED = 'closed',
}

export enum TicketPriority {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  URGENT = 'urgent',
}

export interface EscalationDetails {
  level: number;
  at: string;
  reason: string;
  by: {
    id: string;
    first_name: string;
    last_name: string;
    email: string;
  };
}

export interface Ticket {
  id: string;
  description: string;
  priority: string;
  status: string;
  category_id: string;
  contact_id: string;
  channel_id: string;
  product_id: string;
  assigned_to: string;
  due_date: string | null;
  resolution: string | null;
  resolution_date: string | null;
  resolution_time: string | null;
  created_at: string;
  updated_at: string;
  created_by: string;
  escalation_level?: number;
  escalated_at?: string | null;
  escalated_by?: string | null;
  escalation_reason?: string | null;
  contact: {
    id: string;
    first_name: string;
    last_name: string;
    email: string;
    phone: string;
  };
  assignee: {
    id: string;
    first_name: string;
    last_name: string;
    email: string;
  };
  creator: {
    id: string;
    first_name: string;
    last_name: string;
    email: string;
  };
  category: {
    id: string;
    name: string;
    description: string;
  };
  channel: {
    id: string;
    name: string;
    type: string;
  };
  comments?: TicketComment[];
}

export interface CreateTicketRequest {
  description: string;
  priority: string;
  status: string;
  category_id: string;
  contact_id: string;
  channel_id: string;
  product_id: string;
  // assigned_to: string;
  due_date: string;
}

export interface UpdateTicketRequest {
  description?: string;
  priority?: TicketPriority;
  status?: TicketStatus;
  category_id?: string;
  contact_id?: string;
  channel_id?: string;
  product_id?: string;
  assigned_to?: string;
  due_date?: string;
}

export interface EscalateTicketRequest {
  reason: string;
  priority: TicketPriority;
}

export interface AssignTicketRequest {
  assigned_to: string;
}

export interface TicketComment {
  id: string;
  ticket_id: string;
  user_id: string;
  user?: User;
  content: string;
  type?: string;
  is_internal?: boolean;
  created_at: string;
  updated_at: string;
}

export interface AddTicketCommentRequest {
  content: string;
  is_internal?: boolean;
}

// Agent types
export interface AgentAttributes {
  skills: string[];
  status: string;
  agent_id: string;
  current_calls: number;
  department_id: string;
  daily_call_target: number;
  performance_score: number;
  last_call_end_time: string | null;
  last_status_change: string;
  availability_status: string;
  total_calls_handled: number;
  max_concurrent_calls: number;
  average_handling_time: number;
}

export interface SuspensionInfo {
  reason: string;
  suspended_at: string;
}

export interface RoleAttributes {
  agent: AgentAttributes;
  suspension_info?: SuspensionInfo;
}

export interface Department {
  id: string;
  name: string;
  description: string;
  created_at: string;
  updated_at: string;
  createdAt: string;
  updatedAt: string;
}

export interface Agent extends User {
  extension: string | null;
  last_login_at: string | null;
  role_attributes: RoleAttributes;
  failed_login_attempts: number;
  locked_until: string | null;
  email_verified: boolean;
  password_changed_at: string | null;
  password_reset_token: string | null;
  password_reset_expires: string | null;
  email_verification_token: string | null;
  email_verification_expires: string | null;
  deleted_at: string | null;
  department?: Department;
}

export interface AgentsResponse {
  success: boolean;
  data: Agent[];
}

// FAQ types
export enum FAQStatus {
  PENDING = 'pending',
  APPROVED = 'approved',
  REJECTED = 'rejected'
}

export interface FAQ {
  id: string;
  question: string;
  answer: string;
  category_id: string;
  product_id: string | null;
  is_flagged: boolean;
  flagged_reason?: string;
  flagged_by?: string;
  flagged_at?: string;
  is_active: boolean;
  is_published: boolean;
  status?: FAQStatus;
  approved_by?: string;
  approved_at?: string;
  rejected_reason?: string;
  created_by: string;
  created_at: string;
  updated_at: string;
  creator?: {
    id: string;
    first_name: string;
    last_name: string;
    email: string;
  };
  category?: Category;
  product?: Product | null;
  flagger?: {
    id: string;
    first_name: string;
    last_name: string;
    email: string;
  };
  approver?: {
    id: string;
    first_name: string;
    last_name: string;
    email: string;
  };
}

export interface CreateFAQRequest {
  question: string;
  answer: string;
  category_id: string;
  product_id?: string | null;
  is_active?: boolean;
  is_published?: boolean;
  status?: FAQStatus;
}

export interface UpdateFAQRequest {
  question?: string;
  answer?: string;
  category_id?: string;
  product_id?: string | null;
  is_active?: boolean;
  is_published?: boolean;
  status?: FAQStatus;
  approved_by?: string;
  approved_at?: string;
  rejected_reason?: string;
}

export interface FlagFAQRequest {
  is_flagged: boolean;
  flag_reason?: string;
}

export interface FAQResponse extends ApiResponse<FAQ> {}
export interface FAQsResponse extends ApiResponse<FAQ[]> {}
export interface BatchCreateFAQResponse extends ApiResponse<{
  success: number;
  failed: number;
  errors?: Record<string, string>;
}> {}

// Escalation types
export interface Escalation {
  id: string;
  ticket_id: string;
  level: number;
  reason: string;
  resolved: boolean;
  resolved_at: string | null;
  resolved_by: string | null;
  resolution_notes: string | null;
  escalated_by: string;
  escalated_at: string;
  created_at: string;
  updated_at: string;
  escalate_to?: string; // User ID to escalate to (optional)
  escalator?: User;
  resolver?: User | null;
  ticket?: Ticket;
}

export interface EscalationComment {
  id: string;
  escalation_id: string;
  comment: string;
  user_id: string;
  is_internal: boolean;
  created_at: string;
  updated_at: string;
  user?: User;
}

export interface CreateEscalationRequest {
  ticket_id: string;
  reason: string;
  level: number;
  escalate_to?: string; // User ID to escalate to (optional)
}

export interface CreateEscalationCommentRequest {
  comment: string;
  is_internal?: boolean;
}

export interface ResolveEscalationRequest {
  resolution_notes: string;
}

export interface EscalationResponse extends ApiResponse<Escalation> {}
export interface EscalationsResponse extends ApiResponse<Escalation[]> {}
export interface EscalationCommentResponse extends ApiResponse<EscalationComment> {}
export interface EscalationCommentsResponse extends ApiResponse<EscalationComment[]> {}

// Pagination types
export interface PaginationParams {
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'ASC' | 'DESC';
  assigned_to?: string;
}

export interface ApiResponse<T> {
  success: boolean;
  message?: string;
}

export interface UsersResponse {
  success: boolean;
  message?: string;
  data: User[];
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    total: number;
    page: number;
    limit: number;
    total_pages: number;
  };
}

// Audit Log types
export interface AuditLog {
  id: string;
  user_id: string;
  action: string;
  resource_type: string;
  resource_id: string;
  details: Record<string, any>;
  metadata: Record<string, any> | null;
  status: string;
  error_message: string | null;
  ip_address: string;
  user_agent: string;
  created_at: string;
  timestamp: string;
  user: {
    id: string;
    email: string;
    first_name: string;
    last_name: string;
    role_id: string;
  };
}
