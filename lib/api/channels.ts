import {
  ChannelResponse,
  ChannelsResponse,
  CreateChannelRequest,
  UpdateChannelRequest
} from './types';
import { apiClient } from './client';

export async function getChannels(): Promise<ChannelsResponse> {
  return apiClient.get<ChannelsResponse>('/channels');
}

export async function createChannel(data: CreateChannelRequest): Promise<ChannelResponse> {
  return apiClient.post<ChannelResponse>('/channels', data);
}


export async function updateChannel(
  id: string,
  data: UpdateChannelRequest
): Promise<ChannelResponse> {
  return apiClient.put<ChannelResponse>(`/channels/${id}`, data);
}

export async function deleteChannel(id: string): Promise<void> {
  await apiClient.delete<void>(`/channels/${id}`);
}
