import { Role } from '@/lib/api/types';

// Permission codes mapping for menu items and features
export const PERMISSION_CODES = {
  // User permissions
  canViewUsers: 'user:view',
  canCreateUsers: 'user:create',
  canUpdateUsers: 'user:update',
  canDeleteUsers: 'user:delete',
  canManageUserRoles: 'user:manage_roles',

  // Agent permissions
  canViewAgents: 'agent:view',
  canCreateAgents: 'agent:create',
  canUpdateAgents: 'agent:update',
  canDeleteAgents: 'agent:delete',

  // Report permissions
  canViewReports: 'report:view',
  canGenerateReports: 'report:generate',
  canExportReports: 'report:export',

  // Ticket permissions
  canViewTickets: 'tickets:read',
  canCreateTickets: 'tickets:create',
  canUpdateTickets: 'tickets:update',
  canDeleteTickets: 'tickets:delete',
  canCommentOnTickets: 'tickets:comment',

  // Contact permissions
  canViewContacts: 'contacts:read',
  canCreateContacts: 'contacts:create',
  canUpdateContacts: 'contacts:update',
  canDeleteContacts: 'contacts:delete',

  // Call permissions
  canViewCalls: 'call:view',
  canCreateCalls: 'call:create',
  canUpdateCalls: 'call:update',
  canDeleteCalls: 'call:delete',
  canAssignCalls: 'call:assign',

  // Department permissions
  canViewDepartments: 'department:view',
  canCreateDepartments: 'department:create',
  canUpdateDepartments: 'department:update',
  canDeleteDepartments: 'department:delete',

  // System permissions
  canManageSettings: 'system:settings',
  canViewSystemLogs: 'system:logs',
  canManageKPIs: 'system:kpis',

  // Role permissions
  canViewRoles: 'role:view',
  canCreateRoles: 'role:create',
  canUpdateRoles: 'role:update',
  canDeleteRoles: 'role:delete',
} as const;

// Helper function to check if a role has a specific permission
export function hasPermission(role: Role | undefined | null, permissionKey: keyof typeof PERMISSION_CODES): boolean {
  if (!role) return false;

  // Platform Owner has all permissions
  if (role.name === 'Platform Owner') return true;

  if (!role.permissions) return false;

  // Get the actual permission code from our mapping
  const requiredPermissionCode = PERMISSION_CODES[permissionKey];

  // Check if the role has the permission
  return role.permissions.some(permission => permission.code === requiredPermissionCode);
}

