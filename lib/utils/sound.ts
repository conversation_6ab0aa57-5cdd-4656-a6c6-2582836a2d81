'use client';

/**
 * Utility for playing sounds in the application
 */

// Cache for preloaded audio elements
const audioCache: Record<string, HTMLAudioElement> = {};

// Track if user has interacted with the page
let userInteracted = false;

// Set userInteracted to true on any user interaction
if (typeof window !== 'undefined') {
  const interactionEvents = ['click', 'touchstart', 'keydown'];
  interactionEvents.forEach(event => {
    window.addEventListener(event, () => {
      userInteracted = true;
      // Try to resume any suspended audio contexts
      resumeAudioContexts();
    }, { once: true });
  });
}

// Function to resume all audio contexts
const resumeAudioContexts = () => {
  if (typeof window === 'undefined') return;

  // If the browser supports AudioContext, try to resume it
  if (window.AudioContext || (window as any).webkitAudioContext) {
    const AudioContextClass = window.AudioContext || (window as any).webkitAudioContext;
    const audioContext = new AudioContextClass();
    if (audioContext.state === 'suspended') {
      audioContext.resume().catch(err => {
        console.warn('Failed to resume AudioContext:', err);
      });
    }
  }
};

// Default sound URLs
const SOUNDS = {
  INCOMING_CALL: '/sounds/incoming-call.wav',
  CALL_ENDED: '/sounds/incoming-call.mp3',
  NOTIFICATION: '/sounds/incoming-call.mp3',
};

/**
 * Preload a sound for faster playback
 * @param soundUrl URL of the sound to preload
 * @returns The preloaded audio element
 */
export const preloadSound = (soundUrl: string): HTMLAudioElement => {
  if (typeof window === 'undefined') return null as any;

  if (audioCache[soundUrl]) {
    return audioCache[soundUrl];
  }

  try {
    const audio = new Audio(soundUrl);
    audio.preload = 'auto';
    audioCache[soundUrl] = audio;
    return audio;
  } catch (error) {
    console.error('Error preloading sound:', error);
    return null as any;
  }
};

/**
 * Play a sound
 * @param soundUrl URL of the sound to play
 * @param options Options for playback
 * @returns The audio element
 */
export const playSound = (
  soundUrl: string,
  options: {
    loop?: boolean;
    volume?: number;
    forcePlay?: boolean; // Force play even if user hasn't interacted
  } = {}
): HTMLAudioElement | null => {
  if (typeof window === 'undefined') return null;

  try {
    // Check if we can play audio (either user has interacted or we're forcing play)
    if (!userInteracted && !options.forcePlay) {
      console.warn('Attempting to play sound before user interaction. Sound may be blocked by browser.');
    }

    // Use cached audio if available, otherwise create a new one
    let audio = audioCache[soundUrl];

    // If we don't have a cached audio or it's in an error state, create a new one
    if (!audio || audio.error) {
      audio = new Audio(soundUrl);
      audioCache[soundUrl] = audio;
    }

    // Set options
    audio.loop = options.loop || false;
    audio.volume = options.volume !== undefined ? options.volume : 0.5;

    // Reset the audio to the beginning
    try {
      audio.currentTime = 0;
    } catch (e) {
      console.warn('Could not set currentTime, audio may not be loaded yet:', e);
    }

    // Try to play the sound
    const playPromise = audio.play();

    // Modern browsers return a promise from play()
    if (playPromise !== undefined) {
      playPromise.catch(error => {
        console.error('Error playing sound:', error);

        // If autoplay was prevented, we'll try again when the user interacts
        if (error.name === 'NotAllowedError') {
          console.warn('Autoplay prevented by browser. Sound will play after user interaction.');

          // Set up a one-time event listener to try playing again after user interaction
          const retryPlay = () => {
            audio.play().catch(e => console.error('Retry play failed:', e));

            // Remove the event listeners after trying once
            ['click', 'touchstart', 'keydown'].forEach(evt => {
              window.removeEventListener(evt, retryPlay);
            });
          };

          ['click', 'touchstart', 'keydown'].forEach(evt => {
            window.addEventListener(evt, retryPlay, { once: true });
          });
        }
      });
    }

    return audio;
  } catch (error) {
    console.error('Error playing sound:', error);
    return null;
  }
};

/**
 * Stop a playing sound
 * @param audio The audio element to stop
 */
export const stopSound = (audio: HTMLAudioElement | null): void => {
  if (!audio) return;

  try {
    audio.pause();
    audio.currentTime = 0;
  } catch (error) {
    console.error('Error stopping sound:', error);
  }
};

/**
 * Preload all default sounds
 * This should be called early in the application lifecycle
 */
export const preloadAllSounds = (): void => {
  if (typeof window === 'undefined') return;

  console.log('Preloading all sounds...');

  // Preload all default sounds
  Object.values(SOUNDS).forEach(soundUrl => {
    preloadSound(soundUrl);
  });
};

/**
 * Check if a sound can be played
 * @returns True if sounds can be played
 */
export const canPlaySounds = (): boolean => {
  return typeof window !== 'undefined' && userInteracted;
};

// Export the default sounds
export { SOUNDS };
