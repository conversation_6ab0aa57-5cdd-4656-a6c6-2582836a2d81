/**
 * Utility functions for error handling and logging
 */

// Error types for better categorization
export enum ErrorType {
  NETWORK = 'network',
  AUTHENTICATION = 'authentication',
  AUTHORIZATION = 'authorization',
  VALIDATION = 'validation',
  NOT_FOUND = 'not_found',
  SERVER = 'server',
  CLIENT = 'client',
  UNKNOWN = 'unknown',
}

// Interface for structured error information
export interface ErrorInfo {
  type: ErrorType;
  message: string;
  code?: string;
  details?: Record<string, any>;
  originalError?: Error;
}

/**
 * Categorize an error based on its properties and message
 */
export function categorizeError(error: Error): ErrorType {
  const message = error.message.toLowerCase();
  
  if (message.includes('network') || message.includes('fetch') || message.includes('timeout')) {
    return ErrorType.NETWORK;
  }
  
  if (message.includes('unauthorized') || message.includes('unauthenticated') || message.includes('login')) {
    return ErrorType.AUTHENTICATION;
  }
  
  if (message.includes('permission') || message.includes('forbidden') || message.includes('access denied')) {
    return ErrorType.AUTHORIZATION;
  }
  
  if (message.includes('validation') || message.includes('invalid') || message.includes('required')) {
    return ErrorType.VALIDATION;
  }
  
  if (message.includes('not found') || message.includes('404')) {
    return ErrorType.NOT_FOUND;
  }
  
  if (message.includes('server') || message.includes('500')) {
    return ErrorType.SERVER;
  }
  
  return ErrorType.UNKNOWN;
}

/**
 * Get a user-friendly message based on the error type
 */
export function getUserFriendlyMessage(errorType: ErrorType): string {
  switch (errorType) {
    case ErrorType.NETWORK:
      return 'There was a problem connecting to the server. Please check your internet connection and try again.';
    
    case ErrorType.AUTHENTICATION:
      return 'You need to log in to access this feature. Your session may have expired.';
    
    case ErrorType.AUTHORIZATION:
      return 'You don\'t have permission to access this resource. Please contact your administrator.';
    
    case ErrorType.VALIDATION:
      return 'Some of the information you provided is invalid. Please check your inputs and try again.';
    
    case ErrorType.NOT_FOUND:
      return 'The resource you\'re looking for could not be found. It may have been moved or deleted.';
    
    case ErrorType.SERVER:
      return 'The server encountered an error while processing your request. Our team has been notified.';
    
    case ErrorType.CLIENT:
      return 'There was a problem with the application. Please refresh the page and try again.';
    
    case ErrorType.UNKNOWN:
    default:
      return 'An unexpected error occurred. Our team has been notified and is working to fix the issue.';
  }
}

/**
 * Create a structured error object from an Error
 */
export function createErrorInfo(error: Error): ErrorInfo {
  const errorType = categorizeError(error);
  
  return {
    type: errorType,
    message: error.message || getUserFriendlyMessage(errorType),
    originalError: error,
  };
}

/**
 * Log an error to the console and potentially to a monitoring service
 */
export function logError(error: Error, context?: Record<string, any>): void {
  console.error('Application error:', error, context);
  
  // Here you would add code to send the error to your monitoring service
  // Example: sendToErrorMonitoring(error, context);
}

/**
 * Handle API response errors
 */
export async function handleApiResponse(response: Response): Promise<any> {
  if (!response.ok) {
    let errorMessage = `API Error: ${response.status} ${response.statusText}`;
    let errorData: any = {};
    
    try {
      errorData = await response.json();
      if (errorData.message) {
        errorMessage = errorData.message;
      }
    } catch (e) {
      // If we can't parse the JSON, just use the status text
    }
    
    const error = new Error(errorMessage);
    (error as any).status = response.status;
    (error as any).data = errorData;
    throw error;
  }
  
  return response.json();
}
