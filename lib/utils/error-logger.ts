/**
 * Error logger utility for the application
 * This can be extended to send errors to a monitoring service like Sentry
 */

export interface ErrorLogData {
  message: string
  stack?: string
  type: 'api' | 'uncaught' | 'unhandled-promise' | 'permission' | 'validation' | 'other'
  location?: string
  userId?: string
  metadata?: Record<string, any>
  statusCode?: number
}

// In-memory store for errors (in a real app, you'd send these to a service)
const errorLogs: ErrorLogData[] = []

/**
 * Log an error to the console and potentially to a monitoring service
 */
export function logError(error: ErrorLogData): void {
  // Add timestamp
  const errorWithTimestamp = {
    ...error,
    timestamp: new Date().toISOString(),
  }
  
  // Store in memory (for development purposes)
  errorLogs.push(errorWithTimestamp)
  
  // Log to console
  console.error('Error logged:', errorWithTimestamp)
  
  // In production, you would send this to a monitoring service
  if (process.env.NODE_ENV === 'production') {
    // Example: send to a monitoring service
    // sendToMonitoringService(errorWithTimestamp)
  }
  
  // For permission errors, we might want to track them separately
  if (error.type === 'permission') {
    console.warn('Permission error detected:', error.message)
    // You could track these separately or alert admins
  }
}

/**
 * Get all logged errors (for debugging/admin purposes)
 */
export function getErrorLogs(): ErrorLogData[] {
  return [...errorLogs]
}

/**
 * Clear all error logs (for debugging/admin purposes)
 */
export function clearErrorLogs(): void {
  errorLogs.length = 0
}

/**
 * Format an error for display
 */
export function formatError(error: unknown): string {
  if (error instanceof Error) {
    return error.message
  }
  
  if (typeof error === 'string') {
    return error
  }
  
  return 'An unknown error occurred'
}
