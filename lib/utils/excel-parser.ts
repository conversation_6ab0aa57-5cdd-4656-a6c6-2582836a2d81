import * as XLSX from 'xlsx';
import { CreateFAQRequest, Category, Product } from '@/lib/api/types';
import { createFaqSchema } from '@/lib/validations/faqs';
import { ZodError } from 'zod';

interface ParseResult {
  data: CreateFAQRequest[];
  errors: Record<string, string>;
  totalRows: number;
  validRows: number;
}

/**
 * Parse Excel file for FAQ batch creation
 * @param file Excel file to parse
 * @returns Parsed data and validation errors
 */
export const parseFAQExcel = async (file: File): Promise<ParseResult> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();

    reader.onload = (e) => {
      try {
        const data = new Uint8Array(e.target?.result as ArrayBuffer);
        const workbook = XLSX.read(data, { type: 'array' });

        // Get the first worksheet
        const firstSheetName = workbook.SheetNames[0];
        const worksheet = workbook.Sheets[firstSheetName];

        // Convert to JSON
        const jsonData = XLSX.utils.sheet_to_json(worksheet);

        const result: ParseResult = {
          data: [],
          errors: {},
          totalRows: jsonData.length,
          validRows: 0
        };

        // Process each row
        jsonData.forEach((row: any, index) => {
          try {
            // Map Excel columns to FAQ fields
            let categoryId = '';
            let productId = null;

            // Extract category ID from format "Category Name (ID)"
            const categoryValue = row.Category || row.category || '';
            if (categoryValue) {
              const categoryMatch = categoryValue.match(/\(([\w-]+)\)$/);
              if (categoryMatch && categoryMatch[1]) {
                categoryId = categoryMatch[1];
              } else {
                // If no match, use the value as is (for backward compatibility)
                categoryId = categoryValue;
              }
            }

            // Extract product ID from format "Product Name (ID)"
            const productValue = row.Product || row.product || '';
            if (productValue) {
              const productMatch = productValue.match(/\(([\w-]+)\)$/);
              if (productMatch && productMatch[1]) {
                productId = productMatch[1];
              } else {
                // If no match, use the value as is (for backward compatibility)
                productId = productValue;
              }
            }

            const faqData: CreateFAQRequest = {
              question: row.Question || row.question || '',
              answer: row.Answer || row.answer || '',
              category_id: categoryId || row.CategoryID || row.category_id || row['Category ID'] || '',
              product_id: productId || row.ProductID || row.product_id || row['Product ID'] || null,
              is_active: row.IsActive || row.is_active || row['Is Active'] === 'Yes' || true
            };

            // Validate using Zod schema
            createFaqSchema.parse(faqData);

            // Add to valid data
            result.data.push(faqData);
            result.validRows++;
          } catch (error) {
            // Handle validation errors
            if (error instanceof ZodError) {
              const fieldErrors = error.errors.map(err => `${err.path}: ${err.message}`).join(', ');
              result.errors[`Row ${index + 2}`] = fieldErrors;
            } else {
              result.errors[`Row ${index + 2}`] = 'Invalid data format';
            }
          }
        });

        resolve(result);
      } catch (error) {
        reject(new Error('Failed to parse Excel file'));
      }
    };

    reader.onerror = () => {
      reject(new Error('Failed to read file'));
    };

    reader.readAsArrayBuffer(file);
  });
};

/**
 * Generate a template Excel file for FAQ batch creation with category and product dropdowns
 * @param categories List of categories to include in the dropdown
 * @param products List of products to include in the dropdown
 * @returns Excel file as a Blob
 */
export const generateFAQTemplate = (categories: Category[] = [], products: Product[] = []): Blob => {
  // Create a data sheet for categories and products
  // This sheet will be hidden in Excel but will contain the data for dropdowns
  const dataWs = XLSX.utils.aoa_to_sheet([
    ['Category Options'],
    ...categories.map(cat => [`${cat.name} (${cat.id})`]),
    [''],
    ['Product Options'],
    ...products.map(prod => [`${prod.name} (${prod.id})`]),
  ]);

  // Create main worksheet with headers
  const mainWs = XLSX.utils.aoa_to_sheet([
    ['Question', 'Answer', 'Category', 'Product', 'Is Active'],
    ['What is the return policy?', 'You can return items within 30 days of purchase.', '', '', 'Yes'],
    ['How do I reset my password?', 'Go to the login page and click on "Forgot Password".', '', '', 'Yes']
  ]);

  // Set column widths
  const wscols = [
    { wch: 40 }, // Question
    { wch: 60 }, // Answer
    { wch: 36 }, // Category
    { wch: 36 }, // Product
    { wch: 10 }  // Is Active
  ];

  mainWs['!cols'] = wscols;

  // Create workbook
  const wb = XLSX.utils.book_new();
  XLSX.utils.book_append_sheet(wb, mainWs, 'FAQs Template');
  XLSX.utils.book_append_sheet(wb, dataWs, 'Data');

  // Add notes to cells explaining how to use the template
  if (!mainWs['!comments']) mainWs['!comments'] = {};

  mainWs['!comments']['C1'] = {
    t: 'Copy and paste a category from the Data sheet. Format: Category Name (ID)'
  };

  mainWs['!comments']['D1'] = {
    t: 'Copy and paste a product from the Data sheet or leave blank. Format: Product Name (ID)'
  };

  // Add instructions in the main sheet
  const instructionsWs = XLSX.utils.aoa_to_sheet([
    ['FAQ Batch Upload Instructions'],
    [''],
    ['1. Fill in the Question and Answer columns with your FAQ content.'],
    ['2. For Category: Select from the list of categories in the Data sheet.'],
    ['3. For Product: Select from the list of products in the Data sheet (optional).'],
    ['4. For Is Active: Enter "Yes" or "No".'],
    [''],
    ['Note: The format for categories and products must be "Name (ID)" as shown in the Data sheet.'],
  ]);

  // Set column width for instructions
  instructionsWs['!cols'] = [{ wch: 100 }];

  // Add instructions sheet
  XLSX.utils.book_append_sheet(wb, instructionsWs, 'Instructions');

  // Generate Excel file
  const excelBuffer = XLSX.write(wb, {
    bookType: 'xlsx',
    type: 'array',
    bookSST: false
  });

  return new Blob([excelBuffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
};
