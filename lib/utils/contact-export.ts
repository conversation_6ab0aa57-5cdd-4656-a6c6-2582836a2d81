import * as ExcelJS from 'exceljs';
import { Contact } from '@/lib/api/types';

export type ExportFormat = 'csv' | 'excel' | 'pdf';

/**
 * Export contacts to CSV format
 */
export const exportContactsToCSV = (contacts: Contact[], filename = 'contacts') => {
  const headers = [
    'First Name',
    'Last Name',
    'Email',
    'Phone',
    'Company',
    'Address',
    'City',
    'Country',
    'Category',
    'Status',
    'Preferred Contact Method',
    'Created At',
    'Notes'
  ];

  const csvContent = [
    headers.join(','),
    ...contacts.map(contact => [
      `"${contact.first_name}"`,
      `"${contact.last_name}"`,
      `"${contact.email}"`,
      `"${contact.phone}"`,
      `"${contact.company || ''}"`,
      `"${contact.address || ''}"`,
      `"${contact.city || ''}"`,
      `"${contact.country || ''}"`,
      `"${contact.category}"`,
      `"${contact.status}"`,
      `"${contact.preferred_contact_method}"`,
      `"${new Date(contact.created_at).toLocaleDateString()}"`,
      `"${contact.notes || ''}"`
    ].join(','))
  ].join('\n');

  const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
  const link = document.createElement('a');
  const url = URL.createObjectURL(blob);
  link.setAttribute('href', url);
  link.setAttribute('download', `${filename}.csv`);
  link.style.visibility = 'hidden';
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
};

/**
 * Export contacts to Excel format
 */
export const exportContactsToExcel = async (contacts: Contact[], filename = 'contacts') => {
  const workbook = new ExcelJS.Workbook();
  const worksheet = workbook.addWorksheet('Contacts');

  // Define columns
  worksheet.columns = [
    { header: 'First Name', key: 'first_name', width: 15 },
    { header: 'Last Name', key: 'last_name', width: 15 },
    { header: 'Email', key: 'email', width: 25 },
    { header: 'Phone', key: 'phone', width: 15 },
    { header: 'Company', key: 'company', width: 20 },
    { header: 'Address', key: 'address', width: 30 },
    { header: 'City', key: 'city', width: 15 },
    { header: 'Country', key: 'country', width: 15 },
    { header: 'Category', key: 'category', width: 12 },
    { header: 'Status', key: 'status', width: 10 },
    { header: 'Preferred Contact Method', key: 'preferred_contact_method', width: 20 },
    { header: 'Created At', key: 'created_at', width: 15 },
    { header: 'Notes', key: 'notes', width: 30 },
  ];

  // Style the header row
  worksheet.getRow(1).font = { bold: true };
  worksheet.getRow(1).fill = {
    type: 'pattern',
    pattern: 'solid',
    fgColor: { argb: 'FFE0E0E0' }
  };

  // Add data rows
  contacts.forEach(contact => {
    worksheet.addRow({
      first_name: contact.first_name,
      last_name: contact.last_name,
      email: contact.email,
      phone: contact.phone,
      company: contact.company || '',
      address: contact.address || '',
      city: contact.city || '',
      country: contact.country || '',
      category: contact.category,
      status: contact.status,
      preferred_contact_method: contact.preferred_contact_method,
      created_at: new Date(contact.created_at).toLocaleDateString(),
      notes: contact.notes || '',
    });
  });

  // Auto-fit columns
  worksheet.columns.forEach(column => {
    if (column.header) {
      column.width = Math.max(column.width || 10, column.header.toString().length + 2);
    }
  });

  // Generate buffer and download
  const buffer = await workbook.xlsx.writeBuffer();
  const blob = new Blob([buffer], { 
    type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' 
  });
  
  const link = document.createElement('a');
  const url = URL.createObjectURL(blob);
  link.setAttribute('href', url);
  link.setAttribute('download', `${filename}.xlsx`);
  link.style.visibility = 'hidden';
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
};

/**
 * Export contacts to PDF format (simplified version)
 * Note: For a full PDF implementation, you might want to use libraries like jsPDF or Puppeteer
 */
export const exportContactsToPDF = (contacts: Contact[], filename = 'contacts') => {
  // For now, we'll create a simple HTML table and print it
  // In a production environment, you'd want to use a proper PDF library
  
  const htmlContent = `
    <!DOCTYPE html>
    <html>
    <head>
      <title>Contacts Export</title>
      <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        table { width: 100%; border-collapse: collapse; margin-top: 20px; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; font-weight: bold; }
        tr:nth-child(even) { background-color: #f9f9f9; }
        h1 { color: #333; }
        .export-info { margin-bottom: 20px; color: #666; }
      </style>
    </head>
    <body>
      <h1>Contacts Export</h1>
      <div class="export-info">
        <p>Generated on: ${new Date().toLocaleDateString()}</p>
        <p>Total contacts: ${contacts.length}</p>
      </div>
      <table>
        <thead>
          <tr>
            <th>Name</th>
            <th>Email</th>
            <th>Phone</th>
            <th>Company</th>
            <th>Category</th>
            <th>Status</th>
            <th>Created</th>
          </tr>
        </thead>
        <tbody>
          ${contacts.map(contact => `
            <tr>
              <td>${contact.first_name} ${contact.last_name}</td>
              <td>${contact.email}</td>
              <td>${contact.phone}</td>
              <td>${contact.company || '-'}</td>
              <td>${contact.category}</td>
              <td>${contact.status}</td>
              <td>${new Date(contact.created_at).toLocaleDateString()}</td>
            </tr>
          `).join('')}
        </tbody>
      </table>
    </body>
    </html>
  `;

  const printWindow = window.open('', '_blank');
  if (printWindow) {
    printWindow.document.write(htmlContent);
    printWindow.document.close();
    printWindow.focus();
    setTimeout(() => {
      printWindow.print();
      printWindow.close();
    }, 250);
  }
};

/**
 * Main export function that handles all formats
 */
export const exportContacts = async (
  contacts: Contact[], 
  format: ExportFormat, 
  filename = 'contacts'
) => {
  try {
    switch (format) {
      case 'csv':
        exportContactsToCSV(contacts, filename);
        break;
      case 'excel':
        await exportContactsToExcel(contacts, filename);
        break;
      case 'pdf':
        exportContactsToPDF(contacts, filename);
        break;
      default:
        throw new Error(`Unsupported export format: ${format}`);
    }
  } catch (error) {
    console.error('Export failed:', error);
    throw error;
  }
};
