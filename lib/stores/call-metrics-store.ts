import { create } from 'zustand';
import { persist } from 'zustand/middleware';

// Call metrics data structures
export interface CallMetric {
  id: string;
  agentId: string;
  callId: string;
  callerNumber: string;
  callerName: string;
  productName?: string;
  startTime: string;
  endTime?: string;
  duration: number; // in seconds
  handleTime: number; // total interaction time in seconds
  status: 'answered' | 'missed' | 'ended' | 'transferred' | 'escalated';
  resolutionStatus: 'resolved' | 'escalated' | 'follow_up_needed' | 'transferred' | 'unresolved';
  customerSatisfaction?: number; // 1-5 rating
  notes?: string;
  ticketCreated: boolean;
  ticketId?: string;
  callType: 'inbound' | 'outbound';
  isSimulated: boolean;
  timestamp: string;
}

export interface CallMetricsAggregated {
  totalCalls: number;
  totalDuration: number;
  averageHandleTime: number;
  averageDuration: number;
  resolutionRate: number;
  escalationRate: number;
  customerSatisfactionAverage: number;
  callsToday: number;
  callsThisWeek: number;
  callsThisMonth: number;
  missedCalls: number;
  answeredCalls: number;
}

export interface DailyMetrics {
  date: string;
  callCount: number;
  totalDuration: number;
  averageHandleTime: number;
  resolutionRate: number;
  customerSatisfaction: number;
}

interface CallMetricsState {
  // Raw call data
  callMetrics: CallMetric[];
  
  // Aggregated metrics
  aggregatedMetrics: CallMetricsAggregated;
  dailyMetrics: DailyMetrics[];
  
  // State management
  isLoading: boolean;
  lastUpdated: string | null;
  
  // Actions
  addCallMetric: (metric: Omit<CallMetric, 'id' | 'timestamp'>) => void;
  updateCallMetric: (id: string, updates: Partial<CallMetric>) => void;
  getMetricsForAgent: (agentId: string) => CallMetric[];
  getAggregatedMetricsForAgent: (agentId: string) => CallMetricsAggregated;
  getDailyMetricsForAgent: (agentId: string, days?: number) => DailyMetrics[];
  getAllMetrics: () => CallMetricsAggregated;
  clearMetrics: () => void;
  clearAgentMetrics: (agentId: string) => void;
  recalculateMetrics: () => void;
  getDashboardSummary: (agentId?: string) => {
    totalCallsToday: number;
    totalCallsThisWeek: number;
    totalCallsThisMonth: number;
    averageHandleTime: number;
    resolutionRate: number;
    customerSatisfaction: number;
    escalationRate: number;
    answeredCalls: number;
    missedCalls: number;
  };
}

// Helper functions
const calculateAggregatedMetrics = (metrics: CallMetric[], agentId?: string): CallMetricsAggregated => {
  const filteredMetrics = agentId 
    ? metrics.filter(m => m.agentId === agentId)
    : metrics;

  if (filteredMetrics.length === 0) {
    return {
      totalCalls: 0,
      totalDuration: 0,
      averageHandleTime: 0,
      averageDuration: 0,
      resolutionRate: 0,
      escalationRate: 0,
      customerSatisfactionAverage: 0,
      callsToday: 0,
      callsThisWeek: 0,
      callsThisMonth: 0,
      missedCalls: 0,
      answeredCalls: 0,
    };
  }

  const now = new Date();
  const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
  const thisWeek = new Date(today.getTime() - (today.getDay() * 24 * 60 * 60 * 1000));
  const thisMonth = new Date(now.getFullYear(), now.getMonth(), 1);

  const totalCalls = filteredMetrics.length;
  const totalDuration = filteredMetrics.reduce((sum, m) => sum + m.duration, 0);
  const totalHandleTime = filteredMetrics.reduce((sum, m) => sum + m.handleTime, 0);
  
  const resolvedCalls = filteredMetrics.filter(m => m.resolutionStatus === 'resolved').length;
  const escalatedCalls = filteredMetrics.filter(m => m.resolutionStatus === 'escalated').length;
  const answeredCalls = filteredMetrics.filter(m => m.status === 'answered' || m.status === 'ended').length;
  const missedCalls = filteredMetrics.filter(m => m.status === 'missed').length;
  
  const callsToday = filteredMetrics.filter(m => new Date(m.timestamp) >= today).length;
  const callsThisWeek = filteredMetrics.filter(m => new Date(m.timestamp) >= thisWeek).length;
  const callsThisMonth = filteredMetrics.filter(m => new Date(m.timestamp) >= thisMonth).length;

  const satisfactionRatings = filteredMetrics
    .filter(m => m.customerSatisfaction !== undefined)
    .map(m => m.customerSatisfaction!);

  return {
    totalCalls,
    totalDuration,
    averageHandleTime: totalCalls > 0 ? Math.round(totalHandleTime / totalCalls) : 0,
    averageDuration: totalCalls > 0 ? Math.round(totalDuration / totalCalls) : 0,
    resolutionRate: totalCalls > 0 ? Math.round((resolvedCalls / totalCalls) * 100) : 0,
    escalationRate: totalCalls > 0 ? Math.round((escalatedCalls / totalCalls) * 100) : 0,
    customerSatisfactionAverage: satisfactionRatings.length > 0 
      ? Math.round((satisfactionRatings.reduce((sum, rating) => sum + rating, 0) / satisfactionRatings.length) * 10) / 10
      : 0,
    callsToday,
    callsThisWeek,
    callsThisMonth,
    missedCalls,
    answeredCalls,
  };
};

const calculateDailyMetrics = (metrics: CallMetric[], agentId?: string, days: number = 30): DailyMetrics[] => {
  const filteredMetrics = agentId 
    ? metrics.filter(m => m.agentId === agentId)
    : metrics;

  const dailyData: { [date: string]: CallMetric[] } = {};
  
  // Group metrics by date
  filteredMetrics.forEach(metric => {
    const date = new Date(metric.timestamp).toISOString().split('T')[0];
    if (!dailyData[date]) {
      dailyData[date] = [];
    }
    dailyData[date].push(metric);
  });

  // Calculate daily metrics
  const dailyMetrics: DailyMetrics[] = [];
  const startDate = new Date();
  startDate.setDate(startDate.getDate() - days);

  for (let i = 0; i < days; i++) {
    const date = new Date(startDate);
    date.setDate(date.getDate() + i);
    const dateStr = date.toISOString().split('T')[0];
    
    const dayMetrics = dailyData[dateStr] || [];
    const callCount = dayMetrics.length;
    const totalDuration = dayMetrics.reduce((sum, m) => sum + m.duration, 0);
    const totalHandleTime = dayMetrics.reduce((sum, m) => sum + m.handleTime, 0);
    const resolvedCalls = dayMetrics.filter(m => m.resolutionStatus === 'resolved').length;
    
    const satisfactionRatings = dayMetrics
      .filter(m => m.customerSatisfaction !== undefined)
      .map(m => m.customerSatisfaction!);

    dailyMetrics.push({
      date: dateStr,
      callCount,
      totalDuration,
      averageHandleTime: callCount > 0 ? Math.round(totalHandleTime / callCount) : 0,
      resolutionRate: callCount > 0 ? Math.round((resolvedCalls / callCount) * 100) : 0,
      customerSatisfaction: satisfactionRatings.length > 0 
        ? Math.round((satisfactionRatings.reduce((sum, rating) => sum + rating, 0) / satisfactionRatings.length) * 10) / 10
        : 0,
    });
  }

  return dailyMetrics;
};

export const useCallMetricsStore = create<CallMetricsState>()(
  persist(
    (set, get) => ({
      callMetrics: [],
      aggregatedMetrics: {
        totalCalls: 0,
        totalDuration: 0,
        averageHandleTime: 0,
        averageDuration: 0,
        resolutionRate: 0,
        escalationRate: 0,
        customerSatisfactionAverage: 0,
        callsToday: 0,
        callsThisWeek: 0,
        callsThisMonth: 0,
        missedCalls: 0,
        answeredCalls: 0,
      },
      dailyMetrics: [],
      isLoading: false,
      lastUpdated: null,

      addCallMetric: (metric) => {
        const newMetric: CallMetric = {
          ...metric,
          id: `call_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`,
          timestamp: new Date().toISOString(),
        };

        set((state) => {
          const updatedMetrics = [...state.callMetrics, newMetric];
          return {
            callMetrics: updatedMetrics,
            aggregatedMetrics: calculateAggregatedMetrics(updatedMetrics),
            dailyMetrics: calculateDailyMetrics(updatedMetrics),
            lastUpdated: new Date().toISOString(),
          };
        });
      },

      updateCallMetric: (id, updates) => {
        set((state) => {
          const updatedMetrics = state.callMetrics.map(metric =>
            metric.id === id ? { ...metric, ...updates } : metric
          );
          return {
            callMetrics: updatedMetrics,
            aggregatedMetrics: calculateAggregatedMetrics(updatedMetrics),
            dailyMetrics: calculateDailyMetrics(updatedMetrics),
            lastUpdated: new Date().toISOString(),
          };
        });
      },

      getMetricsForAgent: (agentId) => {
        const { callMetrics } = get();
        return callMetrics.filter(metric => metric.agentId === agentId);
      },

      getAggregatedMetricsForAgent: (agentId) => {
        const { callMetrics } = get();
        return calculateAggregatedMetrics(callMetrics, agentId);
      },

      getDailyMetricsForAgent: (agentId, days = 30) => {
        const { callMetrics } = get();
        return calculateDailyMetrics(callMetrics, agentId, days);
      },

      getAllMetrics: () => {
        const { callMetrics } = get();
        return calculateAggregatedMetrics(callMetrics);
      },

      clearMetrics: () => {
        set({
          callMetrics: [],
          aggregatedMetrics: calculateAggregatedMetrics([]),
          dailyMetrics: [],
          lastUpdated: new Date().toISOString(),
        });
      },

      clearAgentMetrics: (agentId) => {
        set((state) => {
          const updatedMetrics = state.callMetrics.filter(metric => metric.agentId !== agentId);
          return {
            callMetrics: updatedMetrics,
            aggregatedMetrics: calculateAggregatedMetrics(updatedMetrics),
            dailyMetrics: calculateDailyMetrics(updatedMetrics),
            lastUpdated: new Date().toISOString(),
          };
        });
      },

      recalculateMetrics: () => {
        set((state) => ({
          aggregatedMetrics: calculateAggregatedMetrics(state.callMetrics),
          dailyMetrics: calculateDailyMetrics(state.callMetrics),
          lastUpdated: new Date().toISOString(),
        }));
      },

      getDashboardSummary: (agentId?: string) => {
        const { callMetrics } = get();
        const aggregated = agentId
          ? calculateAggregatedMetrics(callMetrics, agentId)
          : calculateAggregatedMetrics(callMetrics);

        return {
          totalCallsToday: aggregated.callsToday,
          totalCallsThisWeek: aggregated.callsThisWeek,
          totalCallsThisMonth: aggregated.callsThisMonth,
          averageHandleTime: aggregated.averageHandleTime,
          resolutionRate: aggregated.resolutionRate,
          customerSatisfaction: aggregated.customerSatisfactionAverage,
          escalationRate: aggregated.escalationRate,
          answeredCalls: aggregated.answeredCalls,
          missedCalls: aggregated.missedCalls,
        };
      },
    }),
    {
      name: 'call-metrics-storage',
      partialize: (state) => ({
        callMetrics: state.callMetrics,
        lastUpdated: state.lastUpdated,
      }),
    }
  )
);

// Hook for easy access to call metrics
export const useCallMetrics = () => {
  const store = useCallMetricsStore();

  // Ensure the store is properly initialized
  if (!store.getDashboardSummary) {
    console.warn('Call metrics store not properly initialized');
  }

  return {
    ...store,
    isReady: store.callMetrics.length > 0 || store.lastUpdated !== null,
  };
};

// Initialize the store to ensure it's properly set up
export const initializeCallMetricsStore = () => {
  const store = useCallMetricsStore.getState();
  if (!store.lastUpdated) {
    store.recalculateMetrics();
  }
  return store;
};
