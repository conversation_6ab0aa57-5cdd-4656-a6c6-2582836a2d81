import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { FilterPeriod, AdvancedFilters } from '@/lib/types/dashboard';
import { startOfDay, endOfDay, startOfWeek, endOfWeek, startOfMonth, endOfMonth, startOfQuarter, endOfQuarter } from 'date-fns';

interface FilterState {
  // Current filters
  filters: AdvancedFilters;
  
  // Auto-refresh settings
  autoRefresh: boolean;
  refreshInterval: number; // in seconds
  lastRefresh: Date | null;
  
  // Loading state
  isLoading: boolean;
  
  // Actions
  setPeriod: (period: FilterPeriod) => void;
  setCustomDateRange: (startDate: Date, endDate: Date) => void;
  setProductFilters: (productIds: string[]) => void;
  setCategoryFilters: (categoryIds: string[]) => void;
  setAgentFilters: (agentIds: string[]) => void;
  setCustomFilter: (key: string, value: any) => void;
  clearFilters: () => void;
  resetToDefaults: () => void;
  setAutoRefresh: (enabled: boolean, interval?: number) => void;
  triggerRefresh: () => void;
  setLoading: (loading: boolean) => void;
}

// Predefined period options
export const PERIOD_OPTIONS: FilterPeriod[] = [
  {
    type: 'daily',
    startDate: startOfDay(new Date()),
    endDate: endOfDay(new Date()),
    label: 'Today',
  },
  {
    type: 'weekly',
    startDate: startOfWeek(new Date()),
    endDate: endOfWeek(new Date()),
    label: 'This Week',
  },
  {
    type: 'monthly',
    startDate: startOfMonth(new Date()),
    endDate: endOfMonth(new Date()),
    label: 'This Month',
  },
  {
    type: 'quarterly',
    startDate: startOfQuarter(new Date()),
    endDate: endOfQuarter(new Date()),
    label: 'This Quarter',
  },
];

// Helper function to get period dates
export const getPeriodDates = (type: FilterPeriod['type']): { startDate: Date; endDate: Date } => {
  const now = new Date();
  
  switch (type) {
    case 'daily':
      return {
        startDate: startOfDay(now),
        endDate: endOfDay(now),
      };
    case 'weekly':
      return {
        startDate: startOfWeek(now),
        endDate: endOfWeek(now),
      };
    case 'monthly':
      return {
        startDate: startOfMonth(now),
        endDate: endOfMonth(now),
      };
    case 'quarterly':
      return {
        startDate: startOfQuarter(now),
        endDate: endOfQuarter(now),
      };
    default:
      return {
        startDate: startOfDay(now),
        endDate: endOfDay(now),
      };
  }
};

// Default filters
const defaultFilters: AdvancedFilters = {
  period: PERIOD_OPTIONS[0], // Today
  productIds: [],
  categoryIds: [],
  agentIds: [],
  customFilters: {},
};

// Helper function to ensure dates are Date objects
const ensureDateObject = (date: any): Date | undefined => {
  if (!date) return undefined;
  if (date instanceof Date) return date;
  if (typeof date === 'string') {
    const parsed = new Date(date);
    return isNaN(parsed.getTime()) ? undefined : parsed;
  }
  return undefined;
};

// Helper function to normalize filter period dates
const normalizeFilterPeriod = (period: FilterPeriod): FilterPeriod => {
  return {
    ...period,
    startDate: ensureDateObject(period.startDate),
    endDate: ensureDateObject(period.endDate),
  };
};

export const useFilterStore = create<FilterState>()(
  persist(
    (set, get) => ({
      filters: defaultFilters,
      autoRefresh: false,
      refreshInterval: 30, // 30 seconds
      lastRefresh: null,
      isLoading: false,

      setPeriod: (period: FilterPeriod) => {
        set((state) => ({
          filters: {
            ...state.filters,
            period,
          },
        }));
        
        // Trigger data refresh
        get().triggerRefresh();
      },

      setCustomDateRange: (startDate: Date, endDate: Date) => {
        const customPeriod: FilterPeriod = {
          type: 'custom',
          startDate,
          endDate,
          label: `${startDate.toLocaleDateString()} - ${endDate.toLocaleDateString()}`,
        };
        
        set((state) => ({
          filters: {
            ...state.filters,
            period: customPeriod,
          },
        }));
        
        get().triggerRefresh();
      },

      setProductFilters: (productIds: string[]) => {
        set((state) => ({
          filters: {
            ...state.filters,
            productIds,
          },
        }));
        
        get().triggerRefresh();
      },

      setCategoryFilters: (categoryIds: string[]) => {
        set((state) => ({
          filters: {
            ...state.filters,
            categoryIds,
          },
        }));
        
        get().triggerRefresh();
      },

      setAgentFilters: (agentIds: string[]) => {
        set((state) => ({
          filters: {
            ...state.filters,
            agentIds,
          },
        }));
        
        get().triggerRefresh();
      },

      setCustomFilter: (key: string, value: any) => {
        set((state) => ({
          filters: {
            ...state.filters,
            customFilters: {
              ...state.filters.customFilters,
              [key]: value,
            },
          },
        }));
        
        get().triggerRefresh();
      },

      clearFilters: () => {
        set({
          filters: {
            ...defaultFilters,
            period: get().filters.period, // Keep current period
          },
        });
        
        get().triggerRefresh();
      },

      resetToDefaults: () => {
        set({ filters: defaultFilters });
        get().triggerRefresh();
      },

      setAutoRefresh: (enabled: boolean, interval?: number) => {
        set({
          autoRefresh: enabled,
          refreshInterval: interval || get().refreshInterval,
        });
      },

      triggerRefresh: () => {
        set({ lastRefresh: new Date() });
        
        // Emit filter change event for components to listen
        window.dispatchEvent(new CustomEvent('filtersChanged', {
          detail: { filters: get().filters }
        }));
      },

      setLoading: (loading: boolean) => {
        set({ isLoading: loading });
      },
    }),
    {
      name: 'filter-storage',
      partialize: (state) => ({
        filters: state.filters,
        autoRefresh: state.autoRefresh,
        refreshInterval: state.refreshInterval,
      }),
      onRehydrateStorage: () => (state) => {
        if (state?.filters?.period) {
          // Ensure dates are properly converted back to Date objects
          state.filters.period = normalizeFilterPeriod(state.filters.period);
        }
      },
    }
  )
);

// Hook for easier filter management
export const useAdvancedFilters = () => {
  const store = useFilterStore();

  // Ensure filters are properly normalized
  const normalizedFilters = {
    ...store.filters,
    period: normalizeFilterPeriod(store.filters.period),
  };

  return {
    ...store,
    filters: normalizedFilters,
    hasActiveFilters: () => {
      const { filters } = store;
      return (
        filters.productIds.length > 0 ||
        filters.categoryIds.length > 0 ||
        filters.agentIds.length > 0 ||
        Object.keys(filters.customFilters).length > 0
      );
    },
    getFilterSummary: () => {
      const { filters } = store;
      const summary = [];
      
      if (filters.productIds.length > 0) {
        summary.push(`${filters.productIds.length} product(s)`);
      }
      if (filters.categoryIds.length > 0) {
        summary.push(`${filters.categoryIds.length} category(s)`);
      }
      if (filters.agentIds.length > 0) {
        summary.push(`${filters.agentIds.length} agent(s)`);
      }
      
      return summary.join(', ');
    },
  };
};

// Utility functions
export const formatPeriodLabel = (period: FilterPeriod): string => {
  if (period.type === 'custom' && period.startDate && period.endDate) {
    return `${period.startDate.toLocaleDateString()} - ${period.endDate.toLocaleDateString()}`;
  }
  return period.label;
};

export const isPeriodActive = (period: FilterPeriod, currentPeriod: FilterPeriod): boolean => {
  return period.type === currentPeriod.type;
};