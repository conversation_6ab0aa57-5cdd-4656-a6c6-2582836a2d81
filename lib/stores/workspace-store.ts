import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { WorkspaceContext, WorkspaceUser } from '@/lib/types/dashboard';
import { getProducts } from '@/lib/api/products';
import { Product } from '@/lib/api/types';

interface WorkspaceState {
  // Current workspace context
  currentWorkspace: WorkspaceContext | null;
  
  // Available workspaces for the current user
  availableWorkspaces: WorkspaceContext[];
  
  // User workspace permissions
  userPermissions: string[];
  
  // Loading states
  isLoading: boolean;
  isInitialized: boolean;
  
  // Actions
  setCurrentWorkspace: (workspace: WorkspaceContext) => void;
  setAvailableWorkspaces: (workspaces: WorkspaceContext[]) => void;
  switchWorkspace: (workspaceId: string) => Promise<boolean>;
  initializeWorkspaces: (userId: string) => Promise<void>;
  hasWorkspacePermission: (permission: string) => boolean;
  getWorkspaceById: (workspaceId: string) => WorkspaceContext | undefined;
  clearWorkspaceData: () => void;
}

// Convert products to workspace contexts
const convertProductsToWorkspaces = (products: Product[]): WorkspaceContext[] => {
  return products.map(product => ({
    id: `ws-${product.id}`,
    name: product.name,
    productId: product.id,
    productName: product.name,
    permissions: [
      'ticket:read',
      'ticket:create',
      'faq:view',
      'call:handle',
      'contact:read',
      'report:view'
    ],
    isActive: true,
  }));
};

// Fetch workspaces from products API
const fetchWorkspacesFromProducts = async (): Promise<WorkspaceContext[]> => {
  try {
    console.log('Fetching products from API...');
    
    const response = await getProducts();
    console.log('Products API response:', response);
    
    if (response.success && Array.isArray(response.data)) {
      const workspaces = convertProductsToWorkspaces(response.data);
      console.log('Converted workspaces:', workspaces);
      
      if (workspaces.length > 0) {
        return workspaces;
      }
    }
    
    console.warn('No products data available, using fallback workspaces');
    return getFallbackWorkspaces();
  } catch (error) {
    console.error('Failed to fetch products for workspaces:', error);
    throw error; // Let the caller handle the error
  }
};

// Get fallback workspaces when API is not available
const getFallbackWorkspaces = (): WorkspaceContext[] => {
  const fallbackWorkspaces = [
    {
      id: 'ws-internet-fiber',
      name: 'Internet Fiber',
      productId: 'internet-fiber',
      productName: 'Internet Fiber',
      permissions: ['ticket:read', 'ticket:create', 'faq:view', 'call:handle', 'contact:read', 'report:view'],
      isActive: true,
    },
    {
      id: 'ws-mobile-data',
      name: 'Mobile Data',
      productId: 'mobile-data',
      productName: 'Mobile Data Plan',
      permissions: ['ticket:read', 'ticket:create', 'faq:view', 'call:handle', 'contact:read'],
      isActive: true,
    },
    {
      id: 'ws-business-voip',
      name: 'Business VoIP',
      productId: 'business-voip',
      productName: 'Business VoIP',
      permissions: ['ticket:read', 'ticket:create', 'faq:view', 'call:handle', 'contact:read', 'report:view'],
      isActive: true,
    },
    {
      id: 'ws-cloud-storage',
      name: 'Cloud Storage',
      productId: 'cloud-storage',
      productName: 'Cloud Storage',
      permissions: ['ticket:read', 'ticket:create', 'faq:view', 'contact:read'],
      isActive: true,
    }
  ];
  console.log('Returning fallback workspaces:', fallbackWorkspaces);
  return fallbackWorkspaces;
};

export const useWorkspaceStore = create<WorkspaceState>()(
  persist(
    (set, get) => ({
      currentWorkspace: null,
      availableWorkspaces: [],
      userPermissions: [],
      isLoading: false,
      isInitialized: false,

      setCurrentWorkspace: (workspace: WorkspaceContext) => {
        set({
          currentWorkspace: workspace,
          userPermissions: workspace.permissions,
        });
        
        // Emit workspace change event for other components to listen
        window.dispatchEvent(new CustomEvent('workspaceChanged', {
          detail: { workspace }
        }));
      },

      setAvailableWorkspaces: (workspaces: WorkspaceContext[]) => {
        set({ availableWorkspaces: workspaces });
      },

      switchWorkspace: async (workspaceId: string): Promise<boolean> => {
        const { availableWorkspaces, setCurrentWorkspace } = get();
        const workspace = availableWorkspaces.find(ws => ws.id === workspaceId);
        
        if (!workspace) {
          console.error(`Workspace with ID ${workspaceId} not found`);
          return false;
        }

        if (!workspace.isActive) {
          console.error(`Workspace ${workspace.name} is not active`);
          return false;
        }

        try {
          set({ isLoading: true });
          
          // In a real app, you might need to make an API call here
          // to switch workspace context on the server
          await new Promise(resolve => setTimeout(resolve, 500)); // Simulate API call
          
          setCurrentWorkspace(workspace);
          
          console.log(`Switched to workspace: ${workspace.name}`);
          return true;
        } catch (error) {
          console.error('Failed to switch workspace:', error);
          return false;
        } finally {
          set({ isLoading: false });
        }
      },

      initializeWorkspaces: async (userId: string): Promise<void> => {
        try {
          set({ isLoading: true });
          
          // Fetch workspaces from products API
          const userWorkspaces = await fetchWorkspacesFromProducts();
          
          set({
            availableWorkspaces: userWorkspaces,
            isInitialized: true,
          });
          
          // Set default workspace if none is selected
          const { currentWorkspace } = get();
          if (!currentWorkspace && userWorkspaces.length > 0) {
            get().setCurrentWorkspace(userWorkspaces[0]);
          }
          
        } catch (error) {
          console.error('Failed to initialize workspaces:', error);
          // Set fallback workspace on error
          const fallbackWorkspace = {
            id: 'ws-fallback',
            name: 'Default Workspace',
            productId: 'default',
            productName: 'Default Workspace',
            permissions: ['ticket:read', 'ticket:create', 'faq:view', 'call:handle'],
            isActive: true,
          };
          set({
            availableWorkspaces: [fallbackWorkspace],
            isInitialized: true,
          });
          get().setCurrentWorkspace(fallbackWorkspace);
        } finally {
          set({ isLoading: false });
        }
      },

      hasWorkspacePermission: (permission: string): boolean => {
        const { userPermissions } = get();
        return userPermissions.includes(permission);
      },

      getWorkspaceById: (workspaceId: string): WorkspaceContext | undefined => {
        const { availableWorkspaces } = get();
        return availableWorkspaces.find(ws => ws.id === workspaceId);
      },

      clearWorkspaceData: () => {
        set({
          currentWorkspace: null,
          availableWorkspaces: [],
          userPermissions: [],
          isLoading: false,
          isInitialized: false,
        });
      },
    }),
    {
      name: 'workspace-storage',
      partialize: (state) => ({
        currentWorkspace: state.currentWorkspace,
        availableWorkspaces: state.availableWorkspaces,
        isInitialized: state.isInitialized,
      }),
    }
  )
);

// Hook for workspace management
export const useWorkspace = () => {
  const store = useWorkspaceStore();
  
  return {
    ...store,
    isWorkspaceReady: store.isInitialized && store.currentWorkspace !== null,
  };
};

// Utility functions
export const getWorkspacePermissions = (workspaceId: string): string[] => {
  const workspace = useWorkspaceStore.getState().getWorkspaceById(workspaceId);
  return workspace?.permissions || [];
};

export const canAccessWorkspace = (workspaceId: string, userRole: string): boolean => {
  const workspace = useWorkspaceStore.getState().getWorkspaceById(workspaceId);
  if (!workspace || !workspace.isActive) return false;
  
  // Add role-based access logic here
  // For now, allow access to all active workspaces
  return true;
};