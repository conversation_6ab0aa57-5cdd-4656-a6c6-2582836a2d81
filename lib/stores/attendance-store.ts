import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { AttendanceRecord, AttendanceMetrics, BreakRecord } from '@/lib/types/dashboard';

interface AttendanceState {
  // Current attendance data
  attendanceRecords: AttendanceRecord[];
  currentUserAttendance: AttendanceRecord | null;
  attendanceMetrics: AttendanceMetrics;
  
  // Loading states
  isLoading: boolean;
  
  // Actions
  clockIn: (agentId: string, agentName: string) => Promise<boolean>;
  clockOut: (agentId: string, reason?: string) => Promise<boolean>;
  startBreak: (agentId: string, type: BreakRecord['type'], reason?: string) => Promise<boolean>;
  endBreak: (agentId: string) => Promise<boolean>;
  getAttendanceByAgent: (agentId: string) => AttendanceRecord | null;
  getAttendanceByDate: (date: string) => AttendanceRecord[];
  updateAttendanceMetrics: () => void;
  setLoading: (loading: boolean) => void;
}

// Mock data for development
const mockAttendanceRecords: AttendanceRecord[] = [
  {
    id: '1',
    agentId: '1',
    agentName: '<PERSON>',
    date: new Date().toISOString().split('T')[0],
    loginTime: new Date(new Date().setHours(8, 30, 0, 0)),
    logoutTime: undefined,
    breaks: [
      {
        id: 'break-1',
        type: 'tea',
        startTime: new Date(new Date().setHours(10, 15, 0, 0)),
        endTime: new Date(new Date().setHours(10, 30, 0, 0)),
        duration: 15,
        reason: 'Morning tea break',
      },
    ],
    totalWorkTime: 480, // 8 hours in minutes
    totalBreakTime: 15,
    productivity: 95,
    status: 'active',
    shift: {
      scheduled: {
        start: new Date(new Date().setHours(8, 0, 0, 0)),
        end: new Date(new Date().setHours(17, 0, 0, 0)),
      },
      actual: {
        start: new Date(new Date().setHours(8, 30, 0, 0)),
        end: undefined,
      },
    },
  },
  {
    id: '2',
    agentId: '2',
    agentName: 'Michael Chen',
    date: new Date().toISOString().split('T')[0],
    loginTime: new Date(new Date().setHours(8, 45, 0, 0)),
    logoutTime: undefined,
    breaks: [
      {
        id: 'break-2',
        type: 'tea',
        startTime: new Date(new Date().setHours(10, 30, 0, 0)),
        endTime: new Date(new Date().setHours(10, 45, 0, 0)),
        duration: 15,
      },
      {
        id: 'break-3',
        type: 'lunch',
        startTime: new Date(new Date().setHours(12, 30, 0, 0)),
        endTime: new Date(new Date().setHours(13, 30, 0, 0)),
        duration: 60,
      },
    ],
    totalWorkTime: 465,
    totalBreakTime: 75,
    productivity: 88,
    status: 'active',
    shift: {
      scheduled: {
        start: new Date(new Date().setHours(8, 0, 0, 0)),
        end: new Date(new Date().setHours(17, 0, 0, 0)),
      },
      actual: {
        start: new Date(new Date().setHours(8, 45, 0, 0)),
        end: undefined,
      },
    },
  },
  {
    id: '3',
    agentId: '3',
    agentName: 'Aisha Patel',
    date: new Date().toISOString().split('T')[0],
    loginTime: new Date(new Date().setHours(9, 0, 0, 0)),
    logoutTime: undefined,
    breaks: [
      {
        id: 'break-4',
        type: 'other',
        startTime: new Date(new Date().setHours(14, 0, 0, 0)),
        endTime: undefined,
        reason: 'Personal call',
      },
    ],
    totalWorkTime: 420,
    totalBreakTime: 30,
    productivity: 92,
    status: 'on-break',
    shift: {
      scheduled: {
        start: new Date(new Date().setHours(9, 0, 0, 0)),
        end: new Date(new Date().setHours(18, 0, 0, 0)),
      },
      actual: {
        start: new Date(new Date().setHours(9, 0, 0, 0)),
        end: undefined,
      },
    },
  },
];

const calculateMetrics = (records: AttendanceRecord[]): AttendanceMetrics => {
  const totalAgents = records.length;
  const activeAgents = records.filter(r => r.status === 'active').length;
  const onBreakAgents = records.filter(r => r.status === 'on-break').length;
  const offlineAgents = records.filter(r => r.status === 'offline').length;
  
  const averageWorkTime = records.reduce((sum, r) => sum + r.totalWorkTime, 0) / totalAgents;
  const averageBreakTime = records.reduce((sum, r) => sum + r.totalBreakTime, 0) / totalAgents;
  const productivityScore = records.reduce((sum, r) => sum + r.productivity, 0) / totalAgents;
  const attendanceRate = (activeAgents + onBreakAgents) / totalAgents * 100;

  return {
    totalAgents,
    activeAgents,
    onBreakAgents,
    offlineAgents,
    averageWorkTime,
    averageBreakTime,
    productivityScore,
    attendanceRate,
  };
};

export const useAttendanceStore = create<AttendanceState>()(
  persist(
    (set, get) => ({
      attendanceRecords: mockAttendanceRecords,
      currentUserAttendance: null,
      attendanceMetrics: calculateMetrics(mockAttendanceRecords),
      isLoading: false,

      clockIn: async (agentId: string, agentName: string): Promise<boolean> => {
        try {
          set({ isLoading: true });
          
          // Simulate API call
          await new Promise(resolve => setTimeout(resolve, 1000));
          
          const now = new Date();
          const today = now.toISOString().split('T')[0];
          
          const newRecord: AttendanceRecord = {
            id: `attendance-${Date.now()}`,
            agentId,
            agentName,
            date: today,
            loginTime: now,
            logoutTime: undefined,
            breaks: [],
            totalWorkTime: 0,
            totalBreakTime: 0,
            productivity: 100,
            status: 'active',
            shift: {
              scheduled: {
                start: new Date(now.setHours(8, 0, 0, 0)),
                end: new Date(now.setHours(17, 0, 0, 0)),
              },
              actual: {
                start: now,
                end: undefined,
              },
            },
          };

          set(state => {
            const updatedRecords = [...state.attendanceRecords, newRecord];
            return {
              attendanceRecords: updatedRecords,
              currentUserAttendance: newRecord,
              attendanceMetrics: calculateMetrics(updatedRecords),
            };
          });

          return true;
        } catch (error) {
          console.error('Failed to clock in:', error);
          return false;
        } finally {
          set({ isLoading: false });
        }
      },

      clockOut: async (agentId: string, reason?: string): Promise<boolean> => {
        try {
          set({ isLoading: true });
          
          // Simulate API call
          await new Promise(resolve => setTimeout(resolve, 1000));
          
          const now = new Date();
          
          set(state => {
            const updatedRecords = state.attendanceRecords.map(record => {
              if (record.agentId === agentId && !record.logoutTime) {
                const workTime = Math.floor((now.getTime() - record.loginTime.getTime()) / (1000 * 60));
                return {
                  ...record,
                  logoutTime: now,
                  totalWorkTime: workTime - record.totalBreakTime,
                  status: 'offline' as const,
                  shift: {
                    ...record.shift,
                    actual: {
                      ...record.shift.actual,
                      end: now,
                    },
                  },
                };
              }
              return record;
            });

            return {
              attendanceRecords: updatedRecords,
              currentUserAttendance: null,
              attendanceMetrics: calculateMetrics(updatedRecords),
            };
          });

          return true;
        } catch (error) {
          console.error('Failed to clock out:', error);
          return false;
        } finally {
          set({ isLoading: false });
        }
      },

      startBreak: async (agentId: string, type: BreakRecord['type'], reason?: string): Promise<boolean> => {
        try {
          set({ isLoading: true });
          
          // Simulate API call
          await new Promise(resolve => setTimeout(resolve, 500));
          
          const now = new Date();
          const breakRecord: BreakRecord = {
            id: `break-${Date.now()}`,
            type,
            startTime: now,
            endTime: undefined,
            duration: undefined,
            reason,
          };

          set(state => {
            const updatedRecords = state.attendanceRecords.map(record => {
              if (record.agentId === agentId && record.status === 'active') {
                return {
                  ...record,
                  breaks: [...record.breaks, breakRecord],
                  status: 'on-break' as const,
                };
              }
              return record;
            });

            return {
              attendanceRecords: updatedRecords,
              attendanceMetrics: calculateMetrics(updatedRecords),
            };
          });

          return true;
        } catch (error) {
          console.error('Failed to start break:', error);
          return false;
        } finally {
          set({ isLoading: false });
        }
      },

      endBreak: async (agentId: string): Promise<boolean> => {
        try {
          set({ isLoading: true });
          
          // Simulate API call
          await new Promise(resolve => setTimeout(resolve, 500));
          
          const now = new Date();

          set(state => {
            const updatedRecords = state.attendanceRecords.map(record => {
              if (record.agentId === agentId && record.status === 'on-break') {
                const updatedBreaks = record.breaks.map(breakRecord => {
                  if (!breakRecord.endTime) {
                    const duration = Math.floor((now.getTime() - breakRecord.startTime.getTime()) / (1000 * 60));
                    return {
                      ...breakRecord,
                      endTime: now,
                      duration,
                    };
                  }
                  return breakRecord;
                });

                const totalBreakTime = updatedBreaks.reduce((sum, b) => sum + (b.duration || 0), 0);

                return {
                  ...record,
                  breaks: updatedBreaks,
                  totalBreakTime,
                  status: 'active' as const,
                };
              }
              return record;
            });

            return {
              attendanceRecords: updatedRecords,
              attendanceMetrics: calculateMetrics(updatedRecords),
            };
          });

          return true;
        } catch (error) {
          console.error('Failed to end break:', error);
          return false;
        } finally {
          set({ isLoading: false });
        }
      },

      getAttendanceByAgent: (agentId: string): AttendanceRecord | null => {
        const { attendanceRecords } = get();
        return attendanceRecords.find(record => record.agentId === agentId) || null;
      },

      getAttendanceByDate: (date: string): AttendanceRecord[] => {
        const { attendanceRecords } = get();
        return attendanceRecords.filter(record => record.date === date);
      },

      updateAttendanceMetrics: () => {
        const { attendanceRecords } = get();
        set({ attendanceMetrics: calculateMetrics(attendanceRecords) });
      },

      setLoading: (loading: boolean) => {
        set({ isLoading: loading });
      },
    }),
    {
      name: 'attendance-storage',
      partialize: (state) => ({
        attendanceRecords: state.attendanceRecords,
        currentUserAttendance: state.currentUserAttendance,
      }),
    }
  )
);

// Hook for easier attendance management
export const useAttendance = () => {
  const store = useAttendanceStore();
  
  return {
    ...store,
    isOnBreak: (agentId: string) => {
      const record = store.getAttendanceByAgent(agentId);
      return record?.status === 'on-break';
    },
    isClockedIn: (agentId: string) => {
      const record = store.getAttendanceByAgent(agentId);
      return record && !record.logoutTime;
    },
    getCurrentBreak: (agentId: string) => {
      const record = store.getAttendanceByAgent(agentId);
      return record?.breaks.find(b => !b.endTime) || null;
    },
    getTotalWorkTime: (agentId: string) => {
      const record = store.getAttendanceByAgent(agentId);
      if (!record) return 0;
      
      const now = new Date();
      const endTime = record.logoutTime || now;
      const totalMinutes = Math.floor((endTime.getTime() - record.loginTime.getTime()) / (1000 * 60));
      return Math.max(0, totalMinutes - record.totalBreakTime);
    },
  };
};