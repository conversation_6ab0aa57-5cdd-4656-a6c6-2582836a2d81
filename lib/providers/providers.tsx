'use client';

import React from 'react';
import { QueryProvider } from './query-provider';
import { AuthProvider } from '../context/auth-context';
import { ErrorProvider } from '@/components/error/ErrorProvider';
import { ProgressProvider } from '@bprogress/next/app';
import { SoundProvider } from '@/components/providers/SoundProvider';
import { GlobalCallNotificationProvider } from '@/components/calls/GlobalCallNotificationProvider';

/**
 * Combined providers for the application
 */
export function Providers({ children }: { children: React.ReactNode }) {
  return (
    <QueryProvider>
      <AuthProvider>
        <ErrorProvider>
          <SoundProvider>
            <GlobalCallNotificationProvider>
              <ProgressProvider
                height="4px"
                color="#3A72EC"
                options={{ showSpinner: false }}
                shallowRouting
              >
                {children}
              </ProgressProvider>
            </GlobalCallNotificationProvider>
          </SoundProvider>
        </ErrorProvider>
      </AuthProvider>
    </QueryProvider>
  );
}
