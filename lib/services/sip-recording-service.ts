/**
 * SIP Recording Service
 * 
 * Handles integration with the SIP server for fetching and managing call recordings.
 * Parses SIP recording filenames and provides URLs for playback.
 */

export interface SipRecording {
  filename: string;
  callId: string;
  timestamp: string;
  callerNumber: string;
  direction: 'in' | 'out';
  url: string;
  sipId: string;
  date: string;
  time: string;
}

export interface ParsedFilename {
  direction: 'in' | 'out';
  callerNumber: string;
  callId: string;
  date: string;
  time: string;
  sipId: string;
  extension: string;
}

class SipRecordingService {
  private baseUrl: string;
  private filesEndpoint: string;
  private answerEndpoint: string;
  private debugMode = false; // Add debug mode flag
  private configLogged = false; // Prevent repeated config logging

  constructor() {
    this.baseUrl = process.env.NEXT_PUBLIC_SIP_RECORDING_API || '/api/sip-recordings';
    this.filesEndpoint = process.env.NEXT_PUBLIC_SIP_FILES_ENDPOINT || '/api/sip-recordings/files';
    this.answerEndpoint = process.env.NEXT_PUBLIC_SIP_ANSWER_ENDPOINT || 'https://7a11-41-203-218-161.ngrok-free.app/api/answer';

    // Log configuration for debugging (only once)
    if (typeof window !== 'undefined' && !this.configLogged && this.debugMode) {
      console.log('📼 SIP Recording Service Configuration:');
      console.log('- Base URL:', this.baseUrl);
      console.log('- Files Endpoint:', this.filesEndpoint);
      console.log('- Using Environment Variables:', !!process.env.NEXT_PUBLIC_SIP_RECORDING_API);
      this.configLogged = true;
    }
  }

  /**
   * Fetch all recording filenames from the SIP server
   */
  async getRecordings(): Promise<string[]> {
    try {
      if (this.debugMode) {
        console.log('📼 SIP: Fetching recordings from:', this.baseUrl);
      }

      // Check if we're using environment variables (only warn once)
      if (!process.env.NEXT_PUBLIC_SIP_RECORDING_API && !this.configLogged && this.debugMode) {
        console.warn('⚠️  SIP Recording API URL not configured. Using mock endpoint.');
        console.warn('   Set NEXT_PUBLIC_SIP_RECORDING_API in your .env.local file');
        this.configLogged = true;
      }

      const response = await fetch(this.baseUrl, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          // Add ngrok-specific header to bypass browser warning
          'ngrok-skip-browser-warning': 'true',
          // Add user agent to avoid bot detection
          'User-Agent': 'CallCenter-App/1.0',
        },
      });

      if (!response.ok) {
        const errorMessage = `SIP API error: ${response.status} ${response.statusText}`;

        // Try to get response text for debugging
        let responseText = '';
        try {
          responseText = await response.text();
          console.error('Response body:', responseText.substring(0, 200) + '...');
        } catch (e) {
          console.error('Could not read response body');
        }

        if (response.status === 404 && !process.env.NEXT_PUBLIC_SIP_RECORDING_API) {
          throw new Error(`${errorMessage}\n\nℹ️  Configure your SIP server URL in .env.local:\nNEXT_PUBLIC_SIP_RECORDING_API="http://your-sip-server/api/recordings"`);
        }

        throw new Error(`${errorMessage}\nResponse: ${responseText.substring(0, 100)}`);
      }

      // Check content type before parsing JSON
      const contentType = response.headers.get('content-type');
      if (!contentType || !contentType.includes('application/json')) {
        const responseText = await response.text();
        if (this.debugMode) {
          console.error('📼 SIP: Non-JSON response received:', responseText.substring(0, 200));
        }
        throw new Error(`Expected JSON response but got: ${contentType}\nResponse: ${responseText.substring(0, 100)}`);
      }

      const recordings = await response.json();
      if (this.debugMode) {
        console.log('📼 SIP: Fetched recordings:', recordings);
      }

      // Ensure we return an array
      return Array.isArray(recordings) ? recordings : [];
    } catch (error) {
      if (this.debugMode) {
        console.error('📼 SIP: Error fetching recordings:', error);
      }
      return [];
    }
  }

  /**
   * Parse SIP recording filename
   * Format: "in-+254709918000-716038129-20250528-134749-1748440069.72.wav"
   */
  parseFilename(filename: string): ParsedFilename | null {
    try {
      // Split by dashes, but be careful with phone numbers that start with +
      const parts = filename.split('-');
      
      if (parts.length < 6) {
        console.warn('Invalid filename format:', filename);
        return null;
      }

      // Handle phone numbers that start with + (they get split)
      let direction = parts[0] as 'in' | 'out';
      let callerNumber = parts[1];
      let callId = parts[2];
      let date = parts[3];
      let time = parts[4];
      let sipIdWithExt = parts[5];

      // If the phone number starts with +, it was split, so rejoin
      if (parts[1] === '' && parts[2].startsWith('+')) {
        callerNumber = parts[2];
        callId = parts[3];
        date = parts[4];
        time = parts[5];
        sipIdWithExt = parts[6];
      }

      // Extract SIP ID and extension
      const lastDotIndex = sipIdWithExt.lastIndexOf('.');
      const sipId = lastDotIndex > 0 ? sipIdWithExt.substring(0, lastDotIndex) : sipIdWithExt;
      const extension = lastDotIndex > 0 ? sipIdWithExt.substring(lastDotIndex + 1) : 'wav';

      return {
        direction,
        callerNumber,
        callId,
        date,
        time,
        sipId,
        extension
      };
    } catch (error) {
      console.error('Error parsing filename:', filename, error);
      return null;
    }
  }

  /**
   * Get the full URL for a recording file
   */
  getRecordingUrl(filename: string): string {
    return `${this.filesEndpoint}/${encodeURIComponent(filename)}`;
  }

  /**
   * Convert parsed filename data to SipRecording object
   */
  createSipRecording(filename: string): SipRecording | null {
    const parsed = this.parseFilename(filename);
    if (!parsed) return null;

    // Create timestamp from date and time
    // Format: 20250528-134749 -> 2025-05-28T13:47:49
    const dateStr = `${parsed.date.substring(0, 4)}-${parsed.date.substring(4, 6)}-${parsed.date.substring(6, 8)}`;
    const timeStr = `${parsed.time.substring(0, 2)}:${parsed.time.substring(2, 4)}:${parsed.time.substring(4, 6)}`;
    const timestamp = `${dateStr}T${timeStr}`;

    return {
      filename,
      callId: parsed.callId,
      timestamp,
      callerNumber: parsed.callerNumber,
      direction: parsed.direction,
      url: this.getRecordingUrl(filename),
      sipId: parsed.sipId,
      date: parsed.date,
      time: parsed.time
    };
  }

  /**
   * Get all recordings as SipRecording objects
   */
  async getAllRecordings(): Promise<SipRecording[]> {
    const filenames = await this.getRecordings();
    return filenames
      .map(filename => this.createSipRecording(filename))
      .filter((recording): recording is SipRecording => recording !== null);
  }

  /**
   * Find recording for a specific call
   */
  async getRecordingForCall(callerNumber: string, callId?: string, timestamp?: string): Promise<SipRecording | null> {
    const recordings = await this.getAllRecordings();
    
    // Clean phone numbers for comparison
    const cleanCallerNumber = callerNumber.replace(/\D/g, '');
    
    return recordings.find(recording => {
      const cleanRecordingNumber = recording.callerNumber.replace(/\D/g, '');
      
      // Match by phone number
      const phoneMatch = cleanRecordingNumber === cleanCallerNumber ||
                        cleanRecordingNumber.includes(cleanCallerNumber) ||
                        cleanCallerNumber.includes(cleanRecordingNumber);
      
      // If we have a call ID, try to match that too
      if (callId && recording.callId) {
        return phoneMatch && recording.callId === callId;
      }
      
      // If we have a timestamp, try to match within a reasonable time window
      if (timestamp && recording.timestamp) {
        const callTime = new Date(timestamp).getTime();
        const recordingTime = new Date(recording.timestamp).getTime();
        const timeDiff = Math.abs(callTime - recordingTime);
        const fiveMinutes = 5 * 60 * 1000; // 5 minutes in milliseconds
        
        return phoneMatch && timeDiff <= fiveMinutes;
      }
      
      return phoneMatch;
    }) || null;
  }

  /**
   * Check if a recording exists for a call
   */
  async hasRecording(callerNumber: string, callId?: string, timestamp?: string): Promise<boolean> {
    const recording = await this.getRecordingForCall(callerNumber, callId, timestamp);
    return recording !== null;
  }

  /**
   * Get recording statistics
   */
  async getRecordingStats(): Promise<{
    total: number;
    inbound: number;
    outbound: number;
    today: number;
  }> {
    const recordings = await this.getAllRecordings();
    const today = new Date().toISOString().substring(0, 10).replace(/-/g, '');

    return {
      total: recordings.length,
      inbound: recordings.filter(r => r.direction === 'in').length,
      outbound: recordings.filter(r => r.direction === 'out').length,
      today: recordings.filter(r => r.date === today).length
    };
  }

  /**
   * Answer a SIP call by sending channel ID to the SIP server
   */
  async answerCall(channelId: string): Promise<{ success: boolean; message?: string }> {
    try {
      const response = await fetch(this.answerEndpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'ngrok-skip-browser-warning': 'true',
          'User-Agent': 'CallCenter-App/1.0',
        },
        body: JSON.stringify({ channelId }),
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`SIP answer failed: ${response.status} ${response.statusText} - ${errorText}`);
      }

      const result = await response.json();
      return { success: true, message: result.message || 'Call answered successfully' };
    } catch (error) {
      console.error('Error answering SIP call:', error);
      return {
        success: false,
        message: error instanceof Error ? error.message : 'Failed to answer call'
      };
    }
  }
}

// Export singleton instance
export const sipRecordingService = new SipRecordingService();

// Export class for testing
export { SipRecordingService };
