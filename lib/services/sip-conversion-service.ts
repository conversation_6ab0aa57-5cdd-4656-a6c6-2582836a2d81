/**
 * SIP Recording Format Conversion Service
 * 
 * This service handles the conversion of SIP call recordings from various formats
 * to web-playable formats. It provides a flexible system that can be extended
 * when actual SIP format samples are available.
 */

export interface ConversionResult {
  success: boolean;
  convertedUrl?: string;
  originalFormat?: string;
  targetFormat?: string;
  error?: string;
  duration?: number;
  fileSize?: number;
}

export interface ConversionOptions {
  targetFormat?: 'mp3' | 'wav' | 'ogg';
  quality?: 'low' | 'medium' | 'high';
  normalize?: boolean;
  removeNoise?: boolean;
}

// Supported SIP audio formats
export const SIP_FORMATS = {
  G711_ULAW: 'g711u',
  G711_ALAW: 'g711a', 
  G729: 'g729',
  G722: 'g722',
  GSM: 'gsm',
  SPEEX: 'speex',
  OPUS: 'opus',
  WAV: 'wav',
  RAW: 'raw'
} as const;

export type SipFormat = typeof SIP_FORMATS[keyof typeof SIP_FORMATS];

// Format detection patterns
const FORMAT_PATTERNS = {
  [SIP_FORMATS.G711_ULAW]: /\.(g711u|ulaw)$/i,
  [SIP_FORMATS.G711_ALAW]: /\.(g711a|alaw)$/i,
  [SIP_FORMATS.G729]: /\.g729$/i,
  [SIP_FORMATS.G722]: /\.g722$/i,
  [SIP_FORMATS.GSM]: /\.gsm$/i,
  [SIP_FORMATS.SPEEX]: /\.spx$/i,
  [SIP_FORMATS.OPUS]: /\.opus$/i,
  [SIP_FORMATS.WAV]: /\.wav$/i,
  [SIP_FORMATS.RAW]: /\.(raw|pcm)$/i,
};

class SipConversionService {
  private conversionEndpoint: string;
  private apiKey: string | null;

  constructor() {
    this.conversionEndpoint = process.env.NEXT_PUBLIC_SIP_CONVERSION_API || '/api/convert-recording';
    this.apiKey = process.env.NEXT_PUBLIC_SIP_CONVERSION_API_KEY || null;
  }

  /**
   * Detect the format of a SIP recording based on URL or file extension
   */
  detectFormat(url: string): SipFormat | null {
    for (const [format, pattern] of Object.entries(FORMAT_PATTERNS)) {
      if (pattern.test(url)) {
        return format as SipFormat;
      }
    }
    return null;
  }

  /**
   * Check if a format is web-playable without conversion
   */
  isWebPlayable(format: SipFormat | null): boolean {
    if (!format) return false;
    return [SIP_FORMATS.WAV, SIP_FORMATS.OPUS].includes(format);
  }

  /**
   * Convert SIP recording to web-playable format
   */
  async convertRecording(
    url: string, 
    options: ConversionOptions = {}
  ): Promise<ConversionResult> {
    try {
      const detectedFormat = this.detectFormat(url);
      
      // If already web-playable, return as-is
      if (this.isWebPlayable(detectedFormat)) {
        return {
          success: true,
          convertedUrl: url,
          originalFormat: detectedFormat || 'unknown',
          targetFormat: detectedFormat === SIP_FORMATS.WAV ? 'wav' : 'opus'
        };
      }

      // For development/demo purposes, simulate conversion
      if (process.env.NODE_ENV === 'development') {
        return this.simulateConversion(url, detectedFormat, options);
      }

      // Production conversion via API
      return this.performActualConversion(url, detectedFormat, options);

    } catch (error) {
      console.error('SIP conversion error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown conversion error'
      };
    }
  }

  /**
   * Simulate conversion for development/demo purposes
   */
  private async simulateConversion(
    url: string, 
    originalFormat: SipFormat | null,
    options: ConversionOptions
  ): Promise<ConversionResult> {
    // Simulate processing delay
    await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 2000));

    // For demo, we'll assume all recordings can be "converted" to MP3
    const targetFormat = options.targetFormat || 'mp3';
    
    // In a real scenario, this would be the URL of the converted file
    // For demo, we'll use a sample audio file or the original URL
    const convertedUrl = this.getMockConvertedUrl(url, targetFormat);

    return {
      success: true,
      convertedUrl,
      originalFormat: originalFormat || 'unknown',
      targetFormat,
      duration: 120 + Math.random() * 300, // Mock duration
      fileSize: 1024 * 1024 * (2 + Math.random() * 5) // Mock file size
    };
  }

  /**
   * Perform actual conversion via backend API
   */
  private async performActualConversion(
    url: string,
    originalFormat: SipFormat | null,
    options: ConversionOptions
  ): Promise<ConversionResult> {
    const requestBody = {
      sourceUrl: url,
      originalFormat,
      targetFormat: options.targetFormat || 'mp3',
      quality: options.quality || 'medium',
      normalize: options.normalize || false,
      removeNoise: options.removeNoise || false
    };

    const headers: Record<string, string> = {
      'Content-Type': 'application/json'
    };

    if (this.apiKey) {
      headers['Authorization'] = `Bearer ${this.apiKey}`;
    }

    const response = await fetch(this.conversionEndpoint, {
      method: 'POST',
      headers,
      body: JSON.stringify(requestBody)
    });

    if (!response.ok) {
      throw new Error(`Conversion API error: ${response.status} ${response.statusText}`);
    }

    const result = await response.json();
    return result;
  }

  /**
   * Get a mock converted URL for demo purposes
   */
  private getMockConvertedUrl(originalUrl: string, targetFormat: string): string {
    // In development, we can use sample audio files or return the original URL
    // This would be replaced with actual converted file URLs in production

    // For demo purposes, use a publicly available sample audio file
    // You can replace this with your own sample files in the public/audio directory
    const sampleAudioFiles = [
      'https://www.soundjay.com/misc/sounds/bell-ringing-05.wav',
      'https://file-examples.com/storage/fe68c1b7c1a9d6b/2017/11/file_example_MP3_700KB.mp3',
      originalUrl // Fallback to original URL
    ];

    // Return a random sample file for demo
    const randomSample = sampleAudioFiles[Math.floor(Math.random() * sampleAudioFiles.length)];

    // For demo, return the sample file
    return randomSample;
  }

  /**
   * Get conversion status for a job (for async conversions)
   */
  async getConversionStatus(jobId: string): Promise<ConversionResult> {
    try {
      const response = await fetch(`${this.conversionEndpoint}/status/${jobId}`, {
        headers: this.apiKey ? { 'Authorization': `Bearer ${this.apiKey}` } : {}
      });

      if (!response.ok) {
        throw new Error(`Status check failed: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Status check failed'
      };
    }
  }

  /**
   * Get supported formats and their capabilities
   */
  getSupportedFormats(): Record<SipFormat, { canConvert: boolean; description: string }> {
    return {
      [SIP_FORMATS.G711_ULAW]: {
        canConvert: true,
        description: 'G.711 μ-law (PCMU) - Common in North America'
      },
      [SIP_FORMATS.G711_ALAW]: {
        canConvert: true,
        description: 'G.711 A-law (PCMA) - Common in Europe'
      },
      [SIP_FORMATS.G729]: {
        canConvert: true,
        description: 'G.729 - Low bitrate codec'
      },
      [SIP_FORMATS.G722]: {
        canConvert: true,
        description: 'G.722 - Wideband audio codec'
      },
      [SIP_FORMATS.GSM]: {
        canConvert: true,
        description: 'GSM 06.10 - Mobile codec'
      },
      [SIP_FORMATS.SPEEX]: {
        canConvert: true,
        description: 'Speex - Open source codec'
      },
      [SIP_FORMATS.OPUS]: {
        canConvert: false,
        description: 'Opus - Modern codec (web-playable)'
      },
      [SIP_FORMATS.WAV]: {
        canConvert: false,
        description: 'WAV - Uncompressed audio (web-playable)'
      },
      [SIP_FORMATS.RAW]: {
        canConvert: true,
        description: 'Raw PCM audio data'
      }
    };
  }
}

// Export singleton instance
export const sipConversionService = new SipConversionService();

// Export utility functions
export { SipConversionService };
