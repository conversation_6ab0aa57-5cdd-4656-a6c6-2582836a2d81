import { io, Socket } from 'socket.io-client';
import { create } from 'zustand';

// Define call types
export interface IncomingCall {
  id: string;
  callerNumber: string;
  callerName: string;
  channelNumber?: string; // For simulated calls
  destinationNumber?: string; // For real SIP server calls
  channelId?: string; // SIP channel ID for answering calls
  callId?: string; // SIP call ID
  timestamp?: string;
  startTime?: string; // Real SIP server uses startTime instead of timestamp
  status: 'ringing' | 'answered' | 'missed' | 'ended';
  duration?: number;
  recordingUrl?: string;
  productName?: string;
  callerId?: string;
  direction?: string; // Real SIP server provides direction
  agentExtension?: string; // Real SIP server provides agent extension
}

export interface CallLog extends IncomingCall {
  agentId?: string;
  notes?: string;
  callId?: string;
}

// SIP server event interfaces
export interface SipCallBridgedEvent {
  callId: string;
  caller: string;
}

export interface SipCallEndedEvent {
  callId: string;
  caller?: string;
  duration?: number;
}

// Socket events
export enum SocketEvent {
  CONNECT = 'connect',
  DISCONNECT = 'disconnect',
  INCOMING_CALL = 'incoming_call',
  OUTBOUND_CALL = 'outbound_call',
  ANSWER_CALL = 'answer_call',
  REJECT_CALL = 'reject_call',
  END_CALL = 'end_call',
  CALL_ANSWERED = 'call_answered',
  CALL_REJECTED = 'call_rejected',
  CALL_ENDED = 'call_ended',
  // SIP server specific events
  CALL_BRIDGED = 'call_bridged',
  SIP_CALL_ENDED = 'sip_call_ended',
  OUTBOUND_CALL_CONNECTED = 'outbound_call_connected',
  OUTBOUND_CALL_FAILED = 'outbound_call_failed'
}

// Socket service class
class SocketService {
  private socket: Socket | null = null;
  private initialized = false;
  private eventHandlers: Record<string, Array<(...args: any[]) => void>> = {};
  private simulationMode = false;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private token: string | null = null;
  private debugMode = false; // Disable debug mode to prevent console spam
  private processedEvents: Set<string> = new Set();

  // Initialize socket connection
  initialize(token: string): void {
    if (this.initialized) return;

    this.token = token;
    const socketUrl = process.env.NEXT_PUBLIC_SIP_WS_URL || 'http://localhost:8080';

    try {
      if (this.debugMode) {
        console.log('🔌 Socket: Attempting to connect to:', socketUrl);
      }

      // Close existing socket if any
      if (this.socket) {
        this.socket.close();
        this.socket = null;
      }

      // Create new Socket.IO connection
      this.socket = io(socketUrl, {
        transports: ['websocket'],
        reconnection: true,
        reconnectionAttempts: this.maxReconnectAttempts,
        reconnectionDelay: 1000,
        reconnectionDelayMax: 5000,
        timeout: 20000,
        auth: token ? { token } : undefined
      });

      this.socket.on('connect', () => {
        if (this.debugMode) {
          console.log('🔌 Socket: Connected to server');
        }
        this.initialized = true;
        this.reconnectAttempts = 0;
        this.triggerEvent(SocketEvent.CONNECT);
      });

      this.socket.on('disconnect', (reason) => {
        if (this.debugMode) {
          console.log('🔌 Socket: Disconnected, reason:', reason);
        }
        this.initialized = false;
        this.triggerEvent(SocketEvent.DISCONNECT);
      });

      this.socket.on('connect_error', (error) => {
        if (this.debugMode) {
          console.error('🔌 Socket: Connection error:', error);
        }
      });

      // Set up event handlers for call-related events
      Object.values(SocketEvent).forEach(event => {
        if (event !== SocketEvent.CONNECT && event !== SocketEvent.DISCONNECT) {
          this.socket!.on(event, (data: any) => {
            if (this.debugMode) {
              console.log(`🔌 Socket: Received ${event} event:`, data);
            }
            this.triggerEvent(event, data);
          });
        }
      });

      // Add specific listeners for common SIP server event names
      const sipEventNames = [
        'call_bridged', 'callBridged', 'call-bridged',
        'call_connected', 'callConnected', 'call-connected',
        'agent_answered', 'agentAnswered', 'agent-answered',
        'bridge_success', 'bridgeSuccess', 'bridge-success'
      ];

      // Track processed events to prevent duplicates (use class property for persistence)
      if (!this.processedEvents) {
        this.processedEvents = new Set<string>();
      }

      sipEventNames.forEach(eventName => {
        this.socket!.on(eventName, (data: any) => {
          const eventKey = `${eventName}-${data?.callId || data?.caller || 'unknown'}-${Date.now()}`;
          const baseEventKey = `${eventName}-${data?.callId || data?.caller || 'unknown'}`;

          // Check if we've processed this event recently (within 1 second)
          const recentEvents = Array.from(this.processedEvents).filter((key: string) =>
            key.startsWith(baseEventKey) &&
            (Date.now() - parseInt(key.split('-').pop() || '0')) < 1000
          );

          if (recentEvents.length > 0) {
            console.log(`🔄 Socket: Ignoring duplicate SIP event '${eventName}' for call ${data?.callId || data?.caller}`);
            return;
          }

          this.processedEvents.add(eventKey);
          console.log(`🌉 Socket: Received SIP event '${eventName}':`, data);

          // Clean up old events (keep only last 20 and remove events older than 5 seconds)
          const now = Date.now();
          const eventsToDelete = Array.from(this.processedEvents).filter((key: string) => {
            const timestamp = parseInt(key.split('-').pop() || '0');
            return (now - timestamp) > 5000;
          });

          eventsToDelete.forEach((key: string) => this.processedEvents.delete(key));

          if (this.processedEvents.size > 20) {
            const oldestEvents = Array.from(this.processedEvents).slice(0, this.processedEvents.size - 20);
            oldestEvents.forEach((key: string) => this.processedEvents.delete(key));
          }

          // Trigger our standardized call bridged event
          this.triggerEvent(SocketEvent.CALL_BRIDGED, data);
        });
      });

      // Simplified catch-all listener (disabled to prevent duplicates)
      // this.socket!.onAny((eventName: string, data: any) => {
      //   if (!Object.values(SocketEvent).includes(eventName as SocketEvent) &&
      //       !sipEventNames.includes(eventName)) {
      //     console.log(`🔍 Socket: Received unknown event '${eventName}':`, data);
      //   }
      // });

    } catch (error) {
      console.error('Failed to initialize Socket.IO:', error);
      this.handleReconnect();
    }
  }

  private handleReconnect(): void {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.error('Max reconnection attempts reached');
      return;
    }

    this.reconnectAttempts++;
    console.log(`Socket.IO attempting to reconnect (${this.reconnectAttempts}/${this.maxReconnectAttempts})`);
  }

  // Subscribe to events
  on(event: string, callback: (...args: any[]) => void): void {
    if (!this.eventHandlers[event]) {
      this.eventHandlers[event] = [];
    }
    this.eventHandlers[event].push(callback);
  }

  // Unsubscribe from events
  off(event: string): void {
    delete this.eventHandlers[event];
  }

  // Emit events to Socket.IO server
  emit(event: string, data?: any): void {
    if (!this.socket?.connected) {
      if (this.debugMode) {
        console.error('🔌 Socket: Not connected');
      }
      return;
    }

    if (this.debugMode) {
      console.log(`🔌 Socket: Emitting ${event}:`, data);
    }
    this.socket.emit(event, data);
  }

  private triggerEvent(event: string, data?: any): void {
    if (!this.eventHandlers[event]) return;

    this.eventHandlers[event].forEach(handler => {
      try {
        handler(data);
      } catch (error) {
        console.error(`Error in ${event} event handler:`, error);
      }
    });
  }

  // Disconnect socket
  disconnect(): void {
    if (this.socket) {
      // Remove all socket.io event listeners
      this.socket.removeAllListeners();
      this.socket.close();
      this.socket = null;
    }

    this.initialized = false;
    this.eventHandlers = {};
    this.reconnectAttempts = 0;
  }

  // Check if socket is connected
  isConnected(): boolean {
    return this.socket?.connected || false;
  }
}

// Create singleton instance
export const socketService = new SocketService();

// Create store for call state
interface CallState {
  currentCall: IncomingCall | null;
  callHistory: CallLog[];
  isRecording: boolean;
  setCurrentCall: (call: IncomingCall | null) => void;
  addToCallHistory: (call: CallLog) => void;
  updateCurrentCall: (updates: Partial<IncomingCall>) => void;
  setIsRecording: (isRecording: boolean) => void;
  clearCurrentCall: () => void;
}

export const useCallStore = create<CallState>((set) => ({
  currentCall: null,
  callHistory: [],
  isRecording: false,
  setCurrentCall: (call) => set({ currentCall: call }),
  addToCallHistory: (call) => set((state) => ({ 
    callHistory: [call, ...state.callHistory] 
  })),
  updateCurrentCall: (updates) => set((state) => ({
    currentCall: state.currentCall
      ? { ...state.currentCall, ...updates }
      : null
  })),
  setIsRecording: (isRecording) => set({ isRecording }),
  clearCurrentCall: () => set({ currentCall: null })
}));

// Generate a realistic SIP call ID
const generateSipCallId = (): string => {
  // Format: SIP-yyyyMMdd-HHmmss-xxxx where xxxx is a random 4-digit number
  const now = new Date();
  const year = now.getFullYear();
  const month = String(now.getMonth() + 1).padStart(2, '0');
  const day = String(now.getDate()).padStart(2, '0');
  const hours = String(now.getHours()).padStart(2, '0');
  const minutes = String(now.getMinutes()).padStart(2, '0');
  const seconds = String(now.getSeconds()).padStart(2, '0');
  const random = Math.floor(Math.random() * 10000).toString().padStart(4, '0');

  return `SIP-${year}${month}${day}-${hours}${minutes}${seconds}-${random}`;
};

// Mock function to simulate incoming calls (for development)
export const simulateIncomingCall = (callData?: Partial<IncomingCall>) => {
  const callStore = useCallStore.getState();

  // Don't simulate if there's already an active call
  if (callStore.currentCall) return;

  const phoneNumbers = [
    '+254712345678',
    '+254722123456',
    '+254733987654',
    '+254711234567',
    '+254700987654',
  ];

  const contactNames = [
    'Wanjiru Kamau',
    'Ochieng Otieno',
    'Akinyi Odhiambo',
    'Mutua Kimani',
    'Njeri Muthomi',
  ];

  const channelNumbers = [
    '+254800123456',
    '+254800654321',
    '+254800789012',
  ];

  const productNames = [
    'Internet Fiber',
    'Mobile Data Plan',
    'Business VoIP',
    'Cloud Storage',
    'Home Security',
  ];

  const callerIds = [
    'CID12345',
    'CID67890',
    'CID24680',
    'CID13579',
    'CID98765',
  ];

  const randomCallerNumber = phoneNumbers[Math.floor(Math.random() * phoneNumbers.length)];
  const randomCallerName = contactNames[Math.floor(Math.random() * contactNames.length)];
  const randomChannelNumber = channelNumbers[Math.floor(Math.random() * channelNumbers.length)];
  const randomProductName = productNames[Math.floor(Math.random() * productNames.length)];
  const randomCallerId = callerIds[Math.floor(Math.random() * callerIds.length)];

  // Generate a truly unique ID by combining random string with timestamp
  const uniqueId = `${Math.random().toString(36).substring(2, 8)}-${Date.now().toString(36)}`;

  // Generate a SIP call ID for this call
  const sipCallId = generateSipCallId();

  const incomingCall: IncomingCall & { callId?: string } = {
    id: uniqueId,
    callerNumber: callData?.callerNumber || randomCallerNumber,
    callerName: callData?.callerName || randomCallerName,
    channelNumber: callData?.channelNumber || randomChannelNumber,
    timestamp: callData?.timestamp || new Date().toISOString(),
    status: 'ringing',
    productName: callData?.productName || randomProductName,
    callerId: callData?.callerId || randomCallerId,
    callId: sipCallId, // Add the SIP call ID
  };

  // Process the simulated event
  callStore.setCurrentCall(incomingCall);

  // Simulate the socket event by triggering the handlers
  if (socketService.isConnected()) {
    socketService.emit(SocketEvent.INCOMING_CALL, incomingCall);
  }

  // Also dispatch a custom event for components that listen directly
  const event = new CustomEvent('incomingCall', { detail: incomingCall });
  window.dispatchEvent(event);

  console.log('Simulated incoming call:', incomingCall);

  return incomingCall;
};
