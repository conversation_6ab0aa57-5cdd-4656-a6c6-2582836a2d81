/**
 * WebRTC Service for Real-Time SIP Calling
 * 
 * Integrates with JsSIP to provide real WebRTC calling functionality
 * for call center agents using SIP over WebSocket.
 */

import JsSIP from 'jssip';
import { IncomingCall } from './socket-service';

export interface WebRTCConfig {
  enabled: boolean;
  sipServerWss: string;
  sipDomain: string;
  sipProxy: string;
  stunServer: string;
  extensionBase: string;
  sipPassword: string;
  debug: boolean;
  trace: boolean;
}

export interface AgentSipCredentials {
  extension: string;
  password: string;
  displayName: string;
  userId: string;
}

export interface WebRTCCallSession {
  id: string;
  session: any; // JsSIP RTCSession
  direction: 'inbound' | 'outbound';
  remoteNumber: string;
  localNumber: string;
  status: 'ringing' | 'answered' | 'ended' | 'failed';
  startTime: Date;
  endTime?: Date;
  duration?: number;
}

export type WebRTCEventType = 
  | 'registered'
  | 'unregistered' 
  | 'registrationFailed'
  | 'incomingCall'
  | 'callAnswered'
  | 'callEnded'
  | 'callFailed'
  | 'connectionStateChanged';

export interface WebRTCEventData {
  type: WebRTCEventType;
  data?: any;
  session?: WebRTCCallSession;
  error?: string;
}

class WebRTCService {
  private ua: any = null; // JsSIP UserAgent
  private config: WebRTCConfig;
  private currentAgent: AgentSipCredentials | null = null;
  private activeSessions: Map<string, WebRTCCallSession> = new Map();
  private eventListeners: Map<WebRTCEventType, Function[]> = new Map();
  private isRegistered = false;

  constructor() {
    this.config = this.loadConfig();
    this.initializeEventListeners();
  }

  private loadConfig(): WebRTCConfig {
    return {
      enabled: process.env.NEXT_PUBLIC_WEBRTC_ENABLED === 'true',
      sipServerWss: process.env.NEXT_PUBLIC_SIP_SERVER_WSS || '',
      sipDomain: process.env.NEXT_PUBLIC_SIP_DOMAIN || '',
      sipProxy: process.env.NEXT_PUBLIC_SIP_PROXY || '',
      stunServer: process.env.NEXT_PUBLIC_STUN_SERVER || 'stun:stun.l.google.com:19302',
      extensionBase: process.env.NEXT_PUBLIC_SIP_EXTENSION_BASE || '1001',
      sipPassword: process.env.NEXT_PUBLIC_SIP_PASSWORD || '',
      debug: process.env.NEXT_PUBLIC_WEBRTC_DEBUG === 'true',
      trace: process.env.NEXT_PUBLIC_SIP_TRACE === 'true',
    };
  }

  private initializeEventListeners(): void {
    // Initialize event listener arrays
    Object.values(['registered', 'unregistered', 'registrationFailed', 'incomingCall', 'callAnswered', 'callEnded', 'callFailed', 'connectionStateChanged'] as WebRTCEventType[]).forEach(event => {
      this.eventListeners.set(event, []);
    });
  }

  /**
   * Register event listener
   */
  on(event: WebRTCEventType, callback: (data: WebRTCEventData) => void): void {
    const listeners = this.eventListeners.get(event) || [];
    listeners.push(callback);
    this.eventListeners.set(event, listeners);
  }

  /**
   * Remove event listener
   */
  off(event: WebRTCEventType, callback?: (data: WebRTCEventData) => void): void {
    if (!callback) {
      this.eventListeners.set(event, []);
      return;
    }

    const listeners = this.eventListeners.get(event) || [];
    const index = listeners.indexOf(callback);
    if (index > -1) {
      listeners.splice(index, 1);
    }
  }

  /**
   * Emit event to all listeners
   */
  private emit(event: WebRTCEventType, data?: any, session?: WebRTCCallSession, error?: string): void {
    const listeners = this.eventListeners.get(event) || [];
    const eventData: WebRTCEventData = { type: event, data, session, error };
    
    listeners.forEach(callback => {
      try {
        callback(eventData);
      } catch (err) {
        console.error(`Error in WebRTC event listener for ${event}:`, err);
      }
    });
  }

  /**
   * Check if WebRTC is enabled and configured
   */
  isEnabled(): boolean {
    return this.config.enabled && !!this.config.sipServerWss && !!this.config.sipDomain;
  }

  /**
   * Get current configuration
   */
  getConfig(): WebRTCConfig {
    return { ...this.config };
  }

  /**
   * Register agent with SIP server
   */
  async registerAgent(agentCredentials: AgentSipCredentials): Promise<boolean> {
    if (!this.isEnabled()) {
      console.warn('WebRTC is not enabled or configured');
      return false;
    }

    try {
      this.currentAgent = agentCredentials;

      // Configure JsSIP debug
      if (this.config.debug) {
        JsSIP.debug.enable('JsSIP:*');
      }

      // Create SIP URI
      const sipUri = `sip:${agentCredentials.extension}@${this.config.sipDomain}`;

      // WebSocket configuration with better error handling
      const socket = new JsSIP.WebSocketInterface(this.config.sipServerWss);

      // Enhanced UserAgent configuration
      const configuration = {
        sockets: [socket],
        uri: sipUri,
        password: agentCredentials.password,
        display_name: agentCredentials.displayName,
        authorization_user: agentCredentials.extension,
        realm: this.config.sipDomain, // Explicitly set realm
        register: true,
        register_expires: 300,
        session_timers: false,
        connection_recovery_min_interval: 2,
        connection_recovery_max_interval: 30,
        pcConfig: {
          iceServers: [
            { urls: this.config.stunServer }
          ]
        }
      };

      // Create UserAgent
      this.ua = new JsSIP.UA(configuration);

      // Set up event handlers
      this.setupUserAgentEvents();

      // Start the UserAgent
      this.ua.start();

      console.log('WebRTC: Starting SIP registration for agent:', agentCredentials.extension);
      return true;

    } catch (error) {
      console.error('WebRTC: Failed to register agent:', error);
      this.emit('registrationFailed', null, undefined, error instanceof Error ? error.message : 'Unknown error');
      return false;
    }
  }

  /**
   * Unregister agent from SIP server
   */
  async unregisterAgent(): Promise<void> {
    if (this.ua) {
      try {
        this.ua.stop();
        this.ua = null;
        this.currentAgent = null;
        this.isRegistered = false;
        this.activeSessions.clear();
        
        console.log('WebRTC: Agent unregistered successfully');
        this.emit('unregistered');
      } catch (error) {
        console.error('WebRTC: Error during unregistration:', error);
      }
    }
  }

  /**
   * Get current agent credentials
   */
  getCurrentAgent(): AgentSipCredentials | null {
    return this.currentAgent;
  }

  /**
   * Check if agent is registered
   */
  isAgentRegistered(): boolean {
    return this.isRegistered && this.ua && this.currentAgent !== null;
  }

  /**
   * Get active call sessions
   */
  getActiveSessions(): WebRTCCallSession[] {
    return Array.from(this.activeSessions.values());
  }

  /**
   * Setup UserAgent event handlers
   */
  private setupUserAgentEvents(): void {
    if (!this.ua) return;

    // Registration events
    this.ua.on('registered', () => {
      // Reduced logging for cleaner console
      this.isRegistered = true;
      this.emit('registered', { agent: this.currentAgent });
    });

    this.ua.on('unregistered', () => {
      // Reduced logging for cleaner console
      this.isRegistered = false;
      this.emit('unregistered');
    });

    this.ua.on('registrationFailed', (e: any) => {
      // Only log registration failures as they're important
      console.error('🔴 WebRTC: Registration failed:', e.cause);
      this.isRegistered = false;
      this.emit('registrationFailed', null, undefined, e.cause);
    });

    // Incoming call events
    this.ua.on('newRTCSession', (e: any) => {
      const session = e.session;
      
      if (session.direction === 'incoming') {
        this.handleIncomingCall(session);
      } else {
        this.handleOutgoingCall(session);
      }
    });

    // Connection events
    this.ua.on('connected', () => {
      // Reduced logging for cleaner console
      this.emit('connectionStateChanged', { connected: true });
    });

    this.ua.on('disconnected', (e: any) => {
      // Reduced logging for cleaner console
      this.emit('connectionStateChanged', { connected: false });
    });

    // Transport events for better debugging (only errors)
    this.ua.on('transportError', (e: any) => {
      console.error('🔴 WebRTC: Transport error:', e);
    });

    this.ua.on('transportClosed', (e: any) => {
      // Reduced logging for cleaner console
    });
  }

  /**
   * Handle incoming call
   */
  private handleIncomingCall(session: any): void {
    // Reduced logging for cleaner console

    const callSession: WebRTCCallSession = {
      id: this.generateCallId(),
      session,
      direction: 'inbound',
      remoteNumber: session.remote_identity.uri.user,
      localNumber: this.currentAgent?.extension || '',
      status: 'ringing',
      startTime: new Date()
    };

    this.activeSessions.set(callSession.id, callSession);

    // Set up session event handlers
    this.setupSessionEvents(callSession);

    // Emit incoming call event
    this.emit('incomingCall', {
      callerNumber: callSession.remoteNumber,
      callerName: session.remote_identity.display_name || 'Unknown',
      timestamp: callSession.startTime.toISOString(),
      callId: callSession.id
    }, callSession);
  }

  /**
   * Handle outgoing call (for future implementation)
   */
  private handleOutgoingCall(session: any): void {
    console.log('WebRTC: Outgoing call initiated');
    // Implementation for outgoing calls will be added in Phase 2
  }

  /**
   * Setup session-specific event handlers
   */
  private setupSessionEvents(callSession: WebRTCCallSession): void {
    const session = callSession.session;

    session.on('accepted', () => {
      console.log('WebRTC: Call accepted');
      callSession.status = 'answered';
      this.emit('callAnswered', null, callSession);
    });

    session.on('ended', () => {
      console.log('WebRTC: Call ended');
      callSession.status = 'ended';
      callSession.endTime = new Date();
      callSession.duration = Math.floor((callSession.endTime.getTime() - callSession.startTime.getTime()) / 1000);
      
      this.activeSessions.delete(callSession.id);
      this.emit('callEnded', null, callSession);
    });

    session.on('failed', (e: any) => {
      console.log('WebRTC: Call failed:', e.cause);
      callSession.status = 'failed';
      callSession.endTime = new Date();
      
      this.activeSessions.delete(callSession.id);
      this.emit('callFailed', { cause: e.cause }, callSession);
    });
  }

  /**
   * Answer an incoming call
   */
  answerCall(sessionId: string): boolean {
    const callSession = this.activeSessions.get(sessionId);
    if (!callSession || callSession.status !== 'ringing') {
      console.error('WebRTC: Cannot answer call - invalid session or status');
      return false;
    }

    try {
      const options = {
        mediaConstraints: {
          audio: true,
          video: false
        }
      };

      callSession.session.answer(options);
      console.log('WebRTC: Answering call');
      return true;
    } catch (error) {
      console.error('WebRTC: Error answering call:', error);
      return false;
    }
  }

  /**
   * Reject an incoming call
   */
  rejectCall(sessionId: string): boolean {
    const callSession = this.activeSessions.get(sessionId);
    if (!callSession) {
      console.error('WebRTC: Cannot reject call - session not found');
      return false;
    }

    try {
      callSession.session.terminate();
      console.log('WebRTC: Rejecting call');
      return true;
    } catch (error) {
      console.error('WebRTC: Error rejecting call:', error);
      return false;
    }
  }

  /**
   * End an active call
   */
  endCall(sessionId: string): boolean {
    const callSession = this.activeSessions.get(sessionId);
    if (!callSession) {
      console.error('WebRTC: Cannot end call - session not found');
      return false;
    }

    try {
      callSession.session.terminate();
      console.log('WebRTC: Ending call');
      return true;
    } catch (error) {
      console.error('WebRTC: Error ending call:', error);
      return false;
    }
  }

  /**
   * Generate unique call ID
   */
  private generateCallId(): string {
    return `webrtc-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }
}

// Export singleton instance
export const webrtcService = new WebRTCService();

// Export class for testing
export { WebRTCService };
