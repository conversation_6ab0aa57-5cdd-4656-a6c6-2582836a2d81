/**
 * SIP Voice System Loader
 * Loads and initializes the HTTPWebSocketVoice class from SIP developer
 * This file should be loaded before the voice service is used
 */

// Voice configuration for CCR system (updated with ngrok URLs)
const VOICE_CONFIG = {
  sipServer: '*************',
  extension: '1003',
  password: 'webrtc1003'
};

// WebSocket URLs (updated to use ngrok)
const WEBSOCKET_URL = 'wss://c66e8de5f99b.ngrok-free.app/ws';
const SECURE_WEBSOCKET_URL = 'wss://ded9c059d113.ngrok-free.app/ws';

/**
 * HTTPWebSocketVoice class implementation
 * This is a placeholder for the SIP developer's actual implementation
 * The real implementation should be provided by the SIP developer
 */
class HTTPWebSocketVoice {
  private config: any;
  private isInitialized = false;
  private isConnected = false;
  private hasActiveCall = false;
  private isMuted = false;

  constructor(config: any) {
    this.config = config;
    console.log('🎙️ HTTPWebSocketVoice instance created with config:', config);
  }

  async initialize(): Promise<boolean> {
    try {
      console.log('🔄 Initializing SIP voice system...');
      console.log('📞 Extension:', this.config.extension);
      console.log('🌐 WebSocket URL:', WEBSOCKET_URL);
      
      // Simulate initialization process
      // In real implementation, this would connect to SIP server
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      this.isInitialized = true;
      this.isConnected = true;
      
      console.log('✅ SIP voice system initialized successfully');
      return true;
    } catch (error) {
      console.error('❌ SIP voice system initialization failed:', error);
      return false;
    }
  }

  async makeOutboundCall(phoneNumber: string): Promise<boolean> {
    try {
      console.log(`📞 Making outbound call to ${phoneNumber}`);
      
      // Simulate outbound call process
      await new Promise(resolve => setTimeout(resolve, 500));
      
      this.hasActiveCall = true;
      console.log('✅ Outbound call connected');
      return true;
    } catch (error) {
      console.error('❌ Outbound call failed:', error);
      return false;
    }
  }

  hangupCall(): boolean {
    try {
      console.log('📞 Hanging up call');
      this.hasActiveCall = false;
      this.isMuted = false;
      console.log('✅ Call hung up successfully');
      return true;
    } catch (error) {
      console.error('❌ Hangup failed:', error);
      return false;
    }
  }

  toggleMute(): boolean {
    try {
      this.isMuted = !this.isMuted;
      console.log(`🎤 Microphone ${this.isMuted ? 'muted' : 'unmuted'}`);
      return this.isMuted;
    } catch (error) {
      console.error('❌ Toggle mute failed:', error);
      return this.isMuted;
    }
  }

  getStatus() {
    return {
      connected: this.isConnected,
      hasActiveCall: this.hasActiveCall,
      isMuted: this.isMuted,
      extension: this.config.extension,
      websocketUrl: WEBSOCKET_URL
    };
  }
}

/**
 * Load and initialize the SIP voice system
 * This function should be called when the application starts
 */
export function loadSipVoiceSystem(): void {
  try {
    console.log('📋 Loading SIP voice system...');
    
    // Make HTTPWebSocketVoice available globally
    (window as any).HTTPWebSocketVoice = HTTPWebSocketVoice;
    (window as any).VOICE_CONFIG = VOICE_CONFIG;
    (window as any).WEBSOCKET_URL = WEBSOCKET_URL;
    (window as any).SECURE_WEBSOCKET_URL = SECURE_WEBSOCKET_URL;
    
    console.log('✅ SIP voice system loaded and ready');
    console.log('🎯 HTTPWebSocketVoice class is now available');
    console.log('📞 Extension 1003 ready for WebRTC voice calls');
    
  } catch (error) {
    console.error('❌ Failed to load SIP voice system:', error);
  }
}

/**
 * Check if SIP voice system is loaded
 */
export function isSipVoiceSystemLoaded(): boolean {
  return typeof (window as any).HTTPWebSocketVoice !== 'undefined';
}

/**
 * Get voice system configuration
 */
export function getVoiceConfig() {
  return {
    ...VOICE_CONFIG,
    websocketUrl: WEBSOCKET_URL,
    secureWebsocketUrl: SECURE_WEBSOCKET_URL
  };
}

// Auto-load when this module is imported
if (typeof window !== 'undefined') {
  loadSipVoiceSystem();
}
