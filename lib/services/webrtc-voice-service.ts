/**
 * WebRTC Voice Service for SIP-based Voice Communication
 * Integrates with CCR system using HTTPWebSocketVoice
 * Updated to use SIP developer's implementation
 */

// Voice configuration for CCR system
const VOICE_CONFIG = {
  sipServer: '*************',
  extension: '1003',
  password: 'webrtc1003'
};

// WebSocket URLs (updated to use ngrok)
const WEBSOCKET_URL = 'wss://c66e8de5f99b.ngrok-free.app/ws';
const SECURE_WEBSOCKET_URL = 'wss://ded9c059d113.ngrok-free.app/ws';

export interface WebRTCVoiceState {
  isInitialized: boolean;
  isConnected: boolean;
  hasActiveCall: boolean;
  isMuted: boolean;
  error: string | null;
  extension: string;
}

export interface WebRTCVoiceCallbacks {
  onStateChange?: (state: WebRTCVoiceState) => void;
  onError?: (error: string) => void;
  onVoiceReady?: () => void;
  onCallConnected?: () => void;
  onCallEnded?: () => void;
}

// HTTPWebSocketVoice class interface (to be implemented by SIP developer's code)
interface HTTPWebSocketVoice {
  initialize(): Promise<boolean>;
  makeOutboundCall(phoneNumber: string): Promise<boolean>;
  hangupCall(): boolean;
  toggleMute(): boolean;
  getStatus(): {
    connected: boolean;
    hasActiveCall: boolean;
    isMuted: boolean;
    error?: string;
  };
}

class WebRTCVoiceService {
  private ccrVoice: HTTPWebSocketVoice | null = null;
  private callbacks: WebRTCVoiceCallbacks = {};
  private state: WebRTCVoiceState = {
    isInitialized: false,
    isConnected: false,
    hasActiveCall: false,
    isMuted: false,
    error: null,
    extension: VOICE_CONFIG.extension
  };

  /**
   * Initialize the voice communication system
   */
  async initialize(): Promise<boolean> {
    try {
      console.log('🎙️ Initializing CCR voice communication system...');

      this.updateState({
        isInitialized: true,
        error: null
      });

      // Create voice system instance using HTTPWebSocketVoice
      // Note: HTTPWebSocketVoice class should be imported/defined elsewhere
      if (typeof (window as any).HTTPWebSocketVoice !== 'undefined') {
        this.ccrVoice = new (window as any).HTTPWebSocketVoice(VOICE_CONFIG);
      } else {
        throw new Error('HTTPWebSocketVoice class not available. Please ensure SIP library is loaded.');
      }

      // Initialize the system
      const initialized = await this.ccrVoice.initialize();

      if (initialized) {
        console.log('✅ Voice system ready!');
        console.log('📞 Extension 1003 connected');
        console.log('🌐 WebSocket URL:', WEBSOCKET_URL);

        this.updateState({
          isConnected: true,
          error: null
        });

        this.callbacks.onVoiceReady?.();
        return true;
      } else {
        throw new Error('Voice system failed to initialize');
      }

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown voice system error';
      console.error('❌ Voice system initialization error:', errorMessage);

      this.updateState({
        error: errorMessage,
        isConnected: false
      });

      this.callbacks.onError?.(errorMessage);
      return false;
    }
  }

  /**
   * Answer an incoming call
   */
  async answerIncomingCall(channelId: string, agentId: string): Promise<boolean> {
    try {
      console.log(`📞 Answering call ${channelId} for agent ${agentId}`);

      // 1. Answer call in CCR system
      const response = await fetch('/api/answer', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          channelId,
          agentId,
          extension: VOICE_CONFIG.extension
        })
      });

      if (response.ok) {
        console.log('✅ Call answered in CCR system');
        console.log('🎙️ Voice will connect automatically via WebSocket');

        this.updateState({ hasActiveCall: true });
        this.callbacks.onCallConnected?.();

        return true;
      } else {
        console.error('❌ Failed to answer call in CCR system');
        return false;
      }

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Error answering call';
      console.error('❌ Error answering call:', errorMessage);
      this.updateState({ error: errorMessage });
      this.callbacks.onError?.(errorMessage);
      return false;
    }
  }

  /**
   * Make an outbound call
   */
  async makeOutboundCall(phoneNumber: string, agentId: string): Promise<boolean> {
    try {
      console.log(`📞 Making outbound call to ${phoneNumber} from agent ${agentId}`);

      // 1. Initiate call in CCR system
      const response = await fetch('/api/make-call', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          phoneNumber,
          agentId,
          extension: VOICE_CONFIG.extension
        })
      });

      if (response.ok) {
        console.log('✅ Outbound call initiated in CCR system');

        // 2. Make voice call via SIP.js
        if (this.ccrVoice) {
          const voiceCallSuccess = await this.ccrVoice.makeOutboundCall(phoneNumber);

          if (voiceCallSuccess) {
            console.log('✅ Voice call connected');
            this.updateState({ hasActiveCall: true });
            this.callbacks.onCallConnected?.();
            return true;
          } else {
            console.error('❌ Voice call failed');
            return false;
          }
        } else {
          console.error('❌ Voice system not initialized');
          return false;
        }
      } else {
        console.error('❌ Failed to initiate outbound call in CCR system');
        return false;
      }

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Error making outbound call';
      console.error('❌ Error making outbound call:', errorMessage);
      this.updateState({ error: errorMessage });
      this.callbacks.onError?.(errorMessage);
      return false;
    }
  }

  /**
   * Hang up the current call
   */
  hangupCurrentCall(): boolean {
    try {
      if (this.ccrVoice) {
        const success = this.ccrVoice.hangupCall();
        if (success) {
          console.log('✅ Call hung up');
          this.updateState({ hasActiveCall: false });
          this.callbacks.onCallEnded?.();
          return true;
        } else {
          console.error('❌ Failed to hang up call');
          return false;
        }
      } else {
        console.error('❌ Voice system not initialized');
        return false;
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Error hanging up call';
      console.error('❌ Error hanging up call:', errorMessage);
      this.updateState({ error: errorMessage });
      this.callbacks.onError?.(errorMessage);
      return false;
    }
  }

  /**
   * Toggle microphone mute/unmute
   */
  toggleMute(): boolean {
    try {
      if (this.ccrVoice) {
        const muted = this.ccrVoice.toggleMute();
        console.log(`🎤 Microphone ${muted ? 'muted' : 'unmuted'}`);
        this.updateState({ isMuted: muted });
        return muted;
      } else {
        console.error('❌ Voice system not initialized');
        return false;
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Error toggling microphone';
      console.error('❌ Error toggling microphone:', errorMessage);
      this.updateState({ error: errorMessage });
      this.callbacks.onError?.(errorMessage);
      return false;
    }
  }

  /**
   * Get current voice system status
   */
  getVoiceSystemStatus() {
    try {
      if (this.ccrVoice) {
        const status = this.ccrVoice.getStatus();
        console.log('📊 Voice system status:', status);

        // Update our internal state with the latest status
        this.updateState({
          isConnected: status.connected,
          hasActiveCall: status.hasActiveCall,
          isMuted: status.isMuted,
          error: status.error || null
        });

        return status;
      } else {
        return {
          connected: false,
          hasActiveCall: false,
          isMuted: false,
          error: 'Voice system not initialized'
        };
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Error getting voice status';
      console.error('❌ Error getting voice status:', errorMessage);
      return {
        connected: false,
        hasActiveCall: false,
        isMuted: false,
        error: errorMessage
      };
    }
  }

  /**
   * Cleanup voice resources
   */
  cleanup(): void {
    try {
      console.log('🧹 Cleaning up CCR voice resources');

      // Hang up any active calls
      if (this.state.hasActiveCall) {
        this.hangupCurrentCall();
      }

      // Reset state
      this.state = {
        isInitialized: false,
        isConnected: false,
        hasActiveCall: false,
        isMuted: false,
        error: null,
        extension: VOICE_CONFIG.extension
      };

      console.log('✅ CCR voice cleanup completed');

    } catch (error) {
      console.error('❌ CCR voice cleanup failed:', error);
    }
  }

  /**
   * Get current voice state
   */
  getState(): WebRTCVoiceState {
    return { ...this.state };
  }

  /**
   * Set callbacks for state changes
   */
  setCallbacks(callbacks: WebRTCVoiceCallbacks): void {
    this.callbacks = callbacks;
  }

  /**
   * Update internal state and notify callbacks
   */
  private updateState(updates: Partial<WebRTCVoiceState>): void {
    this.state = { ...this.state, ...updates };
    this.callbacks.onStateChange?.(this.state);
  }

  /**
   * Check if voice system is supported
   */
  static isSupported(): boolean {
    return typeof (window as any).HTTPWebSocketVoice !== 'undefined';
  }

  /**
   * Get voice configuration
   */
  getVoiceConfig() {
    return {
      ...VOICE_CONFIG,
      websocketUrl: WEBSOCKET_URL,
      secureWebsocketUrl: SECURE_WEBSOCKET_URL
    };
  }
}

// Export singleton instance
export const webrtcVoiceService = new WebRTCVoiceService();
