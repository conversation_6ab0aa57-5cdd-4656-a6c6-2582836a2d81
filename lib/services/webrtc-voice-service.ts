/**
 * WebRTC Voice Service for Browser-based Voice Communication
 * Handles microphone access, audio context, and voice communication setup
 * Separate from the JsSIP WebRTC service for SIP-based calling
 */

export interface WebRTCVoiceState {
  isInitialized: boolean;
  hasMicrophoneAccess: boolean;
  isVoiceReady: boolean;
  isMuted: boolean;
  error: string | null;
}

export interface WebRTCVoiceCallbacks {
  onStateChange?: (state: WebRTCVoiceState) => void;
  onError?: (error: string) => void;
  onVoiceReady?: () => void;
}

class WebRTCVoiceService {
  private localStream: MediaStream | null = null;
  private audioContext: AudioContext | null = null;
  private remoteAudio: HTMLAudioElement | null = null;
  private callbacks: WebRTCVoiceCallbacks = {};
  private state: WebRTCVoiceState = {
    isInitialized: false,
    hasMicrophoneAccess: false,
    isVoiceReady: false,
    isMuted: false,
    error: null
  };

  /**
   * Initialize WebRTC voice for a specific channel
   */
  async setupVoice(channelId: string, agentId: string): Promise<boolean> {
    try {
      console.log(`🎙️ Setting up WebRTC voice for channel ${channelId}`);
      
      this.updateState({ 
        isInitialized: true, 
        error: null 
      });

      // 1. Request microphone access
      const microphoneGranted = await this.requestMicrophoneAccess();
      if (!microphoneGranted) {
        throw new Error('Microphone access denied');
      }

      // 2. Create audio context for processing
      await this.setupAudioContext();

      // 3. Signal backend that WebRTC is ready
      const voiceReady = await this.signalBackendReady(channelId, agentId);
      if (!voiceReady) {
        throw new Error('Failed to establish voice connection with backend');
      }

      // 4. Setup remote audio element
      this.setupRemoteAudio();

      this.updateState({ 
        isVoiceReady: true 
      });

      this.callbacks.onVoiceReady?.();
      console.log('✅ WebRTC voice setup completed successfully');
      
      return true;

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown WebRTC error';
      console.error('❌ WebRTC voice setup failed:', errorMessage);
      
      this.updateState({ 
        error: errorMessage,
        isVoiceReady: false 
      });

      this.callbacks.onError?.(errorMessage);
      return false;
    }
  }

  /**
   * Request microphone access with enhanced audio settings
   */
  private async requestMicrophoneAccess(): Promise<boolean> {
    try {
      this.localStream = await navigator.mediaDevices.getUserMedia({
        audio: {
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true,
          sampleRate: 44100,
          channelCount: 1
        },
        video: false
      });

      console.log('✅ Microphone access granted');
      this.updateState({ hasMicrophoneAccess: true });
      return true;

    } catch (error) {
      console.error('❌ Microphone access failed:', error);
      
      let errorMessage = 'Could not access microphone';
      if (error instanceof Error) {
        if (error.name === 'NotAllowedError') {
          errorMessage = 'Microphone access denied. Please allow microphone access and try again.';
        } else if (error.name === 'NotFoundError') {
          errorMessage = 'No microphone found. Please connect a microphone and try again.';
        } else if (error.name === 'NotReadableError') {
          errorMessage = 'Microphone is being used by another application.';
        }
      }

      this.updateState({ 
        hasMicrophoneAccess: false,
        error: errorMessage 
      });
      
      return false;
    }
  }

  /**
   * Setup audio context for audio processing
   */
  private async setupAudioContext(): Promise<void> {
    if (!this.localStream) {
      throw new Error('Local stream not available');
    }

    try {
      this.audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();
      
      // Resume audio context if it's suspended (required by some browsers)
      if (this.audioContext.state === 'suspended') {
        await this.audioContext.resume();
      }

      const source = this.audioContext.createMediaStreamSource(this.localStream);
      console.log('✅ Audio context created successfully');

    } catch (error) {
      console.error('❌ Audio context setup failed:', error);
      throw new Error('Failed to setup audio context');
    }
  }

  /**
   * Signal backend that WebRTC is ready
   */
  private async signalBackendReady(channelId: string, agentId: string): Promise<boolean> {
    try {
      const apiUrl = process.env.NEXT_PUBLIC_CCR_API_URL;
      if (!apiUrl) {
        throw new Error('CCR API URL not configured');
      }

      const response = await fetch(`${apiUrl}/webrtc/offer`, {
        method: 'POST',
        headers: { 
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('accessToken')}`
        },
        body: JSON.stringify({
          channelId: channelId,
          offer: { type: 'browser-audio' },
          agentId: agentId
        })
      });

      if (!response.ok) {
        throw new Error(`Backend signaling failed: ${response.status}`);
      }

      const result = await response.json();
      
      if (result.success) {
        console.log('✅ Backend signaling successful');
        return true;
      } else {
        throw new Error(result.message || 'Backend signaling failed');
      }

    } catch (error) {
      console.error('❌ Backend signaling failed:', error);
      return false;
    }
  }

  /**
   * Setup remote audio element for customer voice
   */
  private setupRemoteAudio(): void {
    try {
      if (!this.remoteAudio) {
        this.remoteAudio = document.createElement('audio');
        this.remoteAudio.autoplay = true;
        this.remoteAudio.controls = false; // Hide controls in production
        this.remoteAudio.style.display = 'none'; // Hidden but functional
        document.body.appendChild(this.remoteAudio);
      }
      
      console.log('✅ Remote audio element setup completed');
    } catch (error) {
      console.error('❌ Remote audio setup failed:', error);
    }
  }

  /**
   * Mute/unmute microphone
   */
  toggleMute(): boolean {
    if (!this.localStream) {
      console.warn('⚠️ Cannot toggle mute: no local stream');
      return false;
    }

    const audioTracks = this.localStream.getAudioTracks();
    if (audioTracks.length === 0) {
      console.warn('⚠️ Cannot toggle mute: no audio tracks');
      return false;
    }

    const newMutedState = !this.state.isMuted;
    audioTracks.forEach(track => {
      track.enabled = !newMutedState;
    });

    this.updateState({ isMuted: newMutedState });
    console.log(`🔇 Microphone ${newMutedState ? 'muted' : 'unmuted'}`);
    
    return newMutedState;
  }

  /**
   * Cleanup WebRTC resources
   */
  cleanup(): void {
    try {
      console.log('🧹 Cleaning up WebRTC voice resources');

      // Stop local stream
      if (this.localStream) {
        this.localStream.getTracks().forEach(track => {
          track.stop();
        });
        this.localStream = null;
      }

      // Close audio context
      if (this.audioContext && this.audioContext.state !== 'closed') {
        this.audioContext.close();
        this.audioContext = null;
      }

      // Remove remote audio element
      if (this.remoteAudio && this.remoteAudio.parentNode) {
        this.remoteAudio.parentNode.removeChild(this.remoteAudio);
        this.remoteAudio = null;
      }

      // Reset state
      this.state = {
        isInitialized: false,
        hasMicrophoneAccess: false,
        isVoiceReady: false,
        isMuted: false,
        error: null
      };

      console.log('✅ WebRTC voice cleanup completed');

    } catch (error) {
      console.error('❌ WebRTC voice cleanup failed:', error);
    }
  }

  /**
   * Get current voice state
   */
  getState(): WebRTCVoiceState {
    return { ...this.state };
  }

  /**
   * Set callbacks for state changes
   */
  setCallbacks(callbacks: WebRTCVoiceCallbacks): void {
    this.callbacks = callbacks;
  }

  /**
   * Update internal state and notify callbacks
   */
  private updateState(updates: Partial<WebRTCVoiceState>): void {
    this.state = { ...this.state, ...updates };
    this.callbacks.onStateChange?.(this.state);
  }

  /**
   * Check if WebRTC is supported
   */
  static isSupported(): boolean {
    return !!(
      navigator.mediaDevices &&
      navigator.mediaDevices.getUserMedia &&
      (window.AudioContext || (window as any).webkitAudioContext)
    );
  }
}

// Export singleton instance
export const webrtcVoiceService = new WebRTCVoiceService();
