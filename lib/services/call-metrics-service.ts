import { useCallMetricsStore, CallMetric } from '@/lib/stores/call-metrics-store';
import { IncomingCall, CallLog } from '@/lib/services/socket-service';

/**
 * Service for managing call metrics and integrating with the call simulation system
 */
export class CallMetricsService {
  private static instance: CallMetricsService;
  private callStartTimes: Map<string, Date> = new Map();
  private callAnswerTimes: Map<string, Date> = new Map();

  private constructor() {}

  static getInstance(): CallMetricsService {
    if (!CallMetricsService.instance) {
      CallMetricsService.instance = new CallMetricsService();
    }
    return CallMetricsService.instance;
  }

  /**
   * Start tracking a call when it begins ringing
   */
  startCallTracking(call: IncomingCall): void {
    this.callStartTimes.set(call.id, new Date());
    console.log(`Started tracking call ${call.id} at ${new Date().toISOString()}`);
  }

  /**
   * Mark a call as answered and start tracking handle time
   */
  answerCall(callId: string): void {
    this.callAnswerTimes.set(callId, new Date());
    console.log(`Call ${callId} answered at ${new Date().toISOString()}`);
  }

  /**
   * End call tracking and store metrics
   */
  endCallTracking(
    call: IncomingCall | CallLog, 
    agentId: string,
    resolutionStatus: CallMetric['resolutionStatus'] = 'resolved',
    customerSatisfaction?: number,
    notes?: string,
    ticketCreated: boolean = false,
    ticketId?: string
  ): void {
    const endTime = new Date();
    const startTime = this.callStartTimes.get(call.id);
    const answerTime = this.callAnswerTimes.get(call.id);

    if (!startTime) {
      console.warn(`No start time found for call ${call.id}`);
      return;
    }

    // Calculate durations
    const totalDuration = Math.round((endTime.getTime() - startTime.getTime()) / 1000); // seconds
    const handleTime = answerTime 
      ? Math.round((endTime.getTime() - answerTime.getTime()) / 1000)
      : 0; // seconds

    // Determine call status
    let status: CallMetric['status'] = 'ended';
    if (call.status === 'missed') {
      status = 'missed';
    } else if (resolutionStatus === 'escalated') {
      status = 'escalated';
    } else if (resolutionStatus === 'transferred') {
      status = 'transferred';
    } else if (answerTime) {
      status = 'answered';
    }

    // Create call metric
    const callMetric: Omit<CallMetric, 'id' | 'timestamp'> = {
      agentId,
      callId: call.id,
      callerNumber: call.callerNumber,
      callerName: call.callerName,
      productName: call.productName,
      startTime: startTime.toISOString(),
      endTime: endTime.toISOString(),
      duration: totalDuration,
      handleTime,
      status,
      resolutionStatus,
      customerSatisfaction,
      notes,
      ticketCreated,
      ticketId,
      callType: 'inbound',
      isSimulated: true, // All calls through this service are simulated for now
    };

    // Store the metric
    const store = useCallMetricsStore.getState();
    store.addCallMetric(callMetric);

    // Clean up tracking data
    this.callStartTimes.delete(call.id);
    this.callAnswerTimes.delete(call.id);

    console.log(`Stored metrics for call ${call.id}:`, {
      duration: totalDuration,
      handleTime,
      status,
      resolutionStatus
    });
  }

  /**
   * Mark a call as missed
   */
  missCall(call: IncomingCall, agentId: string): void {
    this.endCallTracking(call, agentId, 'unresolved');
  }

  /**
   * Get metrics for a specific agent
   */
  getAgentMetrics(agentId: string) {
    const store = useCallMetricsStore.getState();
    return {
      callMetrics: store.getMetricsForAgent(agentId),
      aggregatedMetrics: store.getAggregatedMetricsForAgent(agentId),
      dailyMetrics: store.getDailyMetricsForAgent(agentId),
    };
  }

  /**
   * Get system-wide metrics (for supervisors and above)
   */
  getSystemMetrics() {
    const store = useCallMetricsStore.getState();
    return {
      callMetrics: store.callMetrics,
      aggregatedMetrics: store.getAllMetrics(),
      dailyMetrics: store.dailyMetrics,
    };
  }

  /**
   * Update call resolution after ticket creation
   */
  updateCallResolution(
    callId: string,
    resolutionStatus: CallMetric['resolutionStatus'],
    customerSatisfaction?: number,
    notes?: string,
    ticketId?: string
  ): void {
    const store = useCallMetricsStore.getState();
    const callMetric = store.callMetrics.find(m => m.callId === callId);
    
    if (callMetric) {
      store.updateCallMetric(callMetric.id, {
        resolutionStatus,
        customerSatisfaction,
        notes,
        ticketCreated: !!ticketId,
        ticketId,
      });
      
      console.log(`Updated resolution for call ${callId}:`, {
        resolutionStatus,
        customerSatisfaction,
        ticketCreated: !!ticketId
      });
    }
  }

  /**
   * Generate sample data for testing (development only)
   */
  generateSampleData(agentId: string, numberOfCalls: number = 10): void {
    const store = useCallMetricsStore.getState();
    
    const sampleCallers = [
      { name: 'John Doe', number: '+254712345678' },
      { name: 'Jane Smith', number: '+254722123456' },
      { name: 'Bob Johnson', number: '+254733987654' },
      { name: 'Alice Brown', number: '+254711234567' },
      { name: 'Charlie Wilson', number: '+254700987654' },
    ];

    const products = ['Internet Fiber', 'Mobile Data Plan', 'Business VoIP', 'Cloud Storage', 'Home Security'];
    const resolutionStatuses: CallMetric['resolutionStatus'][] = ['resolved', 'escalated', 'follow_up_needed', 'transferred'];

    for (let i = 0; i < numberOfCalls; i++) {
      const caller = sampleCallers[Math.floor(Math.random() * sampleCallers.length)];
      const product = products[Math.floor(Math.random() * products.length)];
      const resolutionStatus = resolutionStatuses[Math.floor(Math.random() * resolutionStatuses.length)];
      
      // Generate random times within the last 30 days
      const daysAgo = Math.floor(Math.random() * 30);
      const hoursAgo = Math.floor(Math.random() * 24);
      const minutesAgo = Math.floor(Math.random() * 60);
      
      const startTime = new Date();
      startTime.setDate(startTime.getDate() - daysAgo);
      startTime.setHours(startTime.getHours() - hoursAgo);
      startTime.setMinutes(startTime.getMinutes() - minutesAgo);
      
      const duration = 120 + Math.floor(Math.random() * 600); // 2-12 minutes
      const handleTime = 60 + Math.floor(Math.random() * 480); // 1-9 minutes
      
      const endTime = new Date(startTime.getTime() + (duration * 1000));

      const sampleMetric: Omit<CallMetric, 'id' | 'timestamp'> = {
        agentId,
        callId: `sample_${Date.now()}_${i}`,
        callerNumber: caller.number,
        callerName: caller.name,
        productName: product,
        startTime: startTime.toISOString(),
        endTime: endTime.toISOString(),
        duration,
        handleTime,
        status: 'answered',
        resolutionStatus,
        customerSatisfaction: Math.floor(Math.random() * 5) + 1,
        notes: `Sample call ${i + 1}`,
        ticketCreated: Math.random() > 0.3, // 70% chance of ticket creation
        ticketId: Math.random() > 0.3 ? `ticket_${Date.now()}_${i}` : undefined,
        callType: 'inbound',
        isSimulated: true,
      };

      store.addCallMetric(sampleMetric);
    }

    console.log(`Generated ${numberOfCalls} sample call metrics for agent ${agentId}`);
  }

  /**
   * Clear all metrics (for testing)
   */
  clearAllMetrics(): void {
    const store = useCallMetricsStore.getState();
    store.clearMetrics();
    this.callStartTimes.clear();
    this.callAnswerTimes.clear();
    console.log('Cleared all call metrics');
  }

  /**
   * Clear metrics for a specific agent
   */
  clearAgentMetrics(agentId: string): void {
    const store = useCallMetricsStore.getState();
    store.clearAgentMetrics(agentId);
    console.log(`Cleared metrics for agent ${agentId}`);
  }

  /**
   * Export metrics data (for reporting)
   */
  exportMetrics(agentId?: string): CallMetric[] {
    const store = useCallMetricsStore.getState();
    return agentId 
      ? store.getMetricsForAgent(agentId)
      : store.callMetrics;
  }

  /**
   * Get call metrics summary for dashboard
   */
  getDashboardSummary(agentId?: string) {
    const store = useCallMetricsStore.getState();
    const aggregated = agentId 
      ? store.getAggregatedMetricsForAgent(agentId)
      : store.getAllMetrics();

    return {
      totalCallsToday: aggregated.callsToday,
      totalCallsThisWeek: aggregated.callsThisWeek,
      totalCallsThisMonth: aggregated.callsThisMonth,
      averageHandleTime: aggregated.averageHandleTime,
      resolutionRate: aggregated.resolutionRate,
      customerSatisfaction: aggregated.customerSatisfactionAverage,
      escalationRate: aggregated.escalationRate,
      answeredCalls: aggregated.answeredCalls,
      missedCalls: aggregated.missedCalls,
    };
  }
}

// Export singleton instance
export const callMetricsService = CallMetricsService.getInstance();

// Hook for React components
export const useCallMetricsService = () => {
  return callMetricsService;
};
