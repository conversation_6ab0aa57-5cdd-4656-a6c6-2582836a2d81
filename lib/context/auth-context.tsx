'use client';

import React, { createContext, useContext, useEffect, useState } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import { authService, User, UserRole } from '../api';
import { useAuth } from '../hooks';

interface AuthContextType {
  user: User | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  login: (email: string, password: string) => Promise<void>;
  logout: () => Promise<void>;
  hasPermission: (requiredRole: UserRole) => boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const router = useRouter();
  const pathname = usePathname();
  const {
    user,
    isLoadingUser,
    refetchUser,
    login: authLogin,
    logout: authLogout
  } = useAuth();
  const [isInitialized, setIsInitialized] = useState(false);

  // Check authentication status on initial load
  useEffect(() => {
    const checkAuth = async () => {
      const isAuthenticated = authService.isAuthenticated();

      console.log('🔐 Auth Status:', {
        isAuthenticated,
        currentUser: user,
        isLoading: isLoadingUser,
        pathname
      });

      // Don't redirect from forbidden or error pages
      if (pathname === '/forbidden' || pathname === '/error') {
        setIsInitialized(true);
        return;
      }

      if (!isAuthenticated && !pathname.startsWith('/auth/')) {
        router.push('/auth/login');
        return;
      }

      if (isAuthenticated && !user && !isLoadingUser) {
        try {
          // Fetch user data
          const userData = await authService.getCurrentUser();
          console.log('👤 User Data from API:', userData);

          // Fetch role data if we have role_id
          if (userData.role_id) {
            console.log('🎭 Fetching role for ID:', userData.role_id);

            const roleResponse = await fetch(`/api/roles/${userData.role_id}?include=permissions`, {
              headers: { 'Authorization': `Bearer ${localStorage.getItem('accessToken')}` }
            });
            const roleData = await roleResponse.json();

            console.log('🎭 Role Data from API:', roleData);

            if (roleData.success && roleData.data) {
              // Attach role to user data
              userData.role = roleData.data;
              console.log('👑 User with Role:', {
                name: `${userData.first_name} ${userData.last_name}`,
                email: userData.email,
                roleName: userData.role?.name,
                permissions: userData.role?.permissions?.map(p => p.code)
              });
            }
          }

          // Remove redundant setUser call since user state is managed by useAuth hook
        } catch (error) {
          console.error('❌ Error fetching user data:', error);
          // Don't log out on error, just continue
        }
      }

      setIsInitialized(true);
    };

    checkAuth();
  }, [pathname]);

  // Handle login
  const login = async (email: string, password: string) => {
    await authLogin({ email, password });
  };

  // Handle logout
  const logout = async () => {
    await authLogout();
  };

  // Check if user has sufficient role permissions
  const hasPermission = (requiredRole: UserRole): boolean => {
    console.log('🔒 Checking Permission:', {
      requiredRole,
      userRole: user?.role?.name,
      hasRole: !!user?.role
    });

    if (!user || !user.role) return false;

    // Order of roles from highest to lowest permissions
    const roleHierarchy = [
      UserRole.PLATFORM_OWNER,
      UserRole.SUPERVISOR,
      UserRole.AGENT,
      UserRole.USER,
    ];

    // Map role name to UserRole enum for comparison
    const roleNameToEnum: Record<string, UserRole> = {
      // Standard role names from the enum
      'Platform Owner': UserRole.PLATFORM_OWNER,
      'Supervisor': UserRole.SUPERVISOR,
      'Agent': UserRole.AGENT,
      'User': UserRole.USER,

      // Additional mappings for roles from roles.json
      'platform_owner': UserRole.PLATFORM_OWNER,
      'admin': UserRole.SUPERVISOR,
      'super_admin': UserRole.SUPERVISOR,
      'Super Admin': UserRole.SUPERVISOR,
      'supervisor': UserRole.SUPERVISOR,
      'team_lead': UserRole.SUPERVISOR,
      'Team Lead': UserRole.SUPERVISOR,
      'agent': UserRole.AGENT,
      'user': UserRole.USER,
    };

    // Convert the role name to enum
    const userRole = roleNameToEnum[user.role.name];

    console.log('🔑 Role Check:', {
      userRoleName: user.role.name,
      mappedUserRole: userRole,
      roleHierarchy
    });

    // If role name doesn't map to a known enum, return false
    if (!userRole) return false;

    const userRoleIndex = roleHierarchy.indexOf(userRole);
    const requiredRoleIndex = roleHierarchy.indexOf(requiredRole);

    console.log('📊 Permission Calculation:', {
      userRoleIndex,
      requiredRoleIndex,
      hasPermission: userRoleIndex <= requiredRoleIndex
    });

    // If role not found or user's role has fewer permissions than required
    if (userRoleIndex === -1 || requiredRoleIndex === -1) return false;

    // Lower index means higher permissions (Super Admin is 0)
    return userRoleIndex <= requiredRoleIndex;
  };

  const value: AuthContextType = {
    user: user || null,
    isLoading: isLoadingUser || !isInitialized,
    isAuthenticated: !!user && authService.isAuthenticated(),
    login,
    logout,
    hasPermission,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
}

export const useAuthContext = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuthContext must be used within an AuthProvider');
  }
  return context;
};
