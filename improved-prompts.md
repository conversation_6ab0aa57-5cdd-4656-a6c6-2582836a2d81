# Improved Prompts for Project Deliverables

## 1. Fix Escalation Filter on Tickets Table

**Current Issue:** The escalation filter on the tickets table is not filtering tickets correctly. When users select "Escalated Only" from the dropdown, the table should only show tickets that have been escalated, but this functionality is not working.

**Technical Details:**
- The issue is in the `useTickets` hook where the `is_escalated` parameter is not being properly passed to the API or processed correctly.
- The filter is implemented in `app/dashboard/tickets/page.tsx` where the `isEscalatedFilter` state is defined and passed to the `useTickets` hook.
- The API expects a boolean value for the `is_escalated` parameter, but we may be passing a string.

**Implementation Requirements:**
- Modify the `useTickets` hook to correctly handle the `is_escalated` parameter
- Ensure the parameter is properly converted from string values ('yes'/'no') to boolean values (true/false) before being sent to the API
- Test the filter by selecting different options from the dropdown and verifying that the correct tickets are displayed

## 2. Improve FAQs Rendering Layout

**Current Issue:** The FAQs section doesn't fit well within the UI, causing layout issues and poor user experience.

**Technical Details:**
- The FAQs are currently displayed in `components/faqs/product-faqs-dialog.tsx` which is used when viewing FAQs related to a product.
- The layout needs to be improved to better fit within the available space and provide a better user experience.

**Implementation Requirements:**
- Redesign the FAQs layout in the `ProductFAQsDialog` component to:
  - Make better use of available space
  - Improve readability of questions and answers
  - Ensure consistent spacing and alignment
  - Make the component responsive for different screen sizes
  - Consider using a collapsible accordion pattern for FAQs to save space
- Maintain all existing functionality while improving the visual presentation
- Ensure the dialog appears correctly when opened from the call interface

## 3. Autofetch Product While on Call (Floating Ticket Modal)

**Current Issue:** When an agent is on a call and opens the floating ticket modal, the product information is not automatically populated, unlike the contact information which is already being autofetched.

**Technical Details:**
- The `FloatingTicketDialog` component in `components/tickets/floating-ticket-dialog.tsx` already has the capability to receive an `initialProductId` prop.
- The `EnhancedIncomingCall` component in `components/calls/EnhancedIncomingCall.tsx` passes the product ID to the `FloatingTicketDialog` when opening it.
- The issue is that the product information is not being properly passed or utilized.

**Implementation Requirements:**
- Ensure that when a call comes in with product information, that product is automatically selected in the ticket creation form
- Modify the `EnhancedIncomingCall` component to correctly pass the product ID to the `FloatingTicketDialog`
- Update the `FloatingTicketDialog` component to properly use the passed product ID to set the initial value in the form
- Test the functionality by simulating an incoming call with product information and verifying that the product is pre-selected in the ticket form

## 4. Add Call ID for SIP Server Integration

**Current Issue:** Calls currently don't have a Call ID that can be used to fetch details from the SIP server.

**Technical Details:**
- The `IncomingCall` interface in `lib/services/socket-service.ts` already has a `callerId` property, but it may not be properly displayed or utilized throughout the application.
- The call information needs to be displayed in the call interface to allow agents to reference it when needed.

**Implementation Requirements:**
- Ensure the `callerId` property is properly populated in the `IncomingCall` object
- Update the `EnhancedIncomingCall` component to prominently display the Call ID
- Add a label clearly identifying this as the "Call ID" that can be used for SIP server integration
- Consider adding a copy button next to the Call ID for easy copying
- Test the functionality by simulating incoming calls and verifying that the Call ID is displayed correctly

## 5. Restrict Technical Structure Details in Logs to Super Admin

**Current Issue:** The technical structure details in the audit logs are visible to all users with access to the logs page, but these details should only be visible to super admins.

**Technical Details:**
- The audit logs are displayed in `app/dashboard/settings/audit/page.tsx`
- The log details dialog shows technical information that should be restricted
- We need to implement permission-based visibility for these technical details

**Implementation Requirements:**
- Modify the log details dialog to check for super admin permissions before displaying technical structure details
- Hide or remove the technical structure section for non-super admin users
- Maintain all other log information for regular admins
- Add a message for non-super admins indicating that additional technical details are available to super admins only
- Test the functionality by logging in as different user roles and verifying that the technical details are only visible to super admins

## 6. Improve Error Display for Admin in Log Details Dialog

**Current Issue:** Error type and error message for admins in the Log Details Dialog are currently displayed as part of an array, making them difficult to read and understand.

**Technical Details:**
- The log details dialog in `app/dashboard/settings/audit/page.tsx` displays error information
- Currently, error type and message are embedded within an array structure
- These need to be extracted and displayed more prominently

**Implementation Requirements:**
- Modify the log details dialog to extract and display error type and error message separately from the array
- Create dedicated sections for "Error Type" and "Error Message" in the dialog
- Format these sections to be easily readable and visually distinct (possibly using appropriate colors for error information)
- Ensure that the original array data is still available for super admins who need the complete technical details
- Test the functionality by generating logs with errors and verifying that the error information is displayed correctly

## SIP Server Payload Example

{
  "callId": "SIP-20250523-123456-0001",
  "channelId": "main-channel-01",
  "agentExtension": "1001",
  "callerNumber": "+254712345678",
  "destinationNumber": "+254800123456",
  "timestamp": "2025-05-23T12:34:56Z",
  "direction": "inbound",
  "status": "ringing",
  "sipSessionId": "abc123xyz",
  "callerName": "Samuel K.",
  "metadata": {
    "ivr_path": "sales_queue",
    "recordingUrl": null,
    "tags": ["priority_customer", "vip"]
  }
}
