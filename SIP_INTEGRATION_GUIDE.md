# SIP Server Integration Guide

## Complete Implementation Overview

This guide explains the complete SIP server integration for call recordings, including all components, data flow, and user interactions.

## 🏗️ Architecture Overview

### Components Created

1. **SipRecordingService** (`lib/services/sip-recording-service.ts`)
   - Handles API communication with SIP server
   - Parses SIP recording filenames
   - Provides recording URLs and metadata

2. **useSipRecordings Hook** (`lib/hooks/useSipRecordings.ts`)
   - React hook for SIP recording data
   - Caching and error handling
   - Call-to-recording matching logic

3. **Enhanced CallHistory** (`components/calls/CallHistory.tsx`)
   - Updated to use SIP recordings
   - Shows recording availability indicators
   - Integrates with enhanced recording player

4. **SipRecordingDebug** (`components/calls/SipRecordingDebug.tsx`)
   - Debug interface for testing SIP integration
   - Filename parsing tests
   - Connection status monitoring

5. **Recording Statistics** (Added to `/calls` page)
   - Real-time SIP server statistics
   - Connection status monitoring
   - Recording counts and metrics

## 📊 Data Flow

### 1. SIP Server API Integration

**Your SIP Server Response:**
```json
[
  "in-+254709918000-716038129-20250528-134749-1748440069.72.wav",
  "in-+254709918000-716038129-20250528-134801-1748440081.73.wav"
]
```

**Parsed Data Structure:**
```typescript
{
  direction: "in",
  callerNumber: "+254709918000",
  callId: "716038129",
  date: "20250528",
  time: "134749",
  sipId: "1748440069.72",
  extension: "wav"
}
```

### 2. Call-to-Recording Matching

The system matches call records to SIP recordings using:
1. **Primary**: Phone number matching (with number cleaning)
2. **Secondary**: Call ID matching (if available)
3. **Tertiary**: Timestamp proximity (within 5 minutes)

### 3. Recording URL Generation

```typescript
// Original filename from SIP server
const filename = "in-+254709918000-716038129-20250528-134749-1748440069.72.wav";

// Generated URL for playback
const url = `${NEXT_PUBLIC_SIP_FILES_ENDPOINT}/${encodeURIComponent(filename)}`;
// Result: "http://your-sip-server/api/recordings/files/in-%2B254709918000-716038129-20250528-134749-1748440069.72.wav"
```

## 🎯 User Journey

### Complete User Experience Flow:

1. **Navigate to Calls Page** (`/calls`)
   - See SIP recording statistics in dashboard cards
   - View connection status and recording counts

2. **View Call History** (History tab)
   - Table shows audio icon for calls with available recordings
   - Icon is enabled/disabled based on SIP server data

3. **Click Recording Icon**
   - System finds matching SIP recording
   - Opens enhanced recording player dialog
   - Logs access event to audit system

4. **Recording Playback**
   - Enhanced player loads with SIP conversion service
   - WAV files play directly (no conversion needed)
   - Full controls: play, pause, seek, speed, download

5. **Debug Interface** (SIP Debug tab)
   - Test filename parsing
   - Test call-to-recording matching
   - Monitor connection status
   - View all available recordings

## 🔧 Configuration Setup

### Environment Variables

Add to your `.env.local`:
```env
# SIP Server Integration
NEXT_PUBLIC_SIP_RECORDING_API="http://your-sip-server/api/recordings"
NEXT_PUBLIC_SIP_FILES_ENDPOINT="http://your-sip-server/api/recordings/files"
```

### API Endpoints Required

Your SIP server needs to provide:

1. **GET /api/recordings** - Returns array of recording filenames
2. **GET /api/recordings/files/{filename}** - Serves the actual audio file

## 🧪 Testing the Integration

### 1. Test SIP Server Connection

Navigate to `/calls` → "SIP Debug" tab:
- Check connection status
- View recording statistics
- Test filename parsing

### 2. Test Recording Playback

1. Go to `/calls` → "History" tab
2. Look for calls with audio icons
3. Click audio icon to open player
4. Test playback controls

### 3. Debug Tools

Use the SIP Debug interface to:
- Parse test filenames
- Test call matching logic
- Monitor API responses
- View raw recording data

## 📱 UI Components Integration

### Call History Table

**Before:**
```tsx
{call.recordingUrl ? (
  <Button>Play Recording</Button>
) : (
  <span>No Recording</span>
)}
```

**After:**
```tsx
{hasRecording(call) ? (
  <Button onClick={() => openRecordingPlayer(call)}>
    <FileAudio className="size-4" />
  </Button>
) : (
  <span>-</span>
)}
```

### Recording Player Dialog

**Enhanced Features:**
- SIP filename display in description
- Automatic format detection (.wav files)
- Error handling for missing recordings
- Audit logging for access tracking

### Statistics Dashboard

**Real-time Metrics:**
- Total recordings available
- Inbound vs outbound counts
- Today's recording count
- SIP server connection status

## 🔍 Troubleshooting

### Common Issues

1. **No Recordings Showing**
   - Check environment variables
   - Verify SIP server API endpoint
   - Check network connectivity
   - Use SIP Debug tab to test connection

2. **Recording Not Playing**
   - Verify file URL accessibility
   - Check CORS settings on SIP server
   - Test direct file access in browser

3. **Call Matching Issues**
   - Use SIP Debug tab to test matching logic
   - Check phone number formats
   - Verify call ID consistency

### Debug Steps

1. **Check SIP Debug Tab**
   - Connection status
   - Raw API response
   - Filename parsing results

2. **Browser Developer Tools**
   - Network tab for API calls
   - Console for error messages
   - Application tab for cached data

3. **Test Direct URLs**
   - Try accessing recording URLs directly
   - Verify file accessibility

## 🚀 Production Deployment

### Pre-deployment Checklist

- [ ] Configure production SIP server URLs
- [ ] Test API connectivity
- [ ] Verify CORS settings
- [ ] Test recording playback
- [ ] Check audit logging
- [ ] Verify error handling

### Performance Considerations

- **Caching**: Recording metadata is cached in React state
- **Lazy Loading**: Recordings loaded on demand
- **Error Handling**: Graceful degradation for API failures
- **Audit Logging**: All recording access is logged

## 📈 Future Enhancements

1. **Real-time Updates**: WebSocket integration for live recording updates
2. **Bulk Operations**: Download multiple recordings
3. **Advanced Search**: Search recordings by content/metadata
4. **Transcription**: Automatic speech-to-text integration
5. **Analytics**: Recording usage and access patterns

## 🔐 Security Considerations

- **Access Control**: Role-based recording access
- **Audit Logging**: All recording access is tracked
- **URL Security**: Encoded filenames prevent path traversal
- **CORS**: Proper CORS configuration required
- **Authentication**: Consider adding API key authentication

This implementation provides a complete, production-ready integration with your SIP server for call recording management and playback.
