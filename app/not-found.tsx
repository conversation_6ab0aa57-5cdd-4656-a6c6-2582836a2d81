"use client";

import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card";
import { FileQuestion, Home, ArrowLeft } from "lucide-react";

export default function NotFound() {
  return (
    <div className="flex min-h-screen items-center justify-center bg-background p-4">
      <Card className="mx-auto max-w-md w-full shadow-lg">
        <CardHeader className="space-y-1">
          <div className="flex items-center gap-2">
            <FileQuestion className="size-6 text-primary" />
            <CardTitle className="text-2xl">Page Not Found</CardTitle>
          </div>
          <CardDescription className="text-base">
            We couldn't find the page you were looking for. It might have been moved, deleted, or never existed.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <div className="text-center">
              <p className="text-8xl font-bold text-primary">404</p>
              <p className="mt-2 text-muted-foreground">The page you requested could not be found</p>
            </div>
          </div>
        </CardContent>
        <CardFooter className="flex flex-col sm:flex-row gap-2">
          <Button
            variant="outline"
            className="w-full sm:w-auto"
            onClick={() => window.history.back()}
          >
            <ArrowLeft className="mr-2 size-4" />
            Go Back
          </Button>
          <Button
            className="w-full sm:w-auto"
            asChild
          >
            <Link href="/dashboard">
              <Home className="mr-2 size-4" />
              Dashboard
            </Link>
          </Button>
        </CardFooter>
      </Card>
    </div>
  );
}
