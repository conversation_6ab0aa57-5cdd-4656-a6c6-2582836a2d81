import { NextResponse } from 'next/server';
import { socketService, SocketEvent } from '@/lib/services/socket-service';

/**
 * POST /api/inbound-call
 * SIP webhook endpoint that receives call data and emits WebSocket events
 */
export async function POST(request) {
  try {
    // Parse the incoming JSON payload
    const callData = await request.json();
    
    // Basic validation of required fields
    const requiredFields = [
      'callId',
      'channelId', 
      'callerNumber',
      'callerName',
      'destinationNumber',
      'direction',
      'status',
      'startTime',
      'agentExtension'
    ];
    
    // Check if all required fields are present
    const missingFields = requiredFields.filter(field => !callData[field]);
    if (missingFields.length > 0) {
      return NextResponse.json(
        { 
          error: 'Missing required fields', 
          missingFields 
        },
        { status: 400 }
      );
    }
    
    // Create incoming call object
    const incomingCall = {
      id: callData.callId, // Use SIP's callId as our id
      callerNumber: callData.callerNumber,
      callerName: callData.callerName,
      channelNumber: callData.channelId,
      timestamp: callData.startTime,
      status: 'ringing',
      direction: callData.direction,
      agentExtension: callData.agentExtension,
      metadata: callData.metadata || {}
    };

    // Emit WebSocket event for real-time notification
    if (socketService.isConnected()) {
      socketService.emit(SocketEvent.INCOMING_CALL, incomingCall);
    }
    
    // Log successful webhook processing
    console.log(`SIP webhook processed successfully for call ${callData.callId}`);
    
    // Return success response
    return NextResponse.json(
      {
        success: true,
        message: 'Call notification sent successfully',
        callId: callData.callId
      },
      { status: 200 }
    );
    
  } catch (error) {
    console.error('SIP webhook error:', error);
    
    // Handle JSON parsing errors
    if (error instanceof SyntaxError) {
      return NextResponse.json(
        { 
          error: 'Invalid JSON payload' 
        },
        { status: 400 }
      );
    }
    
    // Return generic error
    return NextResponse.json(
      { 
        error: 'Internal server error',
        details: error.message
      },
      { status: 500 }
    );
  }
}

/**
 * GET /api/inbound-call
 * Health check endpoint
 */
export async function GET() {
  return NextResponse.json({ status: 'healthy' });
}

/**
 * Handle unsupported HTTP methods
 */
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 204,
    headers: {
      'Allow': 'GET, POST, OPTIONS',
      'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type'
    }
  });
}