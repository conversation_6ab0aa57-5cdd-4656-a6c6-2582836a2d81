import { NextRequest, NextResponse } from 'next/server';

/**
 * Mock SIP Recording API Endpoint
 * 
 * This endpoint simulates your SIP server's recording API for testing purposes.
 * Replace this with actual SIP server integration when ready.
 */

// Mock recording data that matches your SIP server format
const mockRecordings = [
  "in-+254709918000-716038129-20250528-134749-1748440069.72.wav",
  "in-+254709918000-716038129-20250528-134801-1748440081.73.wav",
  "in-+254712345678-716038130-20250528-135000-1748440100.74.wav",
  "out-+254798765432-716038131-20250528-135500-1748440200.75.wav",
  "in-+254701234567-716038132-20250528-140000-1748440300.76.wav",
  "in-+254709918000-716038133-20250528-140500-1748440400.77.wav",
  "out-+254712345678-716038134-20250528-141000-1748440500.78.wav",
  "in-+254798765432-716038135-20250528-141500-1748440600.79.wav",
];

export async function GET(request: NextRequest) {
  try {
    // Simulate network delay
    await new Promise(resolve => setTimeout(resolve, 500));

    // Log the request for debugging
    console.log('Mock SIP API: Fetching recordings');

    // Return the mock recordings array
    return NextResponse.json(mockRecordings, {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
        // Add CORS headers if needed
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      },
    });
  } catch (error) {
    console.error('Mock SIP API error:', error);
    return NextResponse.json(
      { error: 'Failed to fetch recordings' },
      { status: 500 }
    );
  }
}

// Handle OPTIONS requests for CORS
export async function OPTIONS(request: NextRequest) {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
