import { NextRequest, NextResponse } from 'next/server';

/**
 * Mock SIP Recording File Endpoint
 * 
 * This endpoint simulates serving actual recording files.
 * In production, this would be handled by your SIP server.
 */

export async function GET(
  request: NextRequest,
  { params }: { params: { filename: string } }
) {
  try {
    const filename = params.filename;
    
    console.log('Mock SIP File API: Requesting file:', filename);

    // For demo purposes, we'll redirect to a sample audio file
    // In production, this would serve the actual recording file
    
    // You can replace this with a path to a sample audio file in your public directory
    // For now, we'll return a 404 with helpful information
    
    return NextResponse.json(
      { 
        error: 'Mock endpoint - file serving not implemented',
        filename: filename,
        message: 'This is a mock endpoint. In production, this would serve the actual recording file from your SIP server.',
        suggestedUrl: `http://your-sip-server/recordings/${filename}`
      },
      { 
        status: 404,
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*',
        }
      }
    );
  } catch (error) {
    console.error('Mock SIP File API error:', error);
    return NextResponse.json(
      { error: 'Failed to serve recording file' },
      { status: 500 }
    );
  }
}

// Handle OPTIONS requests for CORS
export async function OPTIONS(request: NextRequest) {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
