"use client";

import { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { History, BarChart, ArrowLeft, HelpCircle, Phone, FileAudio } from "lucide-react";
import { simulateIncomingCall } from "@/lib/services/socket-service";
import {
  ActiveCall,
  CallHistory,
  SystemLogs,
  CallSimulator
} from "@/components/calls";
import { SipRecordingDebug } from "@/components/calls/SipRecordingDebug";
// Temporarily disabled to reduce console spam
// import { WebRTCStatus } from "@/components/calls/WebRTCStatus";
// import { SipConnectionTester } from "@/components/calls/SipConnectionTester";
import { useCallHandler } from "@/lib/hooks/useCallHandler";
import { DraggableFAQModal, useDraggableFAQModal } from "@/components/faqs/draggable-faq-modal";
// <PERSON>mporarily disabled to reduce console spam
// import { useSipRecordings } from "@/lib/hooks/useSipRecordings";
// import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
// import { FileAudio, RefreshCw } from "lucide-react";
import Link from "next/link";

export default function CallsPage() {
  const { currentCall, callHistory } = useCallHandler();
  // Temporarily disabled to reduce console spam and API calls
  // const { stats: recordingStats, loading: recordingsLoading, error: recordingsError, refetch: refetchRecordings } = useSipRecordings();
  const [activeTab, setActiveTab] = useState("history");
  const { isOpen: isFAQOpen, openModal: openFAQModal, closeModal: closeFAQModal } = useDraggableFAQModal();

  return (
    <div className="container px-10 py-6 space-y-6">
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Inbound Calls</h1>
          <p className="text-muted-foreground">Manage incoming calls and view call history</p>
        </div>

        <div className="flex gap-2">
          <Button
            variant="outline"
            onClick={() => simulateIncomingCall()}
          >
            <Phone className="mr-2 size-4" />
            Test Call
          </Button>
          <Button
            variant="outline"
            onClick={() => openFAQModal({ x: 200, y: 150 })}
          >
            <HelpCircle className="mr-2 size-4" />
            Product FAQs
          </Button>
          <Link href="/dashboard">
            <Button variant="outline">
              <ArrowLeft className="mr-2 size-4" />
              Dashboard
            </Button>
          </Link>
        </div>
      </div>

      {/* SIP Recording Statistics - Temporarily disabled to reduce console spam and API calls */}
      {/* <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Recordings</CardTitle>
            <FileAudio className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {recordingsLoading ? '...' : recordingStats?.total || 0}
            </div>
            <p className="text-xs text-muted-foreground">
              Available in SIP server
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Inbound Calls</CardTitle>
            <Phone className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {recordingsLoading ? '...' : recordingStats?.inbound || 0}
            </div>
            <p className="text-xs text-muted-foreground">
              Recorded incoming calls
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Today's Recordings</CardTitle>
            <BarChart className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {recordingsLoading ? '...' : recordingStats?.today || 0}
            </div>
            <p className="text-xs text-muted-foreground">
              Recorded today
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">SIP Server Status</CardTitle>
            <Button
              variant="ghost"
              size="icon"
              className="h-4 w-4"
              onClick={refetchRecordings}
              disabled={recordingsLoading}
            >
              <RefreshCw className={`h-4 w-4 ${recordingsLoading ? 'animate-spin' : ''}`} />
            </Button>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {recordingsError ? (
                <span className="text-destructive text-sm">Error</span>
              ) : (
                <span className="text-green-600">Online</span>
              )}
            </div>
            <p className="text-xs text-muted-foreground">
              {recordingsError ? recordingsError : 'Connected to SIP server'}
            </p>
          </CardContent>
        </Card>
      </div> */}

      {/* WebRTC Status - Temporarily disabled to reduce console spam */}
      {/* <div className="mb-6">
        <WebRTCStatus />
      </div> */}

      {/* SIP Connection Tester - Temporarily disabled to reduce console spam */}
      {/* <div className="mb-6">
        <SipConnectionTester />
      </div> */}

      {/* Call Simulator */}
      {/* <div className="mb-6 p-4 border rounded-lg bg-card">
        <h2 className="text-xl font-semibold mb-4">Call Simulator</h2>
        <CallSimulator />
      </div> */}

      {/* Show active call if there is one */}
      {currentCall && currentCall.status === 'answered' && (
        <div className="mb-6">
          <ActiveCall call={currentCall} />
        </div>
      )}

      {/* Incoming calls are now handled globally by GlobalCallNotificationProvider */}

      <Tabs defaultValue="history" value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="history">
            <History className="mr-2 size-4" />
            Call History
          </TabsTrigger>
          <TabsTrigger value="logs">
            <BarChart className="mr-2 size-4" />
            Call Logs
          </TabsTrigger>
          <TabsTrigger value="debug">
            <FileAudio className="mr-2 size-4" />
            SIP Debug
          </TabsTrigger>
        </TabsList>

        <TabsContent value="history" className="mt-6">
          <CallHistory calls={callHistory} />
        </TabsContent>

        <TabsContent value="logs" className="mt-6">
          <SystemLogs />
        </TabsContent>

        <TabsContent value="debug" className="mt-6">
          <SipRecordingDebug />
        </TabsContent>
      </Tabs>

      {/* Draggable FAQ Modal */}
      <DraggableFAQModal
        isOpen={isFAQOpen}
        onClose={closeFAQModal}
        productId={currentCall?.productName ? `prod-${currentCall.productName.toLowerCase().replace(/\s+/g, '-')}` : undefined}
      />
    </div>
  );
}
