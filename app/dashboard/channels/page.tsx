"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Plus } from "lucide-react"
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Alert, AlertTitle, AlertDescription } from "@/components/ui/alert";
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { Textarea } from "@/components/ui/textarea"
import {
  useChannels,
  useCreateChannel,
  useDeleteChannel,
  useUpdateChannel,
} from "@/lib/hooks/useChannels"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { Channel } from "@/lib/api"
import { createChannelSchema, type CreateChannelFormValues } from "@/lib/validations/channels"

function CreateChannelDialog({
  open,
  onOpenChange,
}: {
  open: boolean
  onOpenChange: (open: boolean) => void
}) {
  const { register, handleSubmit, reset, formState: { errors } } = useForm<CreateChannelFormValues>({
    resolver: zodResolver(createChannelSchema),
    defaultValues: {
      name: "",
      type: "",
      description: "",
      is_active: true,
    },
  })

  const createChannel = useCreateChannel()

  const onSubmit = async (data: CreateChannelFormValues) => {
    try {
      await createChannel.mutateAsync(data)
      reset()
      onOpenChange(false)
    } catch (error) {
      console.error("Failed to create channel:", error)
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>
            Create Channel
          </DialogTitle>
          <DialogDescription>
            Add a new channel for organizing tickets
          </DialogDescription>
        </DialogHeader>
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="name">Name</Label>
            <Input
              id="name"
              {...register("name")}
              placeholder="Enter category name"
            />
            {errors.name && (
              <p className="text-sm text-destructive">{errors.name.message}</p>
            )}
          </div>
          <div className="space-y-2">
            <Label htmlFor="description">Description</Label>
            <Textarea
              id="description"
              {...register("description")}
              placeholder="Enter category description"
            />
            {errors.description && (
              <p className="text-sm text-destructive">
                {errors.description.message}
              </p>
            )}
          </div>
          <div className="space-y-2">
            <Label htmlFor="type">Type</Label>
            <Input
              id="type"
              {...register("type")}
              placeholder="Enter channel type"
            />
            {errors.type && (
              <p className="text-sm text-destructive">{errors.type.message}</p>
            )}
          </div>
          <div className="flex items-center space-x-2">
            <Switch
              id="is_active"
              {...register("is_active")}
              defaultChecked
            />
            <Label htmlFor="is_active">Active</Label>
          </div>
          <div className="flex justify-end space-x-2">
            <Button
              type="button"
              variant="outline"
              onClick={() => onOpenChange(false)}
            >
              Cancel
            </Button>
            <Button type="submit">
              Create Channel
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  )
}

function ChannelCard({ channel }: { channel: Channel }) {
  const deleteChannel = useDeleteChannel()
  const updateChannel = useUpdateChannel()

  const handleDelete = async () => {
    if (
      window.confirm(
        `Are you sure you want to delete ${channel.name}? This action cannot be undone.`
      )
    ) {
      try {
        await deleteChannel.mutateAsync(channel.id)
      } catch (error) {
        console.error("Failed to delete category:", error)
      }
    }
  }

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <div>
          <CardTitle className="text-2xl font-bold">{channel.name}</CardTitle>
          <CardDescription>{channel.description}</CardDescription>
        </div>
        <div className="flex space-x-2">
          <Button
            variant="destructive"
            size="sm"
            onClick={handleDelete}
          >
            Delete
          </Button>
        </div>
      </CardHeader>
    </Card>
  )
}

export default function ChannelsPage() {
  const [isCreateChannelOpen, setIsCreateChannelOpen] = useState(false)
  const { data: channels, isLoading, error } = useChannels()
  let errorMessage = "Error loading categories. Please try again.";

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-32">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary" />
      </div>
    )
  }

  if (error) {
    if (
      error instanceof TypeError &&
      (error.message.includes("Failed to fetch") ||
        error.message.includes("NetworkError") ||
        error.message.includes("ERR_CONNECTION_REFUSED"))
    ) {
      errorMessage = "System is offline. Please check your connection or contact support.";
    }
  }

  return (
    <>
    {error && (
        <Alert variant="destructive">
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>{errorMessage}</AlertDescription>
        </Alert>
      )}
    <div className="flex-1 space-y-4 p-4 md:p-8 pt-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">
            Channel Management
          </h2>
          <p className="text-muted-foreground">
            Manage channels
          </p>
        </div>
        <Button onClick={() => setIsCreateChannelOpen(true)}>
          <Plus className="mr-2 size-4" />
          Create Channel
        </Button>
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {channels?.map((channel) => (
          <ChannelCard key={channel.id} channel={channel} />
        ))}
      </div>

      <CreateChannelDialog
        open={isCreateChannelOpen}
        onOpenChange={setIsCreateChannelOpen}
      />
    </div>
    </>
  )
}
