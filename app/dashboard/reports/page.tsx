"use client";

import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTit<PERSON> } from "@/components/ui/card";
import { Ta<PERSON>, Ta<PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { 
  Download, 
  Filter, 
  Calendar, 
  BarChart3, 
  TrendingUp, 
  Users, 
  Phone, 
  Ticket,
  FileText,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Target
} from "lucide-react";
import { PeriodSelector } from "@/components/dashboard/period-selector";
import { EnhancedCallVolume } from "@/components/dashboard/enhanced-call-volume";
import { EnhancedAgentPerformance } from "@/components/dashboard/enhanced-agent-performance";
import { useAdvancedFilters } from '@/lib/stores/filter-store';
import { useWorkspace } from '@/lib/stores/workspace-store';

// Mock report data
const reportMetrics = {
  callAnalytics: {
    totalCalls: 1247,
    inboundCalls: 892,
    outboundCalls: 355,
    missedCalls: 67,
    averageWaitTime: 42,
    averageHandleTime: 187,
    peakHour: '4 PM',
    answerRate: 92.5,
    abandonmentRate: 5.4,
  },
  productPerformance: {
    topProduct: 'Internet Fiber',
    totalTickets: 456,
    resolvedTickets: 398,
    averageResolutionTime: 125,
    customerSatisfaction: 4.6,
    escalationRate: 8.2,
  },
  ticketAnalytics: {
    totalTickets: 1156,
    openTickets: 89,
    inProgressTickets: 156,
    resolvedTickets: 911,
    averageResolutionTime: 142,
    firstContactResolution: 78.5,
    escalatedTickets: 95,
  },
  agentPerformance: {
    totalAgents: 24,
    activeAgents: 18,
    topPerformer: 'Sarah Johnson',
    averageTicketsPerAgent: 48,
    averageResolutionTime: 135,
    teamSatisfactionScore: 4.5,
  }
};

export default function ReportsPage() {
  const [selectedReport, setSelectedReport] = useState<string>('overview');
  const [exportFormat, setExportFormat] = useState<'pdf' | 'excel' | 'csv'>('pdf');
  const { filters } = useAdvancedFilters();
  const { currentWorkspace } = useWorkspace();

  const handleExport = (format: 'pdf' | 'excel' | 'csv') => {
    // Implementation for export functionality
    console.log(`Exporting ${selectedReport} report as ${format}`);
    // In a real app, this would trigger the export process
  };

  return (
    <div className="flex-1 space-y-6 p-4 md:p-8 pt-6">
      {/* Header */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Reports Hub</h1>
            <p className="text-muted-foreground">
              Comprehensive analytics and reporting for {currentWorkspace?.name || 'All Products'}
            </p>
          </div>
          <div className="flex items-center gap-2">
            <Button variant="outline" onClick={() => handleExport(exportFormat)}>
              <Download className="h-4 w-4 mr-2" />
              Export {exportFormat.toUpperCase()}
            </Button>
          </div>
        </div>

        {/* Period Selector */}
        <PeriodSelector />
      </div>

      {/* Report Tabs */}
      <Tabs value={selectedReport} onValueChange={setSelectedReport} className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview" className="flex items-center gap-2">
            <BarChart3 className="h-4 w-4" />
            Overview
          </TabsTrigger>
          <TabsTrigger value="calls" className="flex items-center gap-2">
            <Phone className="h-4 w-4" />
            Call Analytics
          </TabsTrigger>
          <TabsTrigger value="tickets" className="flex items-center gap-2">
            <Ticket className="h-4 w-4" />
            Ticket Analytics
          </TabsTrigger>
          <TabsTrigger value="agents" className="flex items-center gap-2">
            <Users className="h-4 w-4" />
            Agent Performance
          </TabsTrigger>
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value="overview" className="space-y-6">
          {/* KPI Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Calls</CardTitle>
                <Phone className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{reportMetrics.callAnalytics.totalCalls.toLocaleString()}</div>
                <div className="flex items-center gap-1 text-xs text-muted-foreground">
                  <TrendingUp className="h-3 w-3 text-green-500" />
                  <span>+12.5% from last period</span>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Tickets</CardTitle>
                <Ticket className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{reportMetrics.ticketAnalytics.totalTickets.toLocaleString()}</div>
                <div className="flex items-center gap-1 text-xs text-muted-foreground">
                  <TrendingUp className="h-3 w-3 text-green-500" />
                  <span>+8.3% from last period</span>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Active Agents</CardTitle>
                <Users className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {reportMetrics.agentPerformance.activeAgents}/{reportMetrics.agentPerformance.totalAgents}
                </div>
                <div className="flex items-center gap-1 text-xs text-muted-foreground">
                  <span>{((reportMetrics.agentPerformance.activeAgents / reportMetrics.agentPerformance.totalAgents) * 100).toFixed(1)}% availability</span>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Resolution Rate</CardTitle>
                <Target className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {((reportMetrics.ticketAnalytics.resolvedTickets / reportMetrics.ticketAnalytics.totalTickets) * 100).toFixed(1)}%
                </div>
                <div className="flex items-center gap-1 text-xs text-muted-foreground">
                  <TrendingUp className="h-3 w-3 text-green-500" />
                  <span>+3.2% from last period</span>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Overview Charts */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Call Volume Overview</CardTitle>
              </CardHeader>
              <CardContent className="p-0">
                <EnhancedCallVolume showComparative={false} allowDrillDown={false} />
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Top Agent Performance</CardTitle>
              </CardHeader>
              <CardContent className="p-0">
                <EnhancedAgentPerformance showTrends={false} maxAgents={5} />
              </CardContent>
            </Card>
          </div>

          {/* Summary Statistics */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Call Performance</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-sm">Answer Rate</span>
                  <Badge variant="default">{reportMetrics.callAnalytics.answerRate}%</Badge>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm">Avg Wait Time</span>
                  <Badge variant="outline">{reportMetrics.callAnalytics.averageWaitTime}s</Badge>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm">Abandonment Rate</span>
                  <Badge variant="destructive">{reportMetrics.callAnalytics.abandonmentRate}%</Badge>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm">Peak Hour</span>
                  <Badge variant="secondary">{reportMetrics.callAnalytics.peakHour}</Badge>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Ticket Performance</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-sm">First Contact Resolution</span>
                  <Badge variant="default">{reportMetrics.ticketAnalytics.firstContactResolution}%</Badge>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm">Avg Resolution Time</span>
                  <Badge variant="outline">{reportMetrics.ticketAnalytics.averageResolutionTime}m</Badge>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm">Escalation Rate</span>
                  <Badge variant="destructive">{((reportMetrics.ticketAnalytics.escalatedTickets / reportMetrics.ticketAnalytics.totalTickets) * 100).toFixed(1)}%</Badge>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm">Open Tickets</span>
                  <Badge variant="secondary">{reportMetrics.ticketAnalytics.openTickets}</Badge>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Product Performance</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-sm">Top Product</span>
                  <Badge variant="default">{reportMetrics.productPerformance.topProduct}</Badge>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm">Customer Satisfaction</span>
                  <Badge variant="outline">{reportMetrics.productPerformance.customerSatisfaction}/5</Badge>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm">Resolution Rate</span>
                  <Badge variant="default">{((reportMetrics.productPerformance.resolvedTickets / reportMetrics.productPerformance.totalTickets) * 100).toFixed(1)}%</Badge>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm">Avg Resolution</span>
                  <Badge variant="secondary">{reportMetrics.productPerformance.averageResolutionTime}m</Badge>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Call Analytics Tab */}
        <TabsContent value="calls" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Comprehensive Call Analytics</CardTitle>
              <div className="flex gap-2">
                <Button variant="outline" size="sm" onClick={() => handleExport('excel')}>
                  <FileText className="h-4 w-4 mr-1" />
                  Export Data
                </Button>
              </div>
            </CardHeader>
            <CardContent className="p-0">
              <EnhancedCallVolume showComparative={true} allowDrillDown={true} />
            </CardContent>
          </Card>
        </TabsContent>

        {/* Ticket Analytics Tab */}
        <TabsContent value="tickets" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
            <Card>
              <CardContent className="p-4">
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-600">{reportMetrics.ticketAnalytics.totalTickets}</div>
                  <div className="text-sm text-muted-foreground">Total Tickets</div>
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4">
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-600">{reportMetrics.ticketAnalytics.resolvedTickets}</div>
                  <div className="text-sm text-muted-foreground">Resolved</div>
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4">
                <div className="text-center">
                  <div className="text-2xl font-bold text-yellow-600">{reportMetrics.ticketAnalytics.inProgressTickets}</div>
                  <div className="text-sm text-muted-foreground">In Progress</div>
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4">
                <div className="text-center">
                  <div className="text-2xl font-bold text-red-600">{reportMetrics.ticketAnalytics.openTickets}</div>
                  <div className="text-sm text-muted-foreground">Open</div>
                </div>
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>Ticket Analytics Dashboard</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8 text-muted-foreground">
                <Ticket className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <p>Detailed ticket analytics charts and tables would be implemented here</p>
                <p className="text-sm">Including resolution trends, category breakdown, and SLA compliance</p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Agent Performance Tab */}
        <TabsContent value="agents" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Detailed Agent Performance Analysis</CardTitle>
              <div className="flex gap-2">
                <Button variant="outline" size="sm" onClick={() => handleExport('pdf')}>
                  <FileText className="h-4 w-4 mr-1" />
                  Performance Report
                </Button>
              </div>
            </CardHeader>
            <CardContent className="p-0">
              <EnhancedAgentPerformance showTrends={true} maxAgents={20} />
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
