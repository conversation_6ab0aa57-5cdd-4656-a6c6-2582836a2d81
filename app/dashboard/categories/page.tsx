"use client"

import React, { useState, useEffect } from "react"
import { fromZodError } from "zod-validation-error"
import { But<PERSON> } from "@/components/ui/button"
import { Plus, Pencil, Trash2, MoreHorizontal, ShieldAlert } from "lucide-react"
import { usePermissions } from "@/lib/hooks/usePermissions"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"

import { Textarea } from "@/components/ui/textarea"
import {
  useCategories,
  useCreateCategory,
  useCreateSubcategory,
  useDeleteCategory,
  useSubcategories,
  useUpdateCategory,
} from "@/lib/hooks/useCategories"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import {
  createCategorySchema,
  type CreateCategoryFormValues,
  updateCategorySchema,
  type UpdateCategoryFormValues,
} from "@/lib/validations/tickets"
import { toast } from "sonner"
import { Category } from "@/lib/api"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"

function CreateCategoryDialog({
  parentId,
  open,
  onOpenChange,
}: {
  parentId?: string
  open: boolean
  onOpenChange: (open: boolean) => void
}) {
  const { register, handleSubmit, reset, formState: { errors } } = useForm<CreateCategoryFormValues>({
    resolver: zodResolver(createCategorySchema),
    defaultValues: {
      name: "",
      description: "",
    },
  })

  // Fetch parent category name if creating a subcategory
  const { data: categories } = useCategories()
  const parentCategory = parentId ? categories?.find(c => c.id === parentId) : null

  const createCategory = useCreateCategory()
  const createSubcategory = useCreateSubcategory(parentId || "")

  const [formError, setFormError] = useState<string | null>(null)

  const onSubmit = async (data: CreateCategoryFormValues) => {
    try {
      setFormError(null)
      if (parentId) {
        await createSubcategory.mutateAsync(data)
      } else {
        await createCategory.mutateAsync(data)
      }
      toast.success(`${parentId ? "Subcategory" : "Category"} created successfully`)
      reset()
      onOpenChange(false)
    } catch (error) {
      console.error("Failed to create category:", error)

      // Handle validation errors
      if (error instanceof Error) {
        // Try to parse as Zod error
        try {
          const zodError = fromZodError(error as any);
          setFormError(zodError.message);
        } catch (e) {
          // Not a Zod error
          setFormError(error.message || "An error occurred while creating the category");
        }
      } else {
        setFormError("An unexpected error occurred");
      }

      toast.error(`Failed to create ${parentId ? "subcategory" : "category"}`);
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>
            Create {parentId ? "Subcategory" : "Category"}
          </DialogTitle>
          <DialogDescription>
            Add a new {parentId ? "subcategory" : "category"} for organizing tickets
          </DialogDescription>
        </DialogHeader>
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
          {formError && (
            <div className="bg-destructive/15 text-destructive text-sm p-3 rounded-md">
              {formError}
            </div>
          )}
          <div className="space-y-2">
            <Label htmlFor="name">Name</Label>
            <Input
              id="name"
              {...register("name")}
              placeholder="Enter category name"
            />
            {errors.name && (
              <p className="text-sm text-destructive">{errors.name.message}</p>
            )}
          </div>
          {/* Parent category - only show if creating a subcategory */}
          {parentId && parentCategory && (
            <div className="space-y-2">
              <Label htmlFor="parent_category">Parent Category</Label>
              <Input
                id="parent_category"
                value={parentCategory.name}
                disabled
                className="bg-muted"
              />
            </div>
          )}
          <div className="space-y-2">
            <Label htmlFor="description">Description</Label>
            <Textarea
              id="description"
              {...register("description")}
              placeholder="Enter category description"
            />
            {errors.description && (
              <p className="text-sm text-destructive">
                {errors.description.message}
              </p>
            )}
          </div>

          <div className="flex justify-end space-x-2">
            <Button
              type="button"
              variant="outline"
              onClick={() => onOpenChange(false)}
            >
              Cancel
            </Button>
            <Button type="submit">
              Create {parentId ? "Subcategory" : "Category"}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  )
}

function EditCategoryDialog({
  category,
  open,
  onOpenChange,
}: {
  category: Category | null
  open: boolean
  onOpenChange: (open: boolean) => void
}) {
  const { register, handleSubmit, reset, formState: { errors }, setValue } = useForm<UpdateCategoryFormValues>({
    resolver: zodResolver(updateCategorySchema),
    defaultValues: {
      name: category?.name || "",
      description: category?.description || "",
      parent_id: category?.parent_id || null,
    },
  })

  // Fetch all categories for parent selection
  const { data: categories } = useCategories()
  const parentCategories = categories?.filter(c => !c.parent_id && c.id !== category?.id) || []



  // Update form values when category changes
  useEffect(() => {
    if (category) {
      reset({
        name: category.name,
        description: category.description || "",
        parent_id: category.parent_id,
      })
    }
  }, [category, reset])

  const updateCategory = useUpdateCategory()

  const [formError, setFormError] = useState<string | null>(null)

  const onSubmit = async (data: UpdateCategoryFormValues) => {
    if (!category) return

    try {
      setFormError(null)
      await updateCategory.mutateAsync({
        id: category.id,
        data: {
          name: data.name,
          description: data.description,
          parent_id: data.parent_id
        }
      })
      reset()
      onOpenChange(false)
      toast.success("Category updated successfully")
    } catch (error) {
      console.error("Failed to update category:", error)

      // Handle validation errors
      if (error instanceof Error) {
        // Try to parse as Zod error
        try {
          const zodError = fromZodError(error as any);
          setFormError(zodError.message);
        } catch (e) {
          // Not a Zod error
          setFormError(error.message || "An error occurred while updating the category");
        }
      } else {
        setFormError("An unexpected error occurred");
      }

      toast.error("Failed to update category")
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>
            Edit {category?.parent_id ? "Subcategory" : "Category"}
          </DialogTitle>
          <DialogDescription>
            Update {category?.parent_id ? "subcategory" : "category"} details
          </DialogDescription>
        </DialogHeader>
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
          {formError && (
            <div className="bg-destructive/15 text-destructive text-sm p-3 rounded-md">
              {formError}
            </div>
          )}
          <div className="space-y-2">
            <Label htmlFor="edit-name">Name</Label>
            <Input
              id="edit-name"
              {...register("name")}
              placeholder="Enter category name"
            />
            {errors.name && (
              <p className="text-sm text-destructive">{errors.name.message}</p>
            )}
          </div>
          <div className="space-y-2">
            <Label htmlFor="edit-description">Description</Label>
            <Textarea
              id="edit-description"
              {...register("description")}
              placeholder="Enter category description"
            />
            {errors.description && (
              <p className="text-sm text-destructive">
                {errors.description.message}
              </p>
            )}
          </div>

          {/* Parent category selection - only for subcategories */}
          {category?.parent_id && (
            <div className="space-y-2">
              <Label htmlFor="edit-parent_id">Parent Category</Label>
              <Select
                onValueChange={(value) => setValue("parent_id", value)}
                defaultValue={category?.parent_id || undefined}
              >
                <SelectTrigger id="edit-parent_id">
                  <SelectValue placeholder="Select parent category" />
                </SelectTrigger>
                <SelectContent>
                  {parentCategories.map((parentCategory) => (
                    <SelectItem key={parentCategory.id} value={parentCategory.id}>
                      {parentCategory.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {errors.parent_id && (
                <p className="text-sm text-destructive">
                  {errors.parent_id.message}
                </p>
              )}
            </div>
          )}

          <div className="flex justify-end space-x-2">
            <Button
              type="button"
              variant="outline"
              onClick={() => onOpenChange(false)}
            >
              Cancel
            </Button>
            <Button type="submit">
              Update {category?.parent_id ? "Subcategory" : "Category"}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  )
}

function CategoryRow({
  category,
  onEdit,
  onAddSubcategory,
  isAdmin = false
}: {
  category: Category,
  onEdit: (category: Category) => void,
  onAddSubcategory: (category: Category) => void,
  isAdmin?: boolean
}) {
  const deleteCategory = useDeleteCategory()
  const { data: subcategories } = useSubcategories(category.id)
  const subcategoryCount = subcategories?.length || 0

  const handleDelete = async () => {
    if (
      window.confirm(
        `Are you sure you want to delete ${category.name}? This action cannot be undone.`
      )
    ) {
      try {
        await deleteCategory.mutateAsync(category.id)
        toast.success("Category deleted successfully")
      } catch (error) {
        console.error("Failed to delete category:", error)
        toast.error("Failed to delete category")
      }
    }
  }

  return (
    <TableRow>
      <TableCell className="font-medium">{category.name}</TableCell>
      <TableCell>{category.description || "—"}</TableCell>
      <TableCell>{subcategoryCount}</TableCell>
      <TableCell className="text-right">
        {isAdmin ? (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="h-8 w-8 p-0">
                <span className="sr-only">Open menu</span>
                <MoreHorizontal className="size-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={() => onEdit(category)}>
                <Pencil className="mr-2 size-4" />
                Edit
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => onAddSubcategory(category)}>
                <Plus className="mr-2 size-4" />
                Add Subcategory
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={handleDelete}
                className="text-destructive focus:text-destructive"
              >
                <Trash2 className="mr-2 size-4" />
                Delete
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        ) : (
          <span className="text-xs text-muted-foreground">No actions available</span>
        )}
      </TableCell>
    </TableRow>
  )
}

function SubcategoryRow({
  subcategory,
  isAdmin = false
}: {
  subcategory: Category,
  isAdmin?: boolean
}) {
  const deleteCategory = useDeleteCategory()

  const handleDelete = async () => {
    if (
      window.confirm(
        `Are you sure you want to delete ${subcategory.name}? This action cannot be undone.`
      )
    ) {
      try {
        await deleteCategory.mutateAsync(subcategory.id)
        toast.success("Subcategory deleted successfully")
      } catch (error) {
        console.error("Failed to delete subcategory:", error)
        toast.error("Failed to delete subcategory")
      }
    }
  }

  return (
    <TableRow>
      <TableCell className="pl-10 font-medium">{subcategory.name}</TableCell>
      <TableCell>{subcategory.description || "—"}</TableCell>
      <TableCell>—</TableCell>
      <TableCell className="text-right">
        {isAdmin ? (
          <Button
            variant="ghost"
            size="sm"
            onClick={handleDelete}
            className="h-8 w-8 p-0"
          >
            <Trash2 className="size-4" />
            <span className="sr-only">Delete</span>
          </Button>
        ) : (
          <span className="text-xs text-muted-foreground">No actions available</span>
        )}
      </TableCell>
    </TableRow>
  )
}

export default function CategoriesPage() {
  const [isCreateCategoryOpen, setIsCreateCategoryOpen] = useState(false)
  const [isEditCategoryOpen, setIsEditCategoryOpen] = useState(false)
  const [isCreateSubcategoryOpen, setIsCreateSubcategoryOpen] = useState(false)
  const [selectedCategory, setSelectedCategory] = useState<Category | null>(null)
  const { data: categories, isLoading, error } = useCategories()
  const { hasPermission } = usePermissions()

  // Check if user has admin access
  const isAdmin = hasPermission('admin:access')

  const handleEditCategory = (category: Category) => {
    setSelectedCategory(category)
    setIsEditCategoryOpen(true)
  }

  const handleAddSubcategory = (category: Category) => {
    setSelectedCategory(category)
    setIsCreateSubcategoryOpen(true)
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-32">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary" />
      </div>
    )
  }

  if (error) {
    return (
      <div className="flex items-center justify-center h-32 text-destructive">
        Error loading categories. Please try again.
      </div>
    )
  }

  // Group subcategories by parent_id
  const categoriesWithSubcategories = categories?.filter(c => !c.parent_id) || []

  return (
    <div className="flex-1 space-y-4 p-4 md:p-8 pt-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">
            Category Management
          </h2>
          <p className="text-muted-foreground">
            {isAdmin
              ? "Manage ticket categories and subcategories"
              : "View ticket categories and subcategories (admin access required for management)"}
          </p>
        </div>
        {isAdmin ? (
          <Button onClick={() => setIsCreateCategoryOpen(true)}>
            <Plus className="mr-2 size-4" />
            Create Category
          </Button>
        ) : (
          <Button variant="outline" disabled>
            <ShieldAlert className="mr-2 size-4" />
            Admin Access Required
          </Button>
        )}
      </div>

      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Name</TableHead>
              <TableHead>Description</TableHead>
              <TableHead>Subcategories</TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {categoriesWithSubcategories.length > 0 ? (
              <>
                {categoriesWithSubcategories.map((category) => (
                  <React.Fragment key={category.id}>
                    <CategoryRow
                      category={category}
                      onEdit={handleEditCategory}
                      onAddSubcategory={handleAddSubcategory}
                      isAdmin={isAdmin}
                    />
                    {/* Render subcategories */}
                    {categories?.filter(c => c.parent_id === category.id).map(subcategory => (
                      <SubcategoryRow
                        key={subcategory.id}
                        subcategory={subcategory}
                        isAdmin={isAdmin}
                      />
                    ))}
                  </React.Fragment>
                ))}
              </>
            ) : (
              <TableRow>
                <TableCell colSpan={4} className="h-24 text-center">
                  <p className="text-sm text-muted-foreground">No categories found.</p>
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>

      <CreateCategoryDialog
        open={isCreateCategoryOpen}
        onOpenChange={setIsCreateCategoryOpen}
      />

      <EditCategoryDialog
        category={selectedCategory}
        open={isEditCategoryOpen}
        onOpenChange={setIsEditCategoryOpen}
      />

      <CreateCategoryDialog
        parentId={selectedCategory?.id}
        open={isCreateSubcategoryOpen}
        onOpenChange={setIsCreateSubcategoryOpen}
      />
    </div>
  )
}
