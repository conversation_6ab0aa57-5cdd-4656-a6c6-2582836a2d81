"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { userService } from "@/lib/api/users"

export function DebugPanel() {
  const [apiResponse, setApiResponse] = useState<any>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const fetchUsers = async () => {
    setLoading(true)
    setError(null)
    try {
      const response = await userService.getAllUsers({})
      setApiResponse(response)
      console.log("API Response:", response)
    } catch (err: any) {
      setError(err.message || "An error occurred")
      console.error("API Error:", err)
    } finally {
      setLoading(false)
    }
  }

  return (
    <Card className="mb-4">
      <CardHeader>
        <CardTitle>API Debug Panel</CardTitle>
        <CardDescription>Test the users API endpoint</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="flex flex-col gap-4">
          <Button onClick={fetchUsers} disabled={loading}>
            {loading ? "Loading..." : "Fetch Users Directly"}
          </Button>
          
          {error && (
            <div className="p-4 bg-red-50 text-red-800 rounded-md">
              <p className="font-bold">Error:</p>
              <p>{error}</p>
            </div>
          )}
          
          {apiResponse && (
            <div className="p-4 bg-gray-50 rounded-md">
              <p className="font-bold">API Response:</p>
              <pre className="mt-2 text-xs overflow-auto max-h-[300px]">
                {JSON.stringify(apiResponse, null, 2)}
              </pre>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )
}
