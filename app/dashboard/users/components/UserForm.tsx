"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage, FormDescription } from "@/components/ui/form"
import {Checkbox} from "@/components/ui/checkbox"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Loader2 } from "lucide-react"
import { User } from "@/lib/api"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { createUserSchema, updateUserSchema, CreateUserFormValues, UpdateUserFormValues } from "@/lib/validations/users"
import { useEffect, useMemo } from "react"
import { useQuery } from "@tanstack/react-query"
import { roleService } from "@/lib/api/roles"

type UserFormProps = {
  type: 'create' | 'update'
  user?: User
  isSubmitting: boolean
  onSubmit: (data: CreateUserFormValues | UpdateUserFormValues) => void
  onCancel: () => void
}

type FormValues = CreateUserFormValues | UpdateUserFormValues

export function UserForm({ type, user, isSubmitting, onSubmit, onCancel }: UserFormProps) {
  const isCreateForm = type === 'create'

  // Fetch roles from the API
  const { data: rolesData, isLoading: isLoadingRoles, error: rolesError } = useQuery({
    queryKey: ['roles'],
    queryFn: roleService.getAllRoles,
  })

  // Debug logging
  useEffect(() => {
    console.log('UserForm - Roles data:', rolesData);
    console.log('UserForm - Is loading roles:', isLoadingRoles);
    console.log('UserForm - Roles error:', rolesError);
  }, [rolesData, isLoadingRoles, rolesError]);

  // Create dynamic role map from fetched roles
  const roleMap = useMemo(() => {
    if (!rolesData?.data) {
      console.log('UserForm - No roles data available');
      return {}
    }

    const map = rolesData.data.reduce<Record<string, string>>((acc, role) => {
      acc[role.id] = role.name
      return acc
    }, {})

    console.log('UserForm - Role map created:', map);
    return map;
  }, [rolesData])

  // Initialize the form for creating or updating users
  const createForm = useForm<CreateUserFormValues>({
    resolver: zodResolver(createUserSchema),
    defaultValues: {
      first_name: "",
      last_name: "",
      email: "",
      role_id: "",
      phone_number: "",
      // department_id: "",
      status: "INACTIVE", // New users are created with INACTIVE status
    },
  })

  const updateForm = useForm<UpdateUserFormValues>({
    resolver: zodResolver(updateUserSchema),
    defaultValues: {
      first_name: user?.first_name || "",
      last_name: user?.last_name || "",
      email: user?.email || "",
      role_id: user?.role_id ? user.role_id.toString() : "",
      // department_id: user?.department_id?.toString(),
      status: user?.status || "ACTIVE",
      phone_number: user?.phone_number || "",
    },
  })

  // Reset form when user changes
  useEffect(() => {
    if (!isCreateForm && user) {
      updateForm.reset({
        first_name: user.first_name,
        last_name: user.last_name,
        email: user.email,
        role_id: user.role_id ? user.role_id.toString() : "",
        // department_id: user.department_id?.toString(),
        status: user.status || "ACTIVE",
        phone_number: user.phone_number || "",
      })
    }
  }, [user, updateForm, isCreateForm])

  const handleSubmit = (data: FormValues) => onSubmit(data)

  return (
    <>
    {isCreateForm ? (
      <Form {...createForm}>
        <form onSubmit={createForm.handleSubmit(handleSubmit)} className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <FormField
              control={createForm.control}
              name="first_name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>First Name</FormLabel>
                  <FormControl>
                    <Input placeholder="Enter first name" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={createForm.control}
              name="last_name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Last Name</FormLabel>
                  <FormControl>
                    <Input placeholder="Enter last name" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          <FormField
            control={createForm.control}
            name="email"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Email</FormLabel>
                <FormControl>
                  <Input placeholder="Enter email" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={createForm.control}
            name="phone_number"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Phone Number</FormLabel>
                <FormControl>
                  <Input placeholder="Enter phone number" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <div className="grid grid-cols-2 gap-4">
            <FormField
              control={createForm.control}
              name="role_id"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Role</FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    value={field.value}
                    disabled={isLoadingRoles}
                  >
                    <FormControl>
                      <SelectTrigger>
                        {isLoadingRoles ? (
                          <div className="flex items-center gap-2">
                            <Loader2 className="size-4 animate-spin" />
                            <span>Loading roles...</span>
                          </div>
                        ) : (
                          <SelectValue placeholder="Select a role" />
                        )}
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {rolesError ? (
                        <div className="p-2 text-sm text-red-600">Error loading roles</div>
                      ) : Object.keys(roleMap).length === 0 ? (
                        <div className="p-2 text-sm text-muted-foreground">
                          {isLoadingRoles ? 'Loading roles...' : 'No roles available'}
                        </div>
                      ) : (
                        Object.entries(roleMap).map(([key, value]) => (
                          <SelectItem key={key} value={key}>{value}</SelectItem>
                        ))
                      )}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* <FormField
              control={createForm.control}
              name="department_id"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Department</FormLabel>
                  <FormControl>
                    <Input placeholder="Enter department" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            /> */}
          </div>

          <div className="flex justify-end space-x-2">
            <Button type="button" variant="ghost" onClick={onCancel}>
              Cancel
            </Button>
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? (
                <>
                  <Loader2 className="mr-2 size-4 animate-spin" />
                  Please wait
                </>
              ) : (
                "Create"
              )}
            </Button>
          </div>
        </form>
      </Form>
    ) : (
      <Form {...updateForm}>
        <form onSubmit={updateForm.handleSubmit(handleSubmit)} className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <FormField
              control={updateForm.control}
              name="first_name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>First Name</FormLabel>
                  <FormControl>
                    <Input placeholder="Enter first name" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={updateForm.control}
              name="last_name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Last Name</FormLabel>
                  <FormControl>
                    <Input placeholder="Enter last name" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          <FormField
            control={updateForm.control}
            name="email"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Email</FormLabel>
                <FormControl>
                  <Input placeholder="Enter email" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={updateForm.control}
            name="phone_number"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Phone Number</FormLabel>
                <FormControl>
                  <Input placeholder="Enter phone number" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <div className="grid grid-cols-2 gap-4">
            <FormField
              control={updateForm.control}
              name="role_id"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Role</FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    value={field.value}
                    disabled={isLoadingRoles}
                  >
                    <FormControl>
                      <SelectTrigger>
                        {isLoadingRoles ? (
                          <div className="flex items-center gap-2">
                            <Loader2 className="size-4 animate-spin" />
                            <span>Loading roles...</span>
                          </div>
                        ) : (
                          <SelectValue placeholder="Select a role" />
                        )}
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {rolesError ? (
                        <div className="p-2 text-sm text-red-600">Error loading roles</div>
                      ) : Object.keys(roleMap).length === 0 ? (
                        <div className="p-2 text-sm text-muted-foreground">
                          {isLoadingRoles ? 'Loading roles...' : 'No roles available'}
                        </div>
                      ) : (
                        Object.entries(roleMap).map(([key, value]) => (
                          <SelectItem key={key} value={key}>{value}</SelectItem>
                        ))
                      )}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* <FormField
              control={updateForm.control}
              name="department_id"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Department</FormLabel>
                  <FormControl>
                    <Input placeholder="Enter department" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            /> */}
          </div>

          <FormField
            control={updateForm.control}
            name="status"
            render={({ field }) => (
              <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                <div className="space-y-0.5">
                  <FormLabel>Active</FormLabel>
                  <FormDescription>Whether the user is active or not.</FormDescription>
                </div>
                <FormControl>
                  <Checkbox
                    checked={field.value === "ACTIVE"}
                    onCheckedChange={(checked) => field.onChange(checked ? "ACTIVE" : "INACTIVE")}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <div className="flex justify-end space-x-2">
            <Button type="button" variant="ghost" onClick={onCancel}>
              Cancel
            </Button>
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? (
                <>
                  <Loader2 className="mr-2 size-4 animate-spin" />
                  Please wait
                </>
              ) : (
                "Update"
              )}
            </Button>
          </div>
        </form>
      </Form>
    ) }
    </>
  )
}
