"use client"

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger 
} from "@/components/ui/dropdown-menu"
import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { MoreHorizontal, Eye, Pencil, RefreshCw, Lock, Unlock, AlertTriangle } from "lucide-react"
import { User } from "@/lib/api"
import { formatDate } from "@/lib/utils"

interface UsersTableProps {
  users: User[]
  isLoading: boolean
  onViewUser: (user: User) => void
  onEditUser: (user: User) => void
  onDeleteUser: (user: User) => void
  onResetPassword: (user: User) => void
  onSuspendUser: (user: User) => void
  onReactivateUser: (user: User) => void
}

// Helper function to get role color
const getRoleBadgeColor = (roleId: number | string): string => {
  const id = typeof roleId === 'string' ? parseInt(roleId, 10) : roleId
  
  switch (id) {
    case 1: // Super Admin
      return "bg-red-100 text-red-800 hover:bg-red-100/80"
    case 2: // Operation Manager
      return "bg-purple-100 text-purple-800 hover:bg-purple-100/80"
    case 3: // Head of Department
      return "bg-blue-100 text-blue-800 hover:bg-blue-100/80"
    case 4: // Team Lead
      return "bg-green-100 text-green-800 hover:bg-green-100/80"
    case 5: // Agent
    default:
      return "bg-gray-100 text-gray-800 hover:bg-gray-100/80"
  }
}

// Helper function to get role name
const getRoleName = (roleId: number | string): string => {
  const id = typeof roleId === 'string' ? parseInt(roleId, 10) : roleId
  
  switch (id) {
    case 1: return "Super Admin"
    case 2: return "Operation Manager"
    case 3: return "Head of Department"
    case 4: return "Team Lead"
    case 5: return "Agent"
    default: return "Unknown Role"
  }
}

// Helper function to get department name
const getDepartmentName = (departmentId: number | string | null | undefined): string => {
  if (departmentId === null || departmentId === undefined) {
    return "Unassigned"
  }
  
  const id = typeof departmentId === 'string' ? parseInt(departmentId, 10) : departmentId
  
  switch (id) {
    case 1: return "Customer Service"
    case 2: return "Technical Support"
    case 3: return "Sales"
    default: return "Other"
  }
}

export function UsersTable({
  users,
  isLoading,
  onViewUser,
  onEditUser,
  onDeleteUser,
  onResetPassword,
  onSuspendUser,
  onReactivateUser
}: UsersTableProps) {
  return (
    <div className="rounded-md border">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Name</TableHead>
            <TableHead>Email</TableHead>
            <TableHead>Role</TableHead>
            <TableHead>Department</TableHead>
            <TableHead>Status</TableHead>
            <TableHead>Created</TableHead>
            <TableHead className="text-right">Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {users.length > 0 ? (
            users.map((user) => (
              <TableRow key={user.id}>
                <TableCell className="font-medium">
                  {user.first_name} {user.last_name}
                </TableCell>
                <TableCell>{user.email}</TableCell>
                <TableCell>
                  <Badge variant="outline" className={getRoleBadgeColor(user.role_id)}>
                    {getRoleName(user.role_id)}
                  </Badge>
                </TableCell>
                <TableCell>{getDepartmentName(user.product?.name || 0)}</TableCell>
                <TableCell>
                  {user.status === "ACTIVE" ? (
                    <Badge variant="outline" className="bg-green-100 text-green-800 hover:bg-green-100/80">
                      Active
                    </Badge>
                  ) : (
                    <Badge variant="destructive">
                      Suspended
                    </Badge>
                  )}
                </TableCell>
                <TableCell>{formatDate(user.created_at)}</TableCell>
                <TableCell className="text-right">
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" className="h-8 w-8 p-0">
                        <span className="sr-only">Open menu</span>
                        <MoreHorizontal className="size-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem onClick={() => onViewUser(user)}>
                        <Eye className="mr-2 size-4" />
                        View details
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => onEditUser(user)}>
                        <Pencil className="mr-2 size-4" />
                        Edit user
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => onResetPassword(user)}>
                        <RefreshCw className="mr-2 size-4" />
                        Reset password
                      </DropdownMenuItem>
                      {user.status === "ACTIVE" ? (
                        <DropdownMenuItem onClick={() => onSuspendUser(user)}>
                          <Lock className="mr-2 size-4" />
                          Suspend user
                        </DropdownMenuItem>
                      ) : (
                        <DropdownMenuItem onClick={() => onReactivateUser(user)}>
                          <Unlock className="mr-2 size-4" />
                          Reactivate user
                        </DropdownMenuItem>
                      )}
                      <DropdownMenuItem onClick={() => onDeleteUser(user)}>
                        <AlertTriangle className="mr-2 size-4" />
                        Delete user
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </TableCell>
              </TableRow>
            ))
          ) : (
            <TableRow>
              <TableCell colSpan={7} className="text-center py-10">
                {isLoading ? "Loading users..." : "No users found"}
              </TableCell>
            </TableRow>
          )}
        </TableBody>
      </Table>
    </div>
  )
}
