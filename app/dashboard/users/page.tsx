"use client"

import { useState, useEffect } from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { UserForm } from "./components/UserForm"
import { SuspendUserDialog } from "./components/SuspendUserDialog"
import { DebugPanel } from "./components/DebugPanel"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  Filter,
  Search,
  UserPlus,
  Loader2,
  AlertCircle,
  MoreHorizontal,
  Eye,
  Pencil,
  KeyRound,
  Ban,
  RefreshCw,
  AlertTriangle,
} from "lucide-react"
import { useRouter } from "next/navigation"
import {
  Drawer,
  DrawerClose,
  DrawerContent,
  DrawerDescription,
  DrawerFooter,
  DrawerHeader,
  DrawerTitle,
} from "@/components/ui/drawer"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { useToast } from "@/components/ui/use-toast"
import { Checkbox } from "@/components/ui/checkbox"
import { Badge } from "@/components/ui/badge"
import * as z from "zod"

// Import components
import { ConfirmationDialog } from "./components/ConfirmationDialog"

// Import API and hooks
import { useUsers } from "@/lib/hooks/useUsers"
import { User, Role } from "@/lib/api"
import { createUserSchema, updateUserSchema, CreateUserFormValues, UpdateUserFormValues } from "@/lib/validations/users"
import { useAuth } from "@/lib/hooks/useAuth"

// Role mappings for display purposes
// We no longer need the hardcoded roleMap as we get role information directly from the API

// We'll use the validation schemas from our lib/validations/users.ts file

export default function UsersPage() {
  const router = useRouter()
  const [searchQuery, setSearchQuery] = useState("")
  const [roleFilter, setRoleFilter] = useState("")
  const [statusFilter, setStatusFilter] = useState("")
  const [isAddUserOpen, setIsAddUserOpen] = useState(false)
  const [isEditUserOpen, setIsEditUserOpen] = useState(false)
  const [currentUser, setCurrentUser] = useState<User | null>(null)
  const [isViewUserOpen, setIsViewUserOpen] = useState(false)
  const [isResetPasswordOpen, setIsResetPasswordOpen] = useState(false)
  const [isDeleteUserOpen, setIsDeleteUserOpen] = useState(false)
  const [isSuspendUserOpen, setIsSuspendUserOpen] = useState(false)
  const [isReactivateUserOpen, setIsReactivateUserOpen] = useState(false)

  // Use our API hooks
  const {
    users: usersData,
    isLoadingUsers,
    refetchUsers,
    createUser,
    isCreatingUser,
    updateUser,
    isUpdatingUser,
    deleteUser,
    isDeletingUser,
    reactivateUser,
    isReactivatingUser,
    suspendUser,
    isSuspendingUser
  } = useUsers({
    search: searchQuery || undefined,
    role_id: roleFilter || undefined,
    status: statusFilter === "active" ? "ACTIVE" : statusFilter === "inactive" ? "INACTIVE" : undefined
  })

  const { forgotPassword, isResettingPassword } = useAuth()

  // Extract users data from the API response
  const users = usersData || []

  // Toast notifications
  const { toast } = useToast()

  // Initialize the form for creating users
  const createForm = useForm<CreateUserFormValues>({
    resolver: zodResolver(createUserSchema),
    defaultValues: {
      first_name: "",
      last_name: "",
      email: "",
      role_id: "",
      // department_id: "",
    },
  })

  // Initialize the form for editing users
  const updateForm = useForm<UpdateUserFormValues>({
    resolver: zodResolver(updateUserSchema),
    defaultValues: {
      first_name: "",
      last_name: "",
      email: "",
      role_id: "",
      // department_id: "",
      status: "INACTIVE", // New users are created with INACTIVE status
    },
  })

  // Reset create form when dialog opens/closes
  useEffect(() => {
    if (isAddUserOpen) {
      createForm.reset({
        first_name: "",
        last_name: "",
        email: "",
        role_id: "",
        // department_id: "",
      })
    }
  }, [isAddUserOpen, createForm])

  // Set form values when editing a user
  useEffect(() => {
    if (isEditUserOpen && currentUser) {
      updateForm.reset({
        first_name: currentUser.first_name,
        last_name: currentUser.last_name,
        email: currentUser.email,
        role_id: currentUser.role_id ? currentUser.role_id.toString() : "",
        // department_id: currentUser.department_id?.toString(),
        status: currentUser.status,
      })
    }
  }, [isEditUserOpen, currentUser, updateForm])

  // Filtering is handled by the API through the useUsers hook

  // Handle form submission for creating a new user
  const handleCreateUser = (data: CreateUserFormValues) => {
    createUser(data, {
      onError: (error: any) => {
        toast({
          title: "Error creating user",
          description: error.message || "An error occurred while creating the user",
          variant: "destructive",
        })
      },
      onSuccess: () => {
        toast({
          title: "User created successfully",
          description: "The user has been added to the system",
          variant: "default"
        })
        setIsAddUserOpen(false)
        createForm.reset()
      }
    })
  }

  // Handle form submission for updating a user
  const handleUpdateUser = (data: UpdateUserFormValues) => {
    if (!currentUser) return

    updateUser({ id: currentUser.id, data }, {
      onError: (error: any) => {
        toast({
          title: "Error updating user",
          description: error.message || "An error occurred while updating the user",
          variant: "destructive",
        })
      },
      onSuccess: () => {
        toast({
          title: "User updated successfully",
          description: "The user information has been updated",
          variant: "default"
        })
        setIsEditUserOpen(false)
        updateForm.reset()
      }
    })
  }

  // Handle user deletion
  const handleDeleteUser = () => {
    if (!currentUser) return

    deleteUser(currentUser.id, {
      onError: (error: any) => {
        toast({
          title: "Error deleting user",
          description: error.message || "An error occurred while deleting the user",
          variant: "destructive",
        })
      },
      onSuccess: () => {
        toast({
          title: "User deleted successfully",
          description: "The user has been removed from the system",
          variant: "default"
        })
        setIsDeleteUserOpen(false)
      }
    })
  }

  // Handle password reset
  const handleResetPassword = () => {
    if (!currentUser) return

    forgotPassword(currentUser.email, {
      onError: (error: any) => {
        toast({
          title: "Error resetting password",
          description: error.message || "An error occurred while resetting the password",
          variant: "destructive",
        })
      },
      onSuccess: () => {
        toast({
          title: "Password reset initiated",
          description: "An email has been sent to the user with password reset instructions",
          variant: "default"
        })
        setIsResetPasswordOpen(false)
      }
    })
  }

  // Handle user suspension
  const handleSuspendUser = (reason: string) => {
    if (!currentUser) return

    suspendUser({
      id: currentUser.id,
      reason: reason
    }, {
      onError: (error: any) => {
        toast({
          title: "Error suspending user",
          description: error.message || "An error occurred while suspending the user",
          variant: "destructive",
        })
      },
      onSuccess: () => {
        toast({
          title: "User suspended",
          description: "The user has been suspended and cannot access the system",
          variant: "default"
        })
        setIsSuspendUserOpen(false)
        // Refresh the users list
        refetchUsers()
      }
    })
  }

  // Handle user reactivation
  const handleReactivateUser = () => {
    if (!currentUser) return

    reactivateUser(currentUser.id, {
      onError: (error: any) => {
        toast({
          title: "Error reactivating user",
          description: error.message || "An error occurred while reactivating the user",
          variant: "destructive",
        })
      },
      onSuccess: () => {
        toast({
          title: "User reactivated",
          description: "The user has been reactivated and can now access the system",
          variant: "default"
        })
        setIsReactivateUserOpen(false)
        // Refresh the users list
        refetchUsers()
      }
    })
  }

  return (
    <div className="flex-1 space-y-4 p-4 md:p-8 pt-6">
      <div className="flex items-center justify-between">
        <h2 className="text-3xl font-bold tracking-tight">User Management</h2>
        <Button onClick={() => setIsAddUserOpen(true)}>
          <UserPlus className="mr-2 size-4" />
          Add User
        </Button>
      </div>

      <div className="flex flex-col gap-4">
        <div className="flex flex-wrap items-center gap-2">
          <div className="flex-1 relative">
            <Search className="absolute left-2.5 top-2.5 size-4 text-muted-foreground" />
            <Input
              placeholder="Search users..."
              className="pl-8 w-full md:max-w-sm"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="icon">
                <Filter className="size-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-[200px]">
              <DropdownMenuLabel>Filter by</DropdownMenuLabel>
              <DropdownMenuSeparator />
              <DropdownMenuItem>Role</DropdownMenuItem>
              <DropdownMenuItem>Status</DropdownMenuItem>
              <DropdownMenuItem>Date created</DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
          <Select value={roleFilter} onValueChange={setRoleFilter}>
            <SelectTrigger className="w-[150px]">
              <SelectValue placeholder="Filter by role" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Roles</SelectItem>
              <SelectItem value="admin">Admin</SelectItem>
              <SelectItem value="agent">Agent</SelectItem>
              <SelectItem value="customer">Customer</SelectItem>
            </SelectContent>
          </Select>
          <Select value={statusFilter} onValueChange={setStatusFilter}>
            <SelectTrigger className="w-[150px]">
              <SelectValue placeholder="Filter by status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Status</SelectItem>
              <SelectItem value="active">Active</SelectItem>
              <SelectItem value="inactive">Inactive</SelectItem>
              <SelectItem value="suspended">Suspended</SelectItem>
            </SelectContent>
          </Select>
        </div>
        <div className="rounded-md border">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Name</TableHead>
                <TableHead>Email</TableHead>
                <TableHead>Role</TableHead>
                <TableHead>Phone Number</TableHead>
                <TableHead>Status</TableHead>
                {/* <TableHead>Product</TableHead> */}
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {isLoadingUsers ? (
                <TableRow>
                  <TableCell colSpan={8} className="text-center py-10">
                    <div className="flex justify-center items-center">
                      <Loader2 className="h-8 w-8 animate-spin mr-2" />
                      <span>Loading users...</span>
                    </div>
                  </TableCell>
                </TableRow>
              ) : users && users.length > 0 ? (
                users.map((user: User) => (
                  <TableRow key={user.id}>
                    <TableCell className="font-medium">
                      {user.first_name} {user.last_name}
                    </TableCell>
                    <TableCell>{user.email}</TableCell>
                    <TableCell>{user.role?.name || 'No Role Assigned'}</TableCell>
                    <TableCell>{user.phone_number}</TableCell>
                    <TableCell>
                      <Badge
                        variant={user.status === "ACTIVE" ? "outline" : "destructive"}
                      >
                        {user.status === "ACTIVE" ? "Active" : "Suspended"}
                      </Badge>
                    </TableCell>
                    {/* <TableCell>{formatDate(user.product?.name || 'No Product Assigned')}</TableCell> */}
                    <TableCell className="text-right">
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" className="h-8 w-8 p-0">
                            <span className="sr-only">Open menu</span>
                            <MoreHorizontal className="size-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuLabel>Actions</DropdownMenuLabel>
                          <DropdownMenuItem
                            onClick={() => {
                              setCurrentUser(user)
                              setIsViewUserOpen(true)
                            }}
                          >
                            <Eye className="mr-2 size-4" />
                            View details
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            onClick={() => {
                              setCurrentUser(user)
                              setIsEditUserOpen(true)
                            }}
                          >
                            <Pencil className="mr-2 size-4" />
                            Edit user
                          </DropdownMenuItem>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem
                            onClick={() => {
                              setCurrentUser(user)
                              setIsResetPasswordOpen(true)
                            }}
                          >
                            <KeyRound className="mr-2 size-4" />
                            Reset password
                          </DropdownMenuItem>
                          {user.status === "ACTIVE" && (
                            <DropdownMenuItem
                              onClick={() => {
                                setCurrentUser(user)
                                setIsSuspendUserOpen(true)
                              }}
                              className="text-destructive"
                            >
                              <Ban className="mr-2 size-4" />
                              Suspend user
                            </DropdownMenuItem>
                          )}
                          {user.status !== "ACTIVE" && (
                            <DropdownMenuItem
                              onClick={() => {
                                setCurrentUser(user)
                                setIsReactivateUserOpen(true)
                              }}
                            >
                              <RefreshCw className="mr-2 size-4" />
                              Reactivate user
                            </DropdownMenuItem>
                          )}
                          <DropdownMenuItem
                            className="text-destructive"
                            onClick={() => {
                              setCurrentUser(user)
                              setIsDeleteUserOpen(true)
                            }}
                          >
                            <AlertTriangle className="mr-2 size-4" />
                            Delete user
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={8} className="text-center py-10">
                    No users found
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </div>
      </div>

      {/* Add User Dialog */}
      <Dialog open={isAddUserOpen} onOpenChange={setIsAddUserOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Add New User</DialogTitle>
            <DialogDescription>
              Create a new user account. Fill in all the required fields below.
            </DialogDescription>
          </DialogHeader>
          <UserForm
            type="create"
            isSubmitting={isCreatingUser}
            onSubmit={(data) => handleCreateUser(data as CreateUserFormValues)}
            onCancel={() => setIsAddUserOpen(false)}
          />
        </DialogContent>
      </Dialog>

      {/* Edit User Dialog */}
      <Dialog open={isEditUserOpen} onOpenChange={setIsEditUserOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Edit User</DialogTitle>
            <DialogDescription>
              Update user information. Make changes to the fields below.
            </DialogDescription>
          </DialogHeader>
          <UserForm
            type="update"
            user={currentUser || undefined}
            isSubmitting={isUpdatingUser}
            onSubmit={handleUpdateUser}
            onCancel={() => setIsEditUserOpen(false)}
          />
        </DialogContent>
      </Dialog>

      {/* View User Dialog */}
      <Dialog open={isViewUserOpen} onOpenChange={setIsViewUserOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>User Details</DialogTitle>
            <DialogDescription>
              Viewing user information and details.
            </DialogDescription>
          </DialogHeader>
          {currentUser && (
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <p className="text-sm font-medium">First Name</p>
                  <p className="text-sm">{currentUser.first_name}</p>
                </div>
                <div>
                  <p className="text-sm font-medium">Last Name</p>
                  <p className="text-sm">{currentUser.last_name}</p>
                </div>
                <div>
                  <p className="text-sm font-medium">Email</p>
                  <p className="text-sm">{currentUser.email}</p>
                </div>
                <div>
                  <p className="text-sm font-medium">Role</p>
                  <p className="text-sm">{currentUser.role?.name || 'No Role Assigned'}</p>
                </div>
                <div>
                  <p className="text-sm font-medium">Phone Number</p>
                  <p className="text-sm">{currentUser.phone_number}</p>
                </div>
                <div>
                  <p className="text-sm font-medium">Status</p>
                  <Badge
                    variant={currentUser.status === "ACTIVE" ? "outline" : "destructive"}
                  >
                    {currentUser.status === "ACTIVE" ? "Active" : "Suspended"}
                  </Badge>
                </div>
                <div>
                  <p className="text-sm font-medium">Created</p>
                  <p className="text-sm">{formatDate(currentUser.created_at)}</p>
                </div>
              </div>
            </div>
          )}
          <DialogFooter>
            <Button onClick={() => setIsViewUserOpen(false)}>
              Close
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Reset Password Dialog */}
      <ConfirmationDialog
        open={isResetPasswordOpen}
        onOpenChange={(open: boolean) => setIsResetPasswordOpen(open)}
        title="Reset Password"
        description="Are you sure you want to reset the password for this user? They will receive an email with instructions to create a new password."
        confirmText="Reset Password"
        cancelText="Cancel"
        onConfirm={handleResetPassword}
        isConfirming={isResettingPassword}
      />

      {/* Delete User Dialog */}
      <ConfirmationDialog
        open={isDeleteUserOpen}
        onOpenChange={(open: boolean) => setIsDeleteUserOpen(open)}
        title="Delete User"
        description="Are you sure you want to delete this user? This action cannot be undone and all associated data will be permanently removed."
        confirmText="Delete User"
        cancelText="Cancel"
        onConfirm={handleDeleteUser}
        isConfirming={isDeletingUser}
        variant="destructive"
      />

      {/* Suspend User Dialog */}
      <SuspendUserDialog
        open={isSuspendUserOpen}
        onOpenChange={(open: boolean) => setIsSuspendUserOpen(open)}
        onConfirm={handleSuspendUser}
        isConfirming={isSuspendingUser}
      />

      {/* Reactivate User Dialog */}
      <ConfirmationDialog
        open={isReactivateUserOpen}
        onOpenChange={(open: boolean) => setIsReactivateUserOpen(open)}
        title="Reactivate User"
        description="Are you sure you want to reactivate this user? They will regain access to the system."
        confirmText="Reactivate User"
        cancelText="Cancel"
        onConfirm={handleReactivateUser}
        isConfirming={isReactivatingUser}
      />
    </div>
  )

}

function formatDate(dateString: string) {
  if (!dateString) return '-';
  const date = new Date(dateString);
  if (isNaN(date.getTime())) return '-';
  return new Intl.DateTimeFormat("en-US", {
    year: "numeric",
    month: "short",
    day: "numeric",
  }).format(date)}
