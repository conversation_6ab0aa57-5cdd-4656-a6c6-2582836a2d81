import React from 'react';
import { useRouter } from 'next/navigation';

// Placeholder for fetching ticket data. Replace with real API call.
async function fetchTicket(ticketId: string) {
  // TODO: Replace with actual data fetching logic
  return {
    id: ticketId,
    subject: 'Sample Ticket Subject',
    status: 'Open',
    description: 'This is a sample ticket description.',
    createdAt: '2025-05-29',
    updatedAt: '2025-05-29',
  };
}

export default async function TicketDetailsPage({ params }: { params: { ticketId: string } }) {
  const ticket = await fetchTicket(params.ticketId);

  if (!ticket) {
    return <div>Ticket not found.</div>;
  }

  return (
    <div className="p-6 max-w-2xl mx-auto">
      <h1 className="text-2xl font-bold mb-4">Ticket Details</h1>
      <div className="mb-2"><strong>ID:</strong> {ticket.id}</div>
      <div className="mb-2"><strong>Subject:</strong> {ticket.subject}</div>
      <div className="mb-2"><strong>Status:</strong> {ticket.status}</div>
      <div className="mb-2"><strong>Description:</strong> {ticket.description}</div>
      <div className="mb-2"><strong>Created At:</strong> {ticket.createdAt}</div>
      <div className="mb-2"><strong>Updated At:</strong> {ticket.updatedAt}</div>
    </div>
  );
}
