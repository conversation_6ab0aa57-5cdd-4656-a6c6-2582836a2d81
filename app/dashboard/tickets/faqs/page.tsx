"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON> } from "next/navigation"
import { toast } from "@/components/ui/use-toast"
import { useFAQs } from "@/lib/hooks/useFAQs"
import { useCategories } from "@/lib/hooks/useCategories"
import { useProducts } from "@/lib/hooks/useProducts"
import { usePermissions } from "@/lib/hooks/usePermissions"
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import { createFaqSchema, updateFaqSchema, flagFaqSchema } from "@/lib/validations/faqs"
import type { CreateFAQInput, UpdateFAQInput, FlagFAQInput } from "@/lib/validations/faqs"
import type { FAQ, CreateFAQRequest } from "@/lib/api/types"
import { FAQStatus } from "@/lib/api/types"
import { BatchUploadDialog } from "@/components/faqs/batch-upload-dialog"
import { ApproveFAQDialog } from "@/components/faqs/approve-faq-dialog"

import {
  AlertTriangle,
  CheckCircle2,
  Flag,
  Pencil,
  Plus,
  Search,
  Trash2,
  User,
  X,
  Upload,
} from "lucide-react"

import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { Textarea } from "@/components/ui/textarea"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  Table,
  TableBody,
  TableCaption,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { Switch } from "@/components/ui/switch"
import { useAuthContext } from "@/lib/context/auth-context"

export default function FAQsPage() {
  // State variables
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedCategory, setSelectedCategory] = useState<string>("all");
  const [selectedProduct, setSelectedProduct] = useState<string>("all");
  const [publishedFilter, setPublishedFilter] = useState<string>("all");
  // For supervisors, show all FAQs by default; for agents, hide flagged FAQs by default
  const [showFlagged, setShowFlagged] = useState(false);
  // Filter to show only FAQs created by the current user
  const [showMyFaqs, setShowMyFaqs] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isFlagDialogOpen, setIsFlagDialogOpen] = useState(false);
  const [isBatchUploadDialogOpen, setIsBatchUploadDialogOpen] = useState(false);
  const [isApproveDialogOpen, setIsApproveDialogOpen] = useState(false);
  const [currentFAQ, setCurrentFAQ] = useState<FAQ | null>(null);
  const [activeTab, setActiveTab] = useState("all");
  const { user } = useAuthContext()

  // Hooks
  const router = useRouter();
  const { hasPermission } = usePermissions();
  const {
    faqs,
    isLoading,
    error,
    refetchFAQs,
    createFAQ,
    updateFAQ,
    deleteFAQ,
    flagFAQ,
    approveFAQ,
    rejectFAQ,
    createFAQsBatch,
    isCreatingFAQ,
    isUpdatingFAQ,
    isDeletingFAQ,
    isFlaggingFAQ,
    isApprovingFAQ,
    isRejectingFAQ,
    isCreatingFAQsBatch,
  } = useFAQs();
  const { data: categories = [], isLoading: isCategoriesLoading } = useCategories();
  const { data: products = [], isLoading: isProductsLoading } = useProducts();

  // Form for creating/editing FAQs
  const faqForm = useForm<CreateFAQInput>({
    resolver: zodResolver(createFaqSchema),
    defaultValues: {
      question: "",
      answer: "",
      category_id: "",
      product_id: null,
      is_active: true,
      is_published: true,
    },
  });

  // Form for flagging FAQs
  const flagForm = useForm<FlagFAQInput>({
    resolver: zodResolver(flagFaqSchema),
    defaultValues: {
      is_flagged: true,
      flag_reason: "",
    },
  });

  // Reset the form when the dialog is opened/closed
  useEffect(() => {
    if (!isEditDialogOpen) {
      faqForm.reset();
      setCurrentFAQ(null);
    }
  }, [isEditDialogOpen, faqForm]);

  // Set the form values when editing an existing FAQ
  useEffect(() => {
    if (currentFAQ && isEditDialogOpen) {
      faqForm.reset({
        question: currentFAQ.question,
        answer: currentFAQ.answer,
        category_id: currentFAQ.category_id,
        product_id: currentFAQ.product_id,
        is_active: currentFAQ.is_active,
        is_published: currentFAQ.is_published,
      });
    }
  }, [currentFAQ, isEditDialogOpen, faqForm]);

  // Filter FAQs based on search term, category, product, published status, flagged status, and creator
  const filteredFAQs = faqs
    .filter((faq) => {
      // For supervisors, always show all FAQs regardless of flagged status
      // For agents, only show flagged FAQs if the toggle is on
      const isSupervisor = hasPermission('supervisor:access');
      if (!isSupervisor && faq.is_flagged && !showFlagged) {
        return false;
      }

      // Filter by creator (show only FAQs created by the current user)
      if (showMyFaqs && user?.id && faq.created_by !== user.id) {
        return false;
      }

      // Filter by search term
      if (
        searchTerm &&
        !faq.question.toLowerCase().includes(searchTerm.toLowerCase()) &&
        !faq.answer.toLowerCase().includes(searchTerm.toLowerCase())
      ) {
        return false;
      }

      // Filter by category
      if (selectedCategory !== "all" && faq.category_id !== selectedCategory) {
        return false;
      }

      // Filter by product
      if (selectedProduct !== "all" && faq.product_id !== selectedProduct) {
        return false;
      }

      // Filter by published status
      if (publishedFilter !== "all") {
        const isPublished = publishedFilter === "published";
        // Check if the FAQ's published status matches the filter
        if (isPublished !== faq.is_published) {
          return false;
        }
      }

      return true;
    })
    .sort((a, b) => {
      // Show flagged FAQs at the top
      if (a.is_flagged && !b.is_flagged) return -1;
      if (!a.is_flagged && b.is_flagged) return 1;

      // Then sort by creation date, newest first
      return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();
    });

  // Handle creating/editing an FAQ
  const handleSaveFAQ = async (data: CreateFAQInput) => {
    try {
      if (currentFAQ) {
        // Update existing FAQ
        updateFAQ({ id: currentFAQ.id, data });
      } else {
        // Create new FAQ
        // If user is an agent, set status to pending
        // If user is a supervisor or admin, set status to approved
        const isAgent = !hasPermission('supervisor:access') && !hasPermission('admin:access');
        const faqData = {
          ...data,
          status: isAgent ? FAQStatus.PENDING : FAQStatus.APPROVED,
        };

        createFAQ(faqData);

        // Show appropriate message based on role
        if (isAgent) {
          toast({
            title: "FAQ Submitted",
            description: "Your FAQ has been submitted for approval by a supervisor.",
          });
        }
      }
      setIsEditDialogOpen(false);
    } catch (error) {
      setIsEditDialogOpen(false);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to save FAQ.",
        variant: "destructive"
      });
    }
  };

  // Handle flagging an FAQ
  const handleFlagFAQ = async (data: FlagFAQInput) => {
    try {
      if (currentFAQ) {
        flagFAQ({
          id: currentFAQ.id,
          data,
          faq: currentFAQ // Pass the full FAQ object for permission checking
        });
        setIsFlagDialogOpen(false);
      }
    } catch (error) {
      setIsFlagDialogOpen(false);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to update FAQ flag status",
        variant: "destructive"
      });
    }
  };

  // Handle deleting an FAQ
  const handleDeleteFAQ = async (id: string) => {
    try {
      if (window.confirm("Are you sure you want to delete this FAQ?")) {
        deleteFAQ(id);
      }
    } catch (error) {
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to delete FAQ.",
        variant: "destructive"
      });
    }
  };

  // Handle editing an FAQ
  const handleEditFAQ = (faq: FAQ) => {
    setCurrentFAQ(faq);
    setIsEditDialogOpen(true);
  };

  // Handle flagging an FAQ
  const handleOpenFlagDialog = (faq: FAQ) => {
    setCurrentFAQ(faq);
    flagForm.reset({
      is_flagged: !faq.is_flagged,
      flag_reason: faq.flagged_reason || "",
    });
    setIsFlagDialogOpen(true);
  };

  // Handle opening the approve dialog
  const handleOpenApproveDialog = (faq: FAQ) => {
    setCurrentFAQ(faq);
    setIsApproveDialogOpen(true);
  };

  // Handle approving an FAQ
  const handleApproveFAQ = (faqId: string, notes?: string) => {
    approveFAQ({ id: faqId, notes });
  };

  // Handle rejecting an FAQ
  const handleRejectFAQ = (faqId: string, reason: string) => {
    rejectFAQ({ id: faqId, reason });
  };

  // Create a new FAQ
  const handleCreateFAQ = () => {
    setCurrentFAQ(null);
    faqForm.reset({
      question: "",
      answer: "",
      category_id: "",
      product_id: null,
      is_active: true,
      is_published: true,
    });
    setIsEditDialogOpen(true);
  };

  // Handle batch upload
  const handleBatchUpload = () => {
    setIsBatchUploadDialogOpen(true);
  };

  // Process batch upload
  const handleProcessBatchUpload = (data: CreateFAQRequest[]) => {
    createFAQsBatch(data);
  };

  // Check if user has permission to view FAQs
  useEffect(() => {
    if (!hasPermission('faq:view')) {
      router.push('/forbidden');
    }
  }, [hasPermission, router]);

  // For supervisors, always show all FAQs by default
  useEffect(() => {
    if (hasPermission('supervisor:access')) {
      setShowFlagged(true);
    }
  }, [hasPermission]);

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold">Frequently Asked Questions</h1>
        {hasPermission('faq:create') && (
          <div className="flex space-x-2">
            {/* Only show batch upload for admin users */}
            {hasPermission('admin:access') && (
              <Button variant="outline" onClick={handleBatchUpload}>
                <Upload className="mr-2 size-4" /> Batch Upload
              </Button>
            )}
            <Button onClick={handleCreateFAQ}>
              <Plus className="mr-2 size-4" /> Add FAQ
            </Button>
          </div>
        )}
      </div>

      <div className="grid grid-cols-1 gap-4 md:grid-cols-4">
        <div className="md:col-span-3">
          <div className="relative">
            <Search className="absolute left-2 top-2.5 size-4 text-muted-foreground" />
            <Input
              placeholder="Search questions and answers..."
              className="pl-8"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
        </div>
        <div className="flex flex-col gap-2">
          {/* Only show the flagged toggle for agents, supervisors always see all FAQs */}
          {!hasPermission('supervisor:access') && (
            <div className="flex items-center gap-2">
              <Label htmlFor="show-flagged-faqs" className="grow text-right">
                Show flagged FAQs
              </Label>
              <Switch
                id="show-flagged-faqs"
                checked={showFlagged}
                onCheckedChange={setShowFlagged}
              />
            </div>
          )}

          {/* Show My FAQs toggle for everyone */}
          <div className="flex items-center gap-2">
            <Label htmlFor="show-my-faqs" className="grow text-right">
              Show my FAQs
            </Label>
            <Switch
              id="show-my-faqs"
              checked={showMyFaqs}
              onCheckedChange={setShowMyFaqs}
            />
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 gap-4 md:grid-cols-4">
        <div className="space-y-4 md:col-span-1">
          <div>
            <Label>Filter by Category</Label>
            <Select
              value={selectedCategory}
              onValueChange={setSelectedCategory}
            >
              <SelectTrigger>
                <SelectValue placeholder="All Categories" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Categories</SelectItem>
                {categories?.map((category: any) => (
                  <SelectItem key={category.id} value={category.id}>
                    {category.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div>
            <Label>Filter by Product</Label>
            <Select
              value={selectedProduct}
              onValueChange={setSelectedProduct}
            >
              <SelectTrigger>
                <SelectValue placeholder="All Products" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Products</SelectItem>
                {products?.map((product: any) => (
                  <SelectItem key={product.id} value={product.id}>
                    {product.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div>
            <Label>Publication Status</Label>
            <Select
              value={publishedFilter}
              onValueChange={setPublishedFilter}
            >
              <SelectTrigger>
                <SelectValue placeholder="All FAQs" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All FAQs</SelectItem>
                <SelectItem value="published">Published</SelectItem>
                <SelectItem value="unpublished">Unpublished</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        <div className="md:col-span-3">
          {isLoading ? (
            <div className="flex h-40 items-center justify-center">
              <p>Loading FAQs...</p>
            </div>
          ) : error ? (
            <div className="flex h-40 items-center justify-center">
              <p className="text-destructive">Error loading FAQs</p>
            </div>
          ) : filteredFAQs.length === 0 ? (
            <div className="flex h-40 items-center justify-center">
              <p>No FAQs found. {hasPermission('faq:create') && 'Create one to get started.'}</p>
            </div>
          ) : (
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <p className="text-sm text-muted-foreground">
                  Showing {filteredFAQs.length} {filteredFAQs.length === 1 ? 'FAQ' : 'FAQs'}
                  {showMyFaqs && ' created by you'}
                  {publishedFilter !== 'all' && ` (${publishedFilter})`}
                </p>
              </div>
              {filteredFAQs.map((faq) => (
                <Card key={faq.id} className={faq.is_flagged ? "border-destructive" : ""}>
                  <CardHeader className="pb-2">
                    <div className="flex items-start justify-between">
                      <div>
                        <CardTitle className="text-lg font-medium">{faq.question}</CardTitle>
                        <CardDescription className="text-xs">
                          Category: {faq.category?.name || "Unknown"}
                          {faq.product && ` • Product: ${faq.product.name}`}
                        </CardDescription>
                      </div>
                      {/* {hasPermission('faq:update') && ( */}
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="icon">
                            <svg
                              xmlns="http://www.w3.org/2000/svg"
                              width="16"
                              height="16"
                              viewBox="0 0 24 24"
                              fill="none"
                              stroke="currentColor"
                              strokeWidth="2"
                              strokeLinecap="round"
                              strokeLinejoin="round"
                            >
                              <circle cx="12" cy="12" r="1" />
                              <circle cx="12" cy="5" r="1" />
                              <circle cx="12" cy="19" r="1" />
                            </svg>
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuLabel>Actions</DropdownMenuLabel>
                          {user?.role?.name === 'supervisor' && (
                          <DropdownMenuItem onClick={() => handleEditFAQ(faq)}>
                            <Pencil className="mr-2 size-4" /> Edit
                          </DropdownMenuItem>
                          )}
                          {/* Show approve option for pending FAQs to supervisors and above */}
                          {faq.is_active === false && user?.role?.name === 'supervisor' && (
                            <DropdownMenuItem onClick={() => handleOpenApproveDialog(faq)}>
                              <CheckCircle2 className="mr-2 size-4" /> Review
                            </DropdownMenuItem>
                          )}
                          {/* Only show flag option to agents, or unflag to supervisors and above */}
                          {(!faq.is_flagged || hasPermission('supervisor:access') || hasPermission('admin:access')) && (
                            <DropdownMenuItem onClick={() => handleOpenFlagDialog(faq)}>
                              {faq.is_flagged ? (
                                <>
                                  {user?.role?.name === 'supervisor' && (
                                    <>
                                      <CheckCircle2 className="mr-2 size-4" /> Unflag
                                    </>
                                  )}
                                </>
                              ) : (
                                <>
                                  <Flag className="mr-2 size-4" /> Flag
                                </>
                              )}
                            </DropdownMenuItem>
                          )}
                          {/* Only show delete option to supervisors and above */}
                          {user?.role?.name === 'supervisor' && (
                            <>
                              <DropdownMenuSeparator />
                              <DropdownMenuItem
                                className="text-destructive"
                                onClick={() => handleDeleteFAQ(faq.id)}
                              >
                                <Trash2 className="mr-2 size-4" /> Delete
                              </DropdownMenuItem>
                            </>
                          )}
                        </DropdownMenuContent>
                      </DropdownMenu>
                      {/* )} */}
                    </div>
                    <div className="flex items-center gap-2">

                      {faq.is_flagged && (
                        <Badge variant="destructive" className="mt-2 mr-2">
                          <AlertTriangle className="mr-1 h-3 w-3" /> Flagged
                        </Badge>
                      )}
                      {!faq.is_published && (
                        <Badge variant="outline" className="mt-2 mr-2 bg-gray-100 text-gray-800">
                          Unpublished
                        </Badge>
                      )}
                      {user?.id && faq.created_by === user.id && (
                        <Badge variant="outline" className="mt-2 mr-2 bg-blue-100 text-blue-800">
                          <User className="mr-1 h-3 w-3" /> My FAQ
                        </Badge>
                      )}
                      {faq.status === FAQStatus.PENDING && (
                        <Badge variant="outline" className="mt-2 bg-yellow-100 text-yellow-800">
                          Pending Approval
                        </Badge>
                      )}
                      {faq.status === FAQStatus.APPROVED && faq.is_flagged && (
                        <Badge variant="outline" className="mt-2 bg-green-100 text-green-800">
                          <CheckCircle2 className="mr-1 h-3 w-3" /> Approved
                        </Badge>
                      )}
                      {faq.status === FAQStatus.REJECTED && faq.is_flagged && (
                        <Badge variant="outline" className="mt-2 bg-red-100 text-red-800">
                          <X className="mr-1 h-3 w-3" /> Rejected
                        </Badge>
                      )}
                      {faq.is_flagged && faq.flagged_reason && (
                        <p className="mt-1 text-xs text-destructive">
                          Reason: {faq.flagged_reason}
                        </p>
                      )}
                      {/* Show rejection reason if available */}
                      {faq.status === FAQStatus.REJECTED && faq.rejected_reason && (
                        <p className="mt-1 text-xs text-red-600">
                          Rejection reason: {faq.rejected_reason}
                        </p>
                      )}
                      {/* Show who flagged the FAQ to supervisors and above */}
                      {faq.is_flagged && faq.flagger && hasPermission('supervisor:access') && (
                        <p className="mt-1 text-xs text-muted-foreground">
                          Flagged by: {faq.flagger.first_name} {faq.flagger.last_name}
                        </p>
                      )}
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="prose max-w-none text-sm" dangerouslySetInnerHTML={{ __html: faq.answer }} />
                  </CardContent>
                  <CardFooter className="text-xs text-muted-foreground">
                    Created by {faq.creator?.first_name} {faq.creator?.last_name} on {new Date(faq.created_at).toLocaleDateString()}
                  </CardFooter>
                </Card>
              ))}
            </div>
          )}
        </div>
      </div>

      {/* Create/Edit FAQ Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="sm:max-w-2xl">
          <DialogHeader>
            <DialogTitle>
              {currentFAQ ? "Edit FAQ" : "Create New FAQ"}
            </DialogTitle>
            <DialogDescription>
              {currentFAQ
                ? "Update the question and answer details below."
                : "Add a new frequently asked question to help users."}
            </DialogDescription>
          </DialogHeader>

          <Form {...faqForm}>
            <form onSubmit={faqForm.handleSubmit(handleSaveFAQ)} className="space-y-4">
              <FormField
                control={faqForm.control}
                name="question"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Question</FormLabel>
                    <FormControl>
                      <Input placeholder="Enter the question" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={faqForm.control}
                name="answer"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Answer</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Provide a detailed answer"
                        className="min-h-[150px]"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                <FormField
                  control={faqForm.control}
                  name="category_id"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Category</FormLabel>
                      <Select
                        value={field.value}
                        onValueChange={field.onChange}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select a category" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {categories?.map((category: any) => (
                            <SelectItem key={category.id} value={category.id}>
                              {category.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={faqForm.control}
                  name="product_id"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Product (Optional)</FormLabel>
                      <Select
                        value={field.value || "none"}
                        onValueChange={(value) => field.onChange(value === "none" ? null : value)}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select a product" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="none">None</SelectItem>
                          {products?.map((product: any) => (
                            <SelectItem key={product.id} value={product.id}>
                              {product.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={faqForm.control}
                name="is_published"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3 shadow-2xs">
                    <div className="space-y-0.5">
                      <FormLabel>Published</FormLabel>
                      <FormDescription>
                        Make this FAQ visible to users
                      </FormDescription>
                    </div>
                    <FormControl>
                      <Switch
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <DialogFooter>
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setIsEditDialogOpen(false)}
                >
                  Cancel
                </Button>
                <Button
                  type="submit"
                  disabled={isCreatingFAQ || isUpdatingFAQ}
                >
                  {(isCreatingFAQ || isUpdatingFAQ) && (
                    <span className="loading loading-spinner loading-xs mr-2"></span>
                  )}
                  {currentFAQ ? "Update" : "Create"}
                </Button>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>

      {/* Flag FAQ Dialog */}
      <Dialog open={isFlagDialogOpen} onOpenChange={setIsFlagDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>
              {currentFAQ?.is_flagged ? "Unflag FAQ" : "Flag FAQ"}
            </DialogTitle>
            <DialogDescription>
              {currentFAQ?.is_flagged
                ? "Remove the flag from this FAQ."
                : "Flag this FAQ for review or correction."}
            </DialogDescription>
          </DialogHeader>

          <Form {...flagForm}>
            <form onSubmit={flagForm.handleSubmit(handleFlagFAQ)} className="space-y-4">
              {!currentFAQ?.is_flagged && (
                <FormField
                  control={flagForm.control}
                  name="flag_reason"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Reason for Flagging</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="Explain why this FAQ needs review"
                          className="min-h-[100px]"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              )}

              <DialogFooter>
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setIsFlagDialogOpen(false)}
                >
                  Cancel
                </Button>
                <Button
                  type="submit"
                  variant={currentFAQ?.is_flagged ? "default" : "destructive"}
                  disabled={isFlaggingFAQ}
                >
                  {isFlaggingFAQ && (
                    <span className="loading loading-spinner loading-xs mr-2"></span>
                  )}
                  {currentFAQ?.is_flagged ? "Unflag" : "Flag"}
                </Button>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>

      {/* Batch Upload Dialog */}
      <BatchUploadDialog
        open={isBatchUploadDialogOpen}
        onOpenChange={setIsBatchUploadDialogOpen}
        onUpload={handleProcessBatchUpload}
        isUploading={isCreatingFAQsBatch}
      />

      {/* Approve FAQ Dialog */}
      {currentFAQ && (
        <ApproveFAQDialog
          faq={currentFAQ}
          open={isApproveDialogOpen}
          onOpenChange={setIsApproveDialogOpen}
          onApprove={handleApproveFAQ}
          onReject={handleRejectFAQ}
        />
      )}
    </div>
  );
}
