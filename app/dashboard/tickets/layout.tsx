"use client"

import type React from "react"
import type { <PERSON>ada<PERSON> } from "next"
import Link from "next/link"
import { Ta<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"
import { usePermissions } from "@/lib/hooks/usePermissions"

const metadata: Metadata = {
  title: "Tickets - Call Center Management System",
  description: "Ticket management system for call center",
}

interface TicketsLayoutProps {
  children: React.ReactNode
}

// Update the layout to remove the title since it's duplicated in the page components
export default function TicketsLayout({ children }: TicketsLayoutProps) {
  const { hasPermission } = usePermissions();

  // Check if user has admin permissions to view additional tabs
  const canViewAgents = hasPermission('agent:view');
  const canViewFAQs = hasPermission('faq:view');
  const canViewEscalations = hasPermission('ticket:escalate');

  // If user has no special permissions, just show the content without tabs
  const hasAdminAccess = canViewAgents || canViewFAQs || canViewEscalations;

  if (!hasAdminAccess) {
    // For regular users, just show the content without tabs
    return (
      <div className="flex-1 space-y-4 p-4 md:p-8 pt-6">
        {children}
      </div>
    );
  }

  // For users with admin permissions, show the tabs
  return (
    <div className="flex-1 space-y-4 p-4 md:p-8 pt-6">
      <Tabs defaultValue="tickets" className="space-y-4">
        <TabsList className="w-full md:w-auto">
          <TabsTrigger value="tickets" asChild>
            <Link href="/dashboard/tickets">Tickets</Link>
          </TabsTrigger>

          {canViewAgents && (
            <TabsTrigger value="agents" asChild>
              <Link href="/dashboard/tickets/agents">Agents</Link>
            </TabsTrigger>
          )}

          {canViewFAQs && (
            <TabsTrigger value="faqs" asChild>
              <Link href="/dashboard/tickets/faqs">FAQs</Link>
            </TabsTrigger>
          )}

          {canViewEscalations && (
            <TabsTrigger value="escalations" asChild>
              <Link href="/dashboard/tickets/escalations">Escalations</Link>
            </TabsTrigger>
          )}
        </TabsList>

        <div className="space-y-4">{children}</div>
      </Tabs>
    </div>
  )
}

