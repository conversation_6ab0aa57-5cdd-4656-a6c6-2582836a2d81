"use client"

import { useState, useEffect } from "react"
import { use<PERSON>out<PERSON> } from "next/navigation"
import { toast } from "@/components/ui/use-toast"
import { useEscalations } from "@/lib/hooks/useEscalations"
import { useTickets } from "@/lib/hooks/useTickets"
import { useUsers } from "@/lib/hooks/useUsers"
import { usePermissions } from "@/lib/hooks/usePermissions"
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import { createEscalationSchema, resolveEscalationSchema, createEscalationCommentSchema } from "@/lib/validations/escalations"
import type { CreateEscalationInput, ResolveEscalationInput, CreateEscalationCommentInput } from "@/lib/validations/escalations"
import type { Escalation, EscalationComment, User } from "@/lib/api/types"

import {
  AlertCircle,
  CheckCircle2,
  ChevronDown,
  ClipboardList,
  MessageCircle,
  MessageSquare,
  Plus,
  Search,
  Ticket,
} from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible"
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { Textarea } from "@/components/ui/textarea"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  Table,
  TableBody,
  TableCaption,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { formatDistanceToNow } from "date-fns"

export default function EscalationsPage() {
  // State variables
  const [searchTerm, setSearchTerm] = useState("")
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false)
  const [isResolveDialogOpen, setIsResolveDialogOpen] = useState(false)
  const [isCommentDialogOpen, setIsCommentDialogOpen] = useState(false)
  const [selectedEscalation, setSelectedEscalation] = useState<Escalation | null>(null)
  const [activeTab, setActiveTab] = useState("active")

  // Filtering state
  const [filterLevel, setFilterLevel] = useState<string>("all")
  const [filterAssignee, setFilterAssignee] = useState<string>("all")

  // Hooks
  const router = useRouter()
  const { hasPermission } = usePermissions()
  const {
    escalations,
    isLoading,
    error,
    refetchEscalations,
    createEscalation,
    resolveEscalation,
    useEscalationComments,
    addEscalationComment,
    getUnresolvedEscalations,
    getResolvedEscalations,
    isCreatingEscalation,
    isResolvingEscalation,
    isAddingComment,
  } = useEscalations()

  const { tickets, isLoadingTickets } = useTickets()

  // Get supervisors and admins for escalation targets
  const { users: allUsers, isLoadingUsers } = useUsers({
    role_id: 'supervisor,admin' // Filter for supervisors and admins
  })

  // Forms setup
  const createEscalationForm = useForm<CreateEscalationInput>({
    resolver: zodResolver(createEscalationSchema),
    defaultValues: {
      ticket_id: "",
      level: 1,
      reason: "",
      escalate_to: undefined,
    },
  })

  const resolveEscalationForm = useForm<ResolveEscalationInput>({
    resolver: zodResolver(resolveEscalationSchema),
    defaultValues: {
      resolution_notes: "",
    },
  })

  const commentForm = useForm<CreateEscalationCommentInput>({
    resolver: zodResolver(createEscalationCommentSchema),
    defaultValues: {
      comment: "",
      is_internal: true,
    },
  })

  // Comments for selected escalation
  const {
    data: comments = [],
    isLoading: isLoadingComments,
    error: commentsError,
    refetch: refetchComments,
  } = useEscalationComments(selectedEscalation?.id)

  // Reset forms when dialogs open/close
  useEffect(() => {
    if (!isCreateDialogOpen) {
      createEscalationForm.reset()
    }
  }, [isCreateDialogOpen, createEscalationForm])

  useEffect(() => {
    if (!isResolveDialogOpen) {
      resolveEscalationForm.reset()
    }
  }, [isResolveDialogOpen, resolveEscalationForm])

  useEffect(() => {
    if (!isCommentDialogOpen) {
      commentForm.reset()
    }
  }, [isCommentDialogOpen, commentForm])

  // Get ticket information
  const getTicketById = (id: string) => {
    return tickets.find(ticket => ticket.id === id)
  }

  // Filter escalations based on search term, level, and assignee
  const filteredEscalations = (activeTab === "active" ? getUnresolvedEscalations() : getResolvedEscalations())
    .filter((escalation) => {
      const ticket = getTicketById(escalation.ticket_id)

      // Filter by search term
      const matchesSearchTerm = !searchTerm || (
        escalation.reason.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (ticket?.description?.toLowerCase() || '').includes(searchTerm.toLowerCase()) ||
        (escalation.escalator?.first_name?.toLowerCase() || '').includes(searchTerm.toLowerCase()) ||
        (escalation.escalator?.last_name?.toLowerCase() || '').includes(searchTerm.toLowerCase())
      )

      // Filter by level
      const matchesLevel = filterLevel === 'all' || escalation.level.toString() === filterLevel

      // Filter by assignee (escalate_to)
      const matchesAssignee = filterAssignee === 'all' || escalation.escalate_to === filterAssignee

      return matchesSearchTerm && matchesLevel && matchesAssignee
    })
    .sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime())

  // Handle create escalation
  const handleCreateEscalation = async (data: CreateEscalationInput) => {
    try {
      await createEscalation(data)
      setIsCreateDialogOpen(false)
    } catch (error) {
      console.error("Error creating escalation:", error)
    }
  }

  // Handle resolve escalation
  const handleResolveEscalation = async (data: ResolveEscalationInput) => {
    try {
      if (selectedEscalation) {
        await resolveEscalation({ id: selectedEscalation.id, data })
        setIsResolveDialogOpen(false)
      }
    } catch (error) {
      console.error("Error resolving escalation:", error)
    }
  }

  // Handle add comment
  const handleAddComment = async (data: CreateEscalationCommentInput) => {
    try {
      if (selectedEscalation) {
        await addEscalationComment({ id: selectedEscalation.id, data })
        setIsCommentDialogOpen(false)
        commentForm.reset()
      }
    } catch (error) {
      console.error("Error adding comment:", error)
    }
  }

  // Handle opening comment dialog
  const handleOpenCommentDialog = (escalation: Escalation) => {
    setSelectedEscalation(escalation)
    setIsCommentDialogOpen(true)
  }

  // Handle opening resolve dialog
  const handleOpenResolveDialog = (escalation: Escalation) => {
    setSelectedEscalation(escalation)
    setIsResolveDialogOpen(true)
  }

  // Format date
  const formatDate = (date: string) => {
    return formatDistanceToNow(new Date(date), { addSuffix: true })
  }

  // Get escalation level label and color
  const getEscalationLevelDetails = (level: number) => {
    switch (level) {
      case 1:
        return { label: "Level 1", color: "bg-yellow-100 text-yellow-800" }
      case 2:
        return { label: "Level 2", color: "bg-orange-100 text-orange-800" }
      case 3:
        return { label: "Level 3", color: "bg-red-100 text-red-800" }
      default:
        return { label: `Level ${level}`, color: "bg-gray-100 text-gray-800" }
    }
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold">Ticket Escalations</h1>
        {hasPermission('ticket:escalate') && (
          <Button onClick={() => setIsCreateDialogOpen(true)}>
            <Plus className="mr-2 size-4" /> Escalate Ticket
          </Button>
        )}
      </div>

      <Tabs
        defaultValue="active"
        value={activeTab}
        onValueChange={setActiveTab}
        className="space-y-4"
      >
        <div className="flex flex-col space-y-4 sm:flex-row sm:items-center sm:justify-between sm:space-y-0">
          <TabsList>
            <TabsTrigger value="active">Active Escalations</TabsTrigger>
            <TabsTrigger value="resolved">Resolved Escalations</TabsTrigger>
          </TabsList>
          <div className="flex flex-col space-y-2 sm:space-y-0 sm:flex-row sm:space-x-2 w-full">
            <div className="relative w-full sm:w-64 lg:w-96">
              <Search className="absolute left-2.5 top-2.5 size-4 text-muted-foreground" />
              <Input
                placeholder="Search escalations..."
                className="pl-8"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>

            <div className="flex space-x-2">
              <Select
                value={filterLevel}
                onValueChange={setFilterLevel}
              >
                <SelectTrigger className="w-[140px]">
                  <SelectValue placeholder="Level" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Levels</SelectItem>
                  <SelectItem value="1">Level 1</SelectItem>
                  <SelectItem value="2">Level 2</SelectItem>
                  <SelectItem value="3">Level 3</SelectItem>
                </SelectContent>
              </Select>

              <Select
                value={filterAssignee}
                onValueChange={setFilterAssignee}
              >
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="Assignee" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Assignees</SelectItem>
                  {allUsers
                    .filter(user => user.status === 'ACTIVE')
                    .map((user) => (
                      <SelectItem key={user.id} value={user.id}>
                        {user.first_name} {user.last_name}
                      </SelectItem>
                    ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>

        <TabsContent value="active" className="space-y-4">
          {isLoading ? (
            <div className="flex h-40 items-center justify-center">
              <p>Loading escalations...</p>
            </div>
          ) : filteredEscalations.length === 0 ? (
            <div className="flex h-40 items-center justify-center">
              <p className="text-center">No active escalations found</p>
            </div>
          ) : (
            <div className="space-y-4">
              {filteredEscalations.map((escalation) => (
                <EscalationCard
                  key={escalation.id}
                  escalation={escalation}
                  comments={escalation.id === selectedEscalation?.id ? comments : []}
                  isLoadingComments={isLoadingComments}
                  onResolve={() => handleOpenResolveDialog(escalation)}
                  onAddComment={() => handleOpenCommentDialog(escalation)}
                  formatDate={formatDate}
                  getEscalationLevelDetails={getEscalationLevelDetails}
                  onSelectEscalation={(esc) => setSelectedEscalation(esc)}
                  getTicketById={getTicketById}
                />
              ))}
            </div>
          )}
        </TabsContent>

        <TabsContent value="resolved" className="space-y-4">
          {isLoading ? (
            <div className="flex h-40 items-center justify-center">
              <p>Loading resolved escalations...</p>
            </div>
          ) : filteredEscalations.length === 0 ? (
            <div className="flex h-40 items-center justify-center">
              <p className="text-center">No resolved escalations found</p>
            </div>
          ) : (
            <div className="space-y-4">
              {filteredEscalations.map((escalation) => (
                <EscalationCard
                  key={escalation.id}
                  escalation={escalation}
                  comments={escalation.id === selectedEscalation?.id ? comments : []}
                  isLoadingComments={isLoadingComments}
                  onResolve={() => {}}
                  onAddComment={() => handleOpenCommentDialog(escalation)}
                  formatDate={formatDate}
                  getEscalationLevelDetails={getEscalationLevelDetails}
                  onSelectEscalation={(esc) => setSelectedEscalation(esc)}
                  getTicketById={getTicketById}
                  isResolved
                />
              ))}
            </div>
          )}
        </TabsContent>
      </Tabs>

      {/* Create Escalation Dialog */}
      <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
        <DialogContent className="sm:max-w-lg">
          <DialogHeader>
            <DialogTitle>Escalate Ticket</DialogTitle>
            <DialogDescription>
              Escalate a ticket to get additional support or attention.
            </DialogDescription>
          </DialogHeader>

          <Form {...createEscalationForm}>
            <form onSubmit={createEscalationForm.handleSubmit(handleCreateEscalation)} className="space-y-4">
              <FormField
                control={createEscalationForm.control}
                name="ticket_id"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Ticket</FormLabel>
                    <Select
                      value={field.value}
                      onValueChange={field.onChange}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select a ticket to escalate" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {tickets
                          .filter(ticket => ticket.status !== 'closed')
                          .map((ticket) => (
                            <SelectItem key={ticket.id} value={ticket.id}>
                              {ticket.description.substring(0, 40)}{ticket.description.length > 40 ? '...' : ''} ({ticket.status})
                            </SelectItem>
                          ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={createEscalationForm.control}
                name="level"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Escalation Level</FormLabel>
                    <Select
                      value={field.value.toString()}
                      onValueChange={(value) => field.onChange(parseInt(value))}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select escalation level" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="1">Level 1 - Team Lead</SelectItem>
                        <SelectItem value="2">Level 2 - Manager</SelectItem>
                        <SelectItem value="3">Level 3 - Executive</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormDescription>
                      Higher levels will involve more senior staff
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={createEscalationForm.control}
                name="reason"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Reason for Escalation</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Explain why this ticket needs to be escalated"
                        className="min-h-[120px]"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={createEscalationForm.control}
                name="escalate_to"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Escalate To (Optional)</FormLabel>
                    <Select
                      value={field.value}
                      onValueChange={field.onChange}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select a specific person to escalate to" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="">Anyone with appropriate access</SelectItem>
                        {allUsers
                          .filter(user => user.status === 'ACTIVE')
                          .map((user) => (
                            <SelectItem key={user.id} value={user.id}>
                              {user.first_name} {user.last_name} ({user.role?.name})
                            </SelectItem>
                          ))}
                      </SelectContent>
                    </Select>
                    <FormDescription>
                      If left empty, the escalation will be visible to all users with appropriate access
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <DialogFooter>
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setIsCreateDialogOpen(false)}
                >
                  Cancel
                </Button>
                <Button
                  type="submit"
                  disabled={isCreatingEscalation}
                >
                  {isCreatingEscalation && (
                    <span className="loading loading-spinner loading-xs mr-2"></span>
                  )}
                  Escalate Ticket
                </Button>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>

      {/* Resolve Escalation Dialog */}
      <Dialog open={isResolveDialogOpen} onOpenChange={setIsResolveDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Resolve Escalation</DialogTitle>
            <DialogDescription>
              Provide resolution notes to close this escalation.
            </DialogDescription>
          </DialogHeader>

          <Form {...resolveEscalationForm}>
            <form onSubmit={resolveEscalationForm.handleSubmit(handleResolveEscalation)} className="space-y-4">
              <FormField
                control={resolveEscalationForm.control}
                name="resolution_notes"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Resolution Notes</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Explain how this escalation was resolved"
                        className="min-h-[120px]"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <DialogFooter>
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setIsResolveDialogOpen(false)}
                >
                  Cancel
                </Button>
                <Button
                  type="submit"
                  disabled={isResolvingEscalation}
                >
                  {isResolvingEscalation && (
                    <span className="loading loading-spinner loading-xs mr-2"></span>
                  )}
                  Resolve Escalation
                </Button>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>

      {/* Add Comment Dialog */}
      <Dialog open={isCommentDialogOpen} onOpenChange={setIsCommentDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Add Comment</DialogTitle>
            <DialogDescription>
              Add a comment to this escalation for tracking purposes.
            </DialogDescription>
          </DialogHeader>

          <Form {...commentForm}>
            <form onSubmit={commentForm.handleSubmit(handleAddComment)} className="space-y-4">
              <FormField
                control={commentForm.control}
                name="comment"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Comment</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Add your comment here"
                        className="min-h-[120px]"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={commentForm.control}
                name="is_internal"
                render={({ field }) => (
                  <FormItem className="flex items-center gap-2 space-y-0">
                    <FormControl>
                      <input
                        type="checkbox"
                        className="size-4 rounded border-gray-300"
                        checked={field.value}
                        onChange={field.onChange}
                      />
                    </FormControl>
                    <FormLabel className="mt-0">Internal comment (only visible to staff)</FormLabel>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <DialogFooter>
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setIsCommentDialogOpen(false)}
                >
                  Cancel
                </Button>
                <Button
                  type="submit"
                  disabled={isAddingComment}
                >
                  {isAddingComment && (
                    <span className="loading loading-spinner loading-xs mr-2"></span>
                  )}
                  Add Comment
                </Button>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>
    </div>
  )
}

// Escalation Card component
function EscalationCard({
  escalation,
  comments,
  isLoadingComments,
  onResolve,
  onAddComment,
  formatDate,
  getEscalationLevelDetails,
  onSelectEscalation,
  getTicketById,
  isResolved = false,
}: {
  escalation: Escalation
  comments: EscalationComment[]
  isLoadingComments: boolean
  onResolve: () => void
  onAddComment: () => void
  formatDate: (date: string) => string
  getEscalationLevelDetails: (level: number) => { label: string; color: string }
  onSelectEscalation: (escalation: Escalation) => void
  getTicketById: (id: string) => any
  isResolved?: boolean
}) {
  const [isOpen, setIsOpen] = useState(false)
  const ticket = getTicketById(escalation.ticket_id)
  const levelDetails = getEscalationLevelDetails(escalation.level)

  const handleCollapsibleToggle = () => {
    if (!isOpen) {
      onSelectEscalation(escalation)
    }
    setIsOpen(!isOpen)
  }

  return (
    <Card className={isResolved ? "border-green-200" : undefined}>
      <CardHeader className="pb-2">
        <div className="flex flex-col space-y-2 sm:flex-row sm:items-start sm:justify-between sm:space-y-0">
          <div>
            <div className="flex items-center space-x-2">
              <Badge className={levelDetails.color}>{levelDetails.label}</Badge>
              {isResolved && (
                <Badge variant="outline" className="border-green-500 text-green-700">
                  <CheckCircle2 className="mr-1 h-3 w-3" /> Resolved
                </Badge>
              )}
            </div>
            <CardTitle className="mt-1 text-lg">
              {ticket?.description ?? "Unknown ticket"}
            </CardTitle>
            <CardDescription>
              Escalated {formatDate(escalation.escalated_at)} by{" "}
              {escalation.escalator?.first_name} {escalation.escalator?.last_name}
            </CardDescription>
          </div>
          <div className="flex items-center space-x-2">
            {/* {!isResolved && hasPermission('escalation:resolve') && ( */}
              <Button size="sm" onClick={onResolve}>
                <CheckCircle2 className="mr-1 size-4" />
                Resolve
              </Button>
            {/* )} */}
            {/* {hasPermission('escalation:comment') && ( */}
              <Button size="sm" variant="outline" onClick={onAddComment}>
                <MessageCircle className="mr-1 size-4" />
                Comment
              </Button>
            {/* )} */}
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <Collapsible open={isOpen} onOpenChange={handleCollapsibleToggle}>
          <div className="space-y-2">
            <div className="rounded-md bg-muted p-3">
              <div className="flex justify-between items-start mb-1">
                <h4 className="text-sm font-medium">Escalation Reason</h4>
                <Badge variant="outline" className="text-xs">From: {escalation.escalator?.first_name} {escalation.escalator?.last_name}</Badge>
              </div>
              <p className="text-sm">{escalation.reason}</p>
            </div>
            <CollapsibleTrigger asChild>
              <Button variant="ghost" size="sm" className="w-full justify-between">
                {isOpen ? "Hide details" : "Show details"}
                <ChevronDown
                  className={`size-4 transition-transform ${
                    isOpen ? "rotate-180" : ""
                  }`}
                />
              </Button>
            </CollapsibleTrigger>
          </div>
          <CollapsibleContent className="mt-2 space-y-4">
            {isResolved && (
              <div>
                <h4 className="mb-1 font-medium">Resolution Notes</h4>
                <div className="rounded-md bg-green-50 p-3">
                  <p className="text-sm text-green-900">{escalation.resolution_notes}</p>
                </div>
                <p className="mt-1 text-xs text-muted-foreground">
                  Resolved {formatDate(escalation.resolved_at!)} by{" "}
                  {escalation.resolver?.first_name} {escalation.resolver?.last_name}
                </p>
              </div>
            )}

            <div>
              <div className="mb-2 flex items-center justify-between">
                <h4 className="font-medium">Comments</h4>
                <p className="text-xs text-muted-foreground">
                  {comments.length} comment{comments.length !== 1 ? "s" : ""}
                </p>
              </div>

              {isLoadingComments ? (
                <div className="flex h-24 items-center justify-center">
                  <p className="text-sm text-muted-foreground">Loading comments...</p>
                </div>
              ) : comments.length === 0 ? (
                <div className="flex h-24 items-center justify-center border border-dashed rounded-md">
                  <p className="text-sm text-muted-foreground">No comments yet</p>
                </div>
              ) : (
                <div className="space-y-3">
                  {comments.map((comment) => (
                    <div key={comment.id} className={`rounded-md p-3 ${comment.is_internal ? 'bg-blue-50 border border-blue-100' : 'bg-muted'}`}>
                      <div className="flex justify-between items-center">
                        <div className="flex items-center gap-2">
                          <p className="text-xs font-medium">
                            {comment.user?.first_name} {comment.user?.last_name}
                          </p>
                          {comment.is_internal && (
                            <Badge variant="outline" className="text-xs bg-blue-50 text-blue-700 border-blue-200">
                              Internal
                            </Badge>
                          )}
                        </div>
                        <p className="text-xs text-muted-foreground">
                          {formatDate(comment.created_at)}
                        </p>
                      </div>
                      <p className="mt-1 text-sm">{comment.comment}</p>
                    </div>
                  ))}
                </div>
              )}
            </div>

            {ticket && (
              <div>
                <h4 className="mb-1 font-medium">Ticket Details</h4>
                <div className="rounded-md border p-3">
                  <div className="grid grid-cols-2 gap-2 text-sm">
                    <div>
                      <p className="text-xs text-muted-foreground">Status</p>
                      <p className="font-medium capitalize">{ticket.status}</p>
                    </div>
                    <div>
                      <p className="text-xs text-muted-foreground">Priority</p>
                      <p className="font-medium capitalize">{ticket.priority}</p>
                    </div>
                    <div>
                      <p className="text-xs text-muted-foreground">Created</p>
                      <p className="font-medium">{formatDate(ticket.created_at)}</p>
                    </div>
                    <div>
                      <p className="text-xs text-muted-foreground">Due Date</p>
                      <p className="font-medium">
                        {ticket.due_date ? formatDate(ticket.due_date) : "No due date"}
                      </p>
                    </div>
                  </div>
                  <Button
                    variant="link"
                    className="mt-2 h-auto p-0 text-xs"
                    onClick={() => window.open(`/dashboard/tickets/${ticket.id}`, "_blank")}
                  >
                    View full ticket
                  </Button>
                </div>
              </div>
            )}
          </CollapsibleContent>
        </Collapsible>
      </CardContent>
    </Card>
  )
}
