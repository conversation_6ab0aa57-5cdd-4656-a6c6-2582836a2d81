"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import { Filter, MoreHorizontal, Search, Eye, Ticket, Phone, CheckCircle, Clock } from "lucide-react"
import {
  Drawer,
  DrawerClose,
  <PERSON>er<PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
} from "@/components/ui/drawer"
import { <PERSON><PERSON>, <PERSON>bs<PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Separator } from "@/components/ui/separator"
import { useQuery } from "@tanstack/react-query"
import { agentService } from "@/lib/api"
import { Agent } from "@/lib/api/types"


// Mock agents data
const agents = [
  {
    id: "U-1001",
    name: "John Doe",
    email: "<EMAIL>",
    avatar: "/placeholder.svg?height=40&width=40",
    initials: "JD",
    role: "Admin",
    department: "Customer Support",
    ticketsAssigned: 5,
    ticketsResolved: 42,
    avgResponseTime: "1m 24s",
    satisfaction: 98,
    status: "Active",
    availability: "Available",
    skills: ["Account", "Billing", "Technical"],
    performance: {
      callsHandled: 156,
      avgCallTime: 210,
      firstResponseTime: 84,
      resolutionRate: 94,
    },
  },
  {
    id: "U-1002",
    name: "Jane Smith",
    email: "<EMAIL>",
    avatar: "/placeholder.svg?height=40&width=40",
    initials: "JS",
    role: "Agent",
    department: "Technical Support",
    ticketsAssigned: 8,
    ticketsResolved: 38,
    avgResponseTime: "1m 45s",
    satisfaction: 95,
    status: "Active",
    availability: "On Call",
    skills: ["Network", "Technical", "Hardware"],
    performance: {
      callsHandled: 142,
      avgCallTime: 195,
      firstResponseTime: 105,
      resolutionRate: 91,
    },
  },
  {
    id: "U-1005",
    name: "Robert Brown",
    email: "<EMAIL>",
    avatar: "/placeholder.svg?height=40&width=40",
    initials: "RB",
    role: "Agent",
    department: "Billing Support",
    ticketsAssigned: 3,
    ticketsResolved: 35,
    avgResponseTime: "2m 10s",
    satisfaction: 92,
    status: "Active",
    availability: "Available",
    skills: ["Billing", "Payments", "Account"],
    performance: {
      callsHandled: 128,
      avgCallTime: 225,
      firstResponseTime: 130,
      resolutionRate: 88,
    },
  },
  {
    id: "U-1007",
    name: "Sarah Johnson",
    email: "<EMAIL>",
    avatar: "/placeholder.svg?height=40&width=40",
    initials: "SJ",
    role: "Agent",
    department: "Customer Support",
    ticketsAssigned: 6,
    ticketsResolved: 33,
    avgResponseTime: "1m 50s",
    satisfaction: 94,
    status: "Active",
    availability: "Break",
    skills: ["Account", "General", "Onboarding"],
    performance: {
      callsHandled: 120,
      avgCallTime: 240,
      firstResponseTime: 110,
      resolutionRate: 89,
    },
  },
  {
    id: "U-1008",
    name: "Michael Chen",
    email: "<EMAIL>",
    avatar: "/placeholder.svg?height=40&width=40",
    initials: "MC",
    role: "Agent",
    department: "Technical Support",
    ticketsAssigned: 4,
    ticketsResolved: 30,
    avgResponseTime: "1m 30s",
    satisfaction: 96,
    status: "Active",
    availability: "Available",
    skills: ["Network", "Software", "Hardware"],
    performance: {
      callsHandled: 115,
      avgCallTime: 180,
      firstResponseTime: 90,
      resolutionRate: 93,
    },
  },
]

// Update the mock tickets data to match the new status terminology
const tickets = [
  {
    id: "T-1001",
    description: "Unable to access account",
    category: "Account",
    priority: "High",
    status: "Open",
    customer: "Jane Smith",
    assignedTo: "U-1002",
    createdAt: "2023-05-15T09:24:00",
  },
  {
    id: "T-1002",
    description: "Billing discrepancy on last invoice",
    category: "Billing",
    priority: "Medium",
    status: "Pending",
    customer: "John Doe",
    assignedTo: "U-1005",
    createdAt: "2023-05-14T14:30:00",
  },
  {
    id: "T-1005",
    description: "Payment not reflecting on account",
    category: "Billing",
    priority: "High",
    status: "Pending",
    customer: "Robert Brown",
    assignedTo: "U-1008",
    createdAt: "2023-05-14T16:20:00",
  },
  {
    id: "T-1007",
    description: "Internet speed slower than expected",
    category: "Network",
    priority: "Medium",
    status: "Open",
    customer: "David Wilson",
    assignedTo: "U-1002",
    createdAt: "2023-05-14T13:50:00",
  },
]

export default function AgentsPage() {
  const [searchQuery, setSearchQuery] = useState("")
  const [departmentFilter, setDepartmentFilter] = useState("all")
  const [availabilityFilter, setAvailabilityFilter] = useState("all")
  const [isViewAgentOpen, setIsViewAgentOpen] = useState(false)
  const [currentAgent, setCurrentAgent] = useState<any>(null)

  // Fetch agents data
  const {
    data: agentsData,
    isLoading,
    error
  } = useQuery({
    queryKey: ['agents'],
    queryFn: () => agentService.getAllAgents({
      search: searchQuery || undefined,
      department_id: departmentFilter === "all" ? undefined : departmentFilter,
      availability: availabilityFilter === "all" ? undefined : availabilityFilter,
    })
  })


  // Filter agents based on search query and filters
  const filteredAgents = agentsData?.data.filter((agent) => {
    const fullName = `${agent.first_name} ${agent.last_name}`.toLowerCase()
    const matchesSearch =
      fullName.includes(searchQuery.toLowerCase()) ||
      agent.email.toLowerCase().includes(searchQuery.toLowerCase()) ||
      (agent.department?.name || '').toLowerCase().includes(searchQuery.toLowerCase())

    const matchesDepartment =
      departmentFilter === "all" || agent.department?.id === departmentFilter
    const matchesAvailability =
      availabilityFilter === "all" || agent.role_attributes.agent.availability_status.toLowerCase() === availabilityFilter.toLowerCase()

    return matchesSearch && matchesDepartment && matchesAvailability
  }) || []

  // Get tickets assigned to an agent
  const getAgentTickets = (agentId: string) => {
    return tickets.filter((ticket) => ticket.assignedTo === agentId)
  }

  // Format date
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString("en-US", {
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    })
  }

  if (isLoading) {
    return <div>Loading agents...</div>
  }

  if (error) {
    return <div>Error loading agents: {error.message}</div>
  }

  return (
    <div className="flex-1 space-y-4 p-4 md:p-8 pt-6">
      <div className="flex items-center justify-between">
        <h2 className="text-3xl font-bold tracking-tight">Agent Management</h2>
      </div>
      <div className="flex flex-col gap-4">
        <div className="flex items-center gap-2 flex-wrap md:flex-nowrap">
          <div className="flex-1 relative">
            <Search className="absolute left-2.5 top-2.5 size-4 text-muted-foreground" />
            <Input
              placeholder="Search agents..."
              className="pl-8 w-full"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="icon">
                <Filter className="size-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-[200px]">
              <DropdownMenuLabel>Filter by</DropdownMenuLabel>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={() => setDepartmentFilter("all")}>All Departments</DropdownMenuItem>
              <DropdownMenuItem onClick={() => setDepartmentFilter("customer")}>Customer Support</DropdownMenuItem>
              <DropdownMenuItem onClick={() => setDepartmentFilter("technical")}>Technical Support</DropdownMenuItem>
              <DropdownMenuItem onClick={() => setDepartmentFilter("billing")}>Billing Support</DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
          <Select value={availabilityFilter} onValueChange={setAvailabilityFilter}>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Availability" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Status</SelectItem>
              <SelectItem value="available">Available</SelectItem>
              <SelectItem value="on call">On Call</SelectItem>
              <SelectItem value="break">On Break</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Agents</CardTitle>
              <Users className="size-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{filteredAgents.length}</div>
              <p className="text-xs text-muted-foreground">
                {filteredAgents.filter((a) => a.role_attributes.agent.availability_status === "active").length} available
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Open Tickets</CardTitle>
              <Ticket className="size-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{tickets.filter((t) => t.status === "Open").length}</div>
              <p className="text-xs text-muted-foreground">{tickets.filter((t) => !t.assignedTo).length} unassigned</p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Avg. Response Time</CardTitle>
              <Clock className="size-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">1m 42s</div>
              <p className="text-xs text-muted-foreground">-5% from last week</p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Resolution Rate</CardTitle>
              <CheckCircle className="size-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">91%</div>
              <p className="text-xs text-muted-foreground">+2% from last week</p>
            </CardContent>
          </Card>
        </div>

        <div className="rounded-md border">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Agent</TableHead>
                <TableHead>Product</TableHead>
                <TableHead>Availability</TableHead>
                <TableHead>Tickets Assigned</TableHead>
                <TableHead>Performance</TableHead>
                <TableHead>Response Time</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredAgents.map((agent) => (
                <TableRow key={agent.id}>
                  <TableCell className="flex items-center gap-2">
                    <Avatar>
                      <AvatarImage src={`https://api.dicebear.com/7.x/initials/svg?seed=${agent.first_name} ${agent.last_name}`} />
                      <AvatarFallback>{`${agent.first_name[0]}${agent.last_name[0]}`}</AvatarFallback>
                    </Avatar>
                    <div>
                      <p className="font-medium">{`${agent.first_name} ${agent.last_name}`}</p>
                      <p className="text-sm text-muted-foreground">{agent.email}</p>
                    </div>
                  </TableCell>
                  <TableCell>{agent.product?.name || 'Unassigned'}</TableCell>
                  <TableCell>
                    <Badge variant={agent.role_attributes.agent.status === "active" ? "default" : "secondary"}>
                      {agent.role_attributes.agent.status}
                    </Badge>
                  </TableCell>
                  <TableCell>{agent.role_attributes.agent.current_calls}</TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <Progress value={agent.role_attributes.agent.performance_score} className="w-[60px]" />
                      <span className="text-sm">{agent.role_attributes.agent.performance_score}%</span>
                    </div>
                  </TableCell>
                  <TableCell>{`${Math.round(agent.role_attributes.agent.average_handling_time / 60)}m ${agent.role_attributes.agent.average_handling_time % 60}s`}</TableCell>
                  <TableCell className="text-right">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="icon">
                          <MoreHorizontal className="size-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem onClick={() => {
                          setCurrentAgent(agent)
                          setIsViewAgentOpen(true)
                        }}>
                          <Eye className="mr-2 size-4" />
                          View Details
                        </DropdownMenuItem>
                        {/* Add more dropdown items as needed */}
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      </div>

      {/* View Agent Drawer */}
      <Drawer open={isViewAgentOpen} onOpenChange={setIsViewAgentOpen}>
        <DrawerContent className="max-h-[90vh]">
          <DrawerHeader>
            <DrawerTitle>Agent Details</DrawerTitle>
            <DrawerDescription>View agent information and performance</DrawerDescription>
          </DrawerHeader>
          {currentAgent && (
            <div className="px-4 overflow-auto">
              <Tabs defaultValue="overview" className="w-full">
                <TabsList className="grid w-full grid-cols-3">
                  <TabsTrigger value="overview">Overview</TabsTrigger>
                  <TabsTrigger value="performance">Performance</TabsTrigger>
                  <TabsTrigger value="tickets">Assigned Tickets</TabsTrigger>
                </TabsList>
                <TabsContent value="overview" className="space-y-4 py-4">
                  <div className="flex flex-col items-center gap-2 mb-4">
                    <Avatar className="h-20 w-20">
                      <AvatarImage src={currentAgent.avatar} alt={currentAgent.name} />
                      <AvatarFallback className="text-xl">{currentAgent.initials}</AvatarFallback>
                    </Avatar>
                    <h3 className="text-xl font-bold">{currentAgent.name}</h3>
                    <p className="text-sm text-muted-foreground">{currentAgent.email}</p>
                    <div className="flex items-center gap-2 mt-1">
                      <Badge variant="outline">{currentAgent.role.name}</Badge>
                      <AvailabilityBadge availability={currentAgent.role_attributes.agent.status} />
                    </div>
                  </div>

                  <Separator />

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <h3 className="text-sm font-medium text-muted-foreground">Product</h3>
                      <p>{currentAgent.product.name}</p>
                    </div>
                    <div>
                      <h3 className="text-sm font-medium text-muted-foreground">Status</h3>
                      <Badge variant="outline">{currentAgent.status}</Badge>
                    </div>
                    <div>
                      <h3 className="text-sm font-medium text-muted-foreground">Skills</h3>
                      <div className="flex flex-wrap gap-1 mt-1">
                        {currentAgent.role_attributes.agent.skills.map((skill: string) => (
                          <Badge key={skill} variant="secondary" className="text-xs">
                            {skill}
                          </Badge>
                        ))}
                      </div>
                    </div>
                    <div>
                      <h3 className="text-sm font-medium text-muted-foreground">Tickets Assigned</h3>
                      <p>{currentAgent.ticketsAssigned}</p>
                    </div>
                  </div>

                  <Separator />

                  <div className="space-y-2">
                    <h3 className="text-sm font-medium">Performance Summary</h3>
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-1">
                        <p className="text-sm font-medium">Satisfaction Rate</p>
                        <div className="flex items-center gap-2">
                          <span className="text-lg font-bold">{currentAgent.role_attributes.agent.performance_score}%</span>
                          <Progress value={currentAgent.role_attributes.agent.performance_score} className="h-2 flex-1" />
                        </div>
                      </div>
                      <div className="space-y-1">
                        <p className="text-sm font-medium">Resolution Rate</p>
                        <div className="flex items-center gap-2">
                          <span className="text-lg font-bold">{currentAgent.role_attributes.agent.performance_score}%</span>
                          <Progress value={currentAgent.role_attributes.agent.performance_score} className="h-2 flex-1" />
                        </div>
                      </div>
                      <div className="space-y-1">
                        <p className="text-sm font-medium">Avg. Response Time</p>
                        <p className="text-lg font-bold">{currentAgent.role_attributes.agent.total_calls_handled}</p>
                      </div>
                      <div className="space-y-1">
                        <p className="text-sm font-medium">Tickets Resolved</p>
                        <p className="text-lg font-bold">{currentAgent.role_attributes.agent.max_concurrent_chats}</p>
                      </div>
                    </div>
                  </div>
                </TabsContent>

                <TabsContent value="performance" className="space-y-4 py-4">
                  <div className="space-y-4">
                    <div className="grid grid-cols-2 gap-4">
                      <Card>
                        <CardHeader className="pb-2">
                          <CardTitle className="text-sm">Calls Handled</CardTitle>
                        </CardHeader>
                        <CardContent>
                          <div className="text-2xl font-bold">{currentAgent.role_attributes.agent.total_calls_handled}</div>
                          <p className="text-xs text-muted-foreground">Last 30 days</p>
                        </CardContent>
                      </Card>
                      <Card>
                        <CardHeader className="pb-2">
                          <CardTitle className="text-sm">Avg. Call Time</CardTitle>
                        </CardHeader>
                        <CardContent>
                          <div className="text-2xl font-bold">{currentAgent.role_attributes.agent.total_calls_handled}s</div>
                          <p className="text-xs text-muted-foreground">Per call</p>
                        </CardContent>
                      </Card>
                      <Card>
                        <CardHeader className="pb-2">
                          <CardTitle className="text-sm">First Response</CardTitle>
                        </CardHeader>
                        <CardContent>
                          <div className="text-2xl font-bold">{currentAgent.role_attributes.agent.average_handling_time}s</div>
                          <p className="text-xs text-muted-foreground">Average time</p>
                        </CardContent>
                      </Card>
                      <Card>
                        <CardHeader className="pb-2">
                          <CardTitle className="text-sm">Resolution Rate</CardTitle>
                        </CardHeader>
                        <CardContent>
                          <div className="text-2xl font-bold">{currentAgent.role_attributes.agent.performance_score}%</div>
                          <Progress value={currentAgent.role_attributes.agent.performance_score} className="h-2 mt-2" />
                        </CardContent>
                      </Card>
                    </div>

                    <Separator />

                    <div className="space-y-2">
                      <h3 className="text-sm font-medium">Satisfaction Trend</h3>
                      <div className="h-[200px] w-full rounded-md border p-4">
                        <div className="flex h-full items-center justify-center">
                          <p className="text-sm text-muted-foreground">Satisfaction chart will be displayed here</p>
                        </div>
                      </div>
                    </div>

                    <Separator />

                    <div className="space-y-2">
                      <h3 className="text-sm font-medium">Skills Assessment</h3>
                      <div className="space-y-4">
                        {currentAgent.role_attributes.agent.skills.map((skill: string) => (
                          <div key={skill} className="space-y-2">
                            <div className="flex items-center justify-between">
                              <p className="text-sm font-medium">{skill}</p>
                              <p className="text-sm font-medium">{85 + Math.floor(Math.random() * 10)}%</p>
                            </div>
                            <Progress value={85 + Math.floor(Math.random() * 10)} className="h-2" />
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                </TabsContent>

                <TabsContent value="tickets" className="space-y-4 py-4">
                  <div className="space-y-4">
                    <h3 className="text-sm font-medium">Currently Assigned Tickets</h3>
                    {getAgentTickets(currentAgent.id).length > 0 ? (
                      <div className="space-y-2">
                        {getAgentTickets(currentAgent.id).map((ticket) => (
                          <Card key={ticket.id}>
                            <CardContent className="p-4">
                              <div className="flex justify-between items-start">
                                <div>
                                  <div className="flex items-center gap-2">
                                    <span className="font-medium">{ticket.id}</span>
                                    <PriorityBadge priority={ticket.priority} />
                                    <StatusBadge status={ticket.status} />
                                  </div>
                                  <p className="mt-1">{ticket.description}</p>
                                  <div className="mt-2 flex items-center gap-2 text-xs text-muted-foreground">
                                    <span>Customer: {ticket.customer}</span>
                                    <span>•</span>
                                    <span>Created: {formatDate(ticket.createdAt)}</span>
                                  </div>
                                </div>
                                <Button variant="outline" size="sm">
                                  View
                                </Button>
                              </div>
                            </CardContent>
                          </Card>
                        ))}
                      </div>
                    ) : (
                      <p className="text-sm text-muted-foreground">No tickets currently assigned.</p>
                    )}
                  </div>
                </TabsContent>
              </Tabs>
            </div>
          )}
          <DrawerFooter>
            <DrawerClose asChild>
              <Button variant="outline">Close</Button>
            </DrawerClose>
          </DrawerFooter>
        </DrawerContent>
      </Drawer>
    </div>
  )
}

function AvailabilityBadge({ availability }: { availability: string }) {
  const getVariant = () => {
    switch (availability) {
      case "Available":
        return "success"
      case "On Call":
        return "warning"
      case "Break":
        return "secondary"
      default:
        return "outline"
    }
  }

  return <Badge variant={getVariant() as any}>{availability}</Badge>
}

function PriorityBadge({ priority }: { priority: string }) {
  const getVariant = () => {
    switch (priority) {
      case "Critical":
        return "destructive"
      case "High":
        return "destructive"
      case "Medium":
        return "warning"
      case "Low":
        return "secondary"
      default:
        return "secondary"
    }
  }

  return <Badge variant={getVariant() as any}>{priority}</Badge>
}

// Update the StatusBadge function to handle the new statuses
function StatusBadge({ status }: { status: string }) {
  const getVariant = () => {
    switch (status) {
      case "Incoming":
        return "secondary"
      case "Open":
        return "default"
      case "Pending":
        return "warning"
      case "Closed":
        return "success"
      default:
        return "outline"
    }
  }

  return <Badge variant={getVariant() as any}>{status}</Badge>
}

// This component is missing from the imports but used in the code
function Users({ className }: { className?: string }) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      className={className}
    >
      <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2" />
      <circle cx="9" cy="7" r="4" />
      <path d="M22 21v-2a4 4 0 0 0-3-3.87" />
      <path d="M16 3.13a4 4 0 0 1 0 7.75" />
    </svg>
  )
}

