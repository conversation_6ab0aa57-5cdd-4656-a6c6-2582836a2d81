"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Plus, <PERSON>cil, Trash2, MoreH<PERSON>zontal, ShieldAlert } from "lucide-react"
import { usePermissions } from "@/lib/hooks/usePermissions"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Alert, AlertTitle, AlertDescription } from "@/components/ui/alert";
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { Textarea } from "@/components/ui/textarea"
import {
  useProducts,
  useCreateProduct,
  useDeleteProduct,
  useUpdateProduct,
} from "@/lib/hooks/useProducts"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { Product } from "@/lib/api"
import {
  createProductSchema,
  type CreateProductFormValues,
  updateProductSchema,
  type UpdateProductFormValues
} from "@/lib/validations/products"

function CreateProductDialog({
  open,
  onOpenChange,
}: {
  open: boolean
  onOpenChange: (open: boolean) => void
}) {
  const { register, handleSubmit, reset, formState: { errors } } = useForm<CreateProductFormValues>({
    resolver: zodResolver(createProductSchema),
    defaultValues: {
      name: "",
      description: "",
    },
  })

  const createProduct = useCreateProduct()

  const onSubmit = async (data: CreateProductFormValues) => {
    try {
      await createProduct.mutateAsync(data)
      reset()
      onOpenChange(false)
    } catch (error) {
      console.error("Failed to create product:", error)
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>
            Create Product
          </DialogTitle>
          <DialogDescription>
            Add a new product for organizing tickets
          </DialogDescription>
        </DialogHeader>
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="name">Name</Label>
            <Input
              id="name"
              {...register("name")}
              placeholder="Enter product name"
            />
            {errors.name && (
              <p className="text-sm text-destructive">{errors.name.message}</p>
            )}
          </div>
          <div className="space-y-2">
            <Label htmlFor="description">Description</Label>
            <Textarea
              id="description"
              {...register("description")}
              placeholder="Enter product description"
            />
            {errors.description && (
              <p className="text-sm text-destructive">
                {errors.description.message}
              </p>
            )}
          </div>
          <div className="flex justify-end space-x-2">
            <Button
              type="button"
              variant="outline"
              onClick={() => onOpenChange(false)}
            >
              Cancel
            </Button>
            <Button type="submit">
              Create Product
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  )
}

function EditProductDialog({
  product,
  open,
  onOpenChange,
}: {
  product: Product | null
  open: boolean
  onOpenChange: (open: boolean) => void
}) {
  const { register, handleSubmit, reset, formState: { errors } } = useForm<UpdateProductFormValues>({
    resolver: zodResolver(updateProductSchema),
    defaultValues: {
      id: product?.id || "",
      name: product?.name || "",
      description: product?.description || "",
    },
  })

  // Update form values when product changes
  useEffect(() => {
    if (product) {
      reset({
        id: product.id,
        name: product.name,
        description: product.description || "",
      })
    }
  }, [product, reset])

  const updateProduct = useUpdateProduct()

  const onSubmit = async (data: UpdateProductFormValues) => {
    if (!product) return

    try {
      await updateProduct.mutateAsync({
        id: product.id,
        data: {
          name: data.name,
          description: data.description
        }
      })
      reset()
      onOpenChange(false)
    } catch (error) {
      console.error("Failed to update product:", error)
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>
            Edit Product
          </DialogTitle>
          <DialogDescription>
            Update product details
          </DialogDescription>
        </DialogHeader>
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="edit-name">Name</Label>
            <Input
              id="edit-name"
              {...register("name")}
              placeholder="Enter product name"
            />
            {errors.name && (
              <p className="text-sm text-destructive">{errors.name.message}</p>
            )}
          </div>
          <div className="space-y-2">
            <Label htmlFor="edit-description">Description</Label>
            <Textarea
              id="edit-description"
              {...register("description")}
              placeholder="Enter product description"
            />
            {errors.description && (
              <p className="text-sm text-destructive">
                {errors.description.message}
              </p>
            )}
          </div>
          <div className="flex justify-end space-x-2">
            <Button
              type="button"
              variant="outline"
              onClick={() => onOpenChange(false)}
            >
              Cancel
            </Button>
            <Button type="submit">
              Update Product
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  )
}

function ProductRow({
  product,
  onEdit,
  isAdmin = false
}: {
  product: Product,
  onEdit: (product: Product) => void,
  isAdmin?: boolean
}) {
  const deleteProduct = useDeleteProduct()

  const handleDelete = async () => {
    if (
      window.confirm(
        `Are you sure you want to delete ${product.name}? This action cannot be undone.`
      )
    ) {
      try {
        await deleteProduct.mutateAsync(product.id)
      } catch (error) {
        console.error("Failed to delete product:", error)
      }
    }
  }

  return (
    <TableRow>
      <TableCell className="font-medium">{product.name}</TableCell>
      <TableCell>{product.description || "—"}</TableCell>
      <TableCell className="text-right">
        {isAdmin ? (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="h-8 w-8 p-0">
                <span className="sr-only">Open menu</span>
                <MoreHorizontal className="size-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={() => onEdit(product)}>
                <Pencil className="mr-2 size-4" />
                Edit
              </DropdownMenuItem>
              <DropdownMenuItem onClick={handleDelete} className="text-destructive focus:text-destructive">
                <Trash2 className="mr-2 size-4" />
                Delete
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        ) : (
          <span className="text-xs text-muted-foreground">No actions available</span>
        )}
      </TableCell>
    </TableRow>
  )
}

export default function ProductsPage() {
  const [isCreateProductOpen, setIsCreateProductOpen] = useState(false)
  const [isEditProductOpen, setIsEditProductOpen] = useState(false)
  const [selectedProduct, setSelectedProduct] = useState<Product | null>(null)
  const { data: products, isLoading, error } = useProducts()
  const { hasPermission } = usePermissions()
  let errorMessage = "Error loading products. Please try again.";

  // Check if user has admin access
  const isAdmin = hasPermission('admin:access')

  const handleEditProduct = (product: Product) => {
    setSelectedProduct(product)
    setIsEditProductOpen(true)
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-32">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary" />
      </div>
    )
  }

  if (error) {
    if (
      error instanceof TypeError &&
      (error.message.includes("Failed to fetch") ||
        error.message.includes("NetworkError") ||
        error.message.includes("ERR_CONNECTION_REFUSED"))
    ) {
      errorMessage = "System is offline. Please check your connection or contact support.";
    }
  }

  return (
    <>
    {error && (
        <Alert variant="destructive">
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>{errorMessage}</AlertDescription>
        </Alert>
      )}
    <div className="flex-1 space-y-4 p-4 md:p-8 pt-6">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-2">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">
            Product Management
          </h2>
          <p className="text-muted-foreground">
            {isAdmin
              ? "Manage products"
              : "View products (admin access required for management)"}
          </p>
        </div>
        {isAdmin ? (
          <Button onClick={() => setIsCreateProductOpen(true)}>
            <Plus className="mr-2 size-4" />
            Create Product
          </Button>
        ) : (
          <Button variant="outline" disabled>
            <ShieldAlert className="mr-2 size-4" />
            Admin Access Required
          </Button>
        )}
      </div>

      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Name</TableHead>
              <TableHead>Description</TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {products && products.length > 0 ? (
              products.map((product) => (
                <ProductRow
                  key={product.id}
                  product={product}
                  onEdit={handleEditProduct}
                  isAdmin={isAdmin}
                />
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={3} className="h-24 text-center">
                  <p className="text-sm text-muted-foreground">No products found.</p>
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>

      <CreateProductDialog
        open={isCreateProductOpen}
        onOpenChange={setIsCreateProductOpen}
      />

      <EditProductDialog
        product={selectedProduct}
        open={isEditProductOpen}
        onOpenChange={setIsEditProductOpen}
      />
    </div>
    </>
  )
}
