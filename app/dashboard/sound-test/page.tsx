'use client';

import { useState, useEffect, useRef } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';

export default function SoundTestPage() {
  const [testResults, setTestResults] = useState<string[]>([]);
  const audioRef = useRef<HTMLAudioElement | null>(null);
  
  const addResult = (message: string) => {
    setTestResults(prev => [...prev, `${new Date().toISOString().split('T')[1].split('.')[0]} - ${message}`]);
  };
  
  // Test direct audio creation
  const testDirectAudio = () => {
    addResult('Testing direct audio creation...');
    
    try {
      const audio = new Audio('/sounds/incoming-call.mp3');
      addResult('Audio element created successfully');
      
      // Check if we can set properties
      audio.volume = 0.5;
      audio.loop = true;
      addResult(`Audio properties set: volume=${audio.volume}, loop=${audio.loop}`);
      
      // Add event listeners
      audio.addEventListener('canplaythrough', () => {
        addResult('Audio can play through event fired');
      });
      
      audio.addEventListener('error', (e) => {
        addResult(`Audio error: ${audio.error?.code || 'unknown'}`);
      });
      
      // Store the audio element
      audioRef.current = audio;
      
      addResult('Audio element ready for playback');
    } catch (err) {
      addResult(`Error creating audio: ${err instanceof Error ? err.message : String(err)}`);
    }
  };
  
  // Test audio playback
  const testPlayback = () => {
    if (!audioRef.current) {
      addResult('No audio element available. Create one first.');
      return;
    }
    
    addResult('Attempting to play audio...');
    
    try {
      const playPromise = audioRef.current.play();
      
      if (playPromise !== undefined) {
        playPromise
          .then(() => {
            addResult('Audio playing successfully!');
          })
          .catch(err => {
            addResult(`Audio play failed: ${err.name} - ${err.message}`);
          });
      } else {
        addResult('Play method did not return a promise');
      }
    } catch (err) {
      addResult(`Error playing audio: ${err instanceof Error ? err.message : String(err)}`);
    }
  };
  
  // Stop audio playback
  const stopPlayback = () => {
    if (!audioRef.current) {
      addResult('No audio element available.');
      return;
    }
    
    try {
      audioRef.current.pause();
      audioRef.current.currentTime = 0;
      addResult('Audio stopped');
    } catch (err) {
      addResult(`Error stopping audio: ${err instanceof Error ? err.message : String(err)}`);
    }
  };
  
  // Test alternative sound file
  const testWavFile = () => {
    addResult('Testing WAV file...');
    
    try {
      const audio = new Audio('/sounds/incoming-call.wav');
      audioRef.current = audio;
      
      audio.addEventListener('canplaythrough', () => {
        addResult('WAV file can play through');
      });
      
      audio.addEventListener('error', (e) => {
        addResult(`WAV file error: ${audio.error?.code || 'unknown'}`);
      });
      
      const playPromise = audio.play();
      
      if (playPromise !== undefined) {
        playPromise
          .then(() => {
            addResult('WAV file playing successfully!');
          })
          .catch(err => {
            addResult(`WAV file play failed: ${err.name} - ${err.message}`);
          });
      }
    } catch (err) {
      addResult(`Error with WAV file: ${err instanceof Error ? err.message : String(err)}`);
    }
  };
  
  // Check browser audio capabilities
  useEffect(() => {
    addResult('Checking browser audio capabilities...');
    
    // Check if Audio is supported
    if (typeof Audio !== 'undefined') {
      addResult('Audio API is supported');
    } else {
      addResult('Audio API is NOT supported');
    }
    
    // Check if AudioContext is supported
    if (window.AudioContext || (window as any).webkitAudioContext) {
      addResult('AudioContext is supported');
      
      try {
        const AudioContextClass = window.AudioContext || (window as any).webkitAudioContext;
        const context = new AudioContextClass();
        addResult(`AudioContext state: ${context.state}`);
        
        if (context.state === 'suspended') {
          addResult('AudioContext is suspended, user interaction needed');
        }
      } catch (err) {
        addResult(`Error creating AudioContext: ${err instanceof Error ? err.message : String(err)}`);
      }
    } else {
      addResult('AudioContext is NOT supported');
    }
    
    // Check file existence
    fetch('/sounds/incoming-call.mp3')
      .then(response => {
        if (response.ok) {
          addResult('MP3 file exists and is accessible');
        } else {
          addResult(`MP3 file error: ${response.status} ${response.statusText}`);
        }
      })
      .catch(err => {
        addResult(`Error fetching MP3: ${err instanceof Error ? err.message : String(err)}`);
      });
      
    fetch('/sounds/incoming-call.wav')
      .then(response => {
        if (response.ok) {
          addResult('WAV file exists and is accessible');
        } else {
          addResult(`WAV file error: ${response.status} ${response.statusText}`);
        }
      })
      .catch(err => {
        addResult(`Error fetching WAV: ${err instanceof Error ? err.message : String(err)}`);
      });
  }, []);
  
  return (
    <div className="container mx-auto py-10">
      <Card>
        <CardHeader>
          <CardTitle>Sound Test Page</CardTitle>
          <CardDescription>
            Test browser audio capabilities and sound file playback
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex flex-wrap gap-2">
              <Button onClick={testDirectAudio}>Create Audio</Button>
              <Button onClick={testPlayback} variant="default">Play Sound</Button>
              <Button onClick={stopPlayback} variant="destructive">Stop Sound</Button>
              <Button onClick={testWavFile} variant="outline">Test WAV File</Button>
            </div>
            
            <div className="border rounded-md p-4 bg-muted/20 h-[300px] overflow-y-auto">
              <h3 className="font-semibold mb-2">Test Results:</h3>
              <div className="space-y-1 font-mono text-sm">
                {testResults.map((result, index) => (
                  <div key={index} className="border-b border-border/30 pb-1">
                    {result}
                  </div>
                ))}
                {testResults.length === 0 && (
                  <div className="text-muted-foreground">No results yet. Run a test to see output.</div>
                )}
              </div>
            </div>
          </div>
        </CardContent>
        <CardFooter>
          <p className="text-sm text-muted-foreground">
            Note: Some browsers require user interaction before allowing audio playback.
          </p>
        </CardFooter>
      </Card>
    </div>
  );
}
