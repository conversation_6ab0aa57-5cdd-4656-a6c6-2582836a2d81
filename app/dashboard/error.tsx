"use client";

import { useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card";
import { AlertCircle, ArrowLeft, RefreshCw, Home, Terminal, ShieldAlert } from "lucide-react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";
import { logError } from "@/lib/utils/error-logger";

export default function DashboardError({
  error,
  reset,
}: {
  error: Error & { digest?: string };
  reset: () => void;
}) {
  const router = useRouter();

  // Log the error when it occurs
  useEffect(() => {
    logError({
      message: error.message,
      stack: error.stack,
      type: error.name === 'ForbiddenError' || error.message.includes('permission') ? 'permission' : 'other',
      location: typeof window !== 'undefined' ? window.location.href : '',
    });
  }, [error]);

  // Format the error stack for better readability
  const formatStack = (stack?: string) => {
    if (!stack) return "No stack trace available";
    return stack
      .split("\n")
      .map((line) => line.trim())
      .join("\n");
  };

  // Check if it's a permission error
  const isPermissionError = 
    error.name === 'ForbiddenError' || 
    error.message.includes('permission') || 
    error.message.includes('403') || 
    error.message.includes('Forbidden');

  // Determine if this is a known error type and provide helpful messages
  const getErrorHelp = () => {
    const message = error.message.toLowerCase();
    
    if (isPermissionError) {
      return "You don't have permission to access this resource. Please contact your administrator if you believe this is a mistake.";
    }
    
    if (message.includes("network") || message.includes("fetch")) {
      return "This appears to be a network error. Please check your internet connection and try again.";
    }
    
    if (message.includes("not found") || message.includes("404")) {
      return "The resource you're looking for could not be found. It may have been moved or deleted.";
    }
    
    return "We've encountered an unexpected error. Our team has been notified and is working to fix the issue.";
  };

  return (
    <div className="flex min-h-screen items-center justify-center bg-background p-4">
      <Card className="mx-auto max-w-2xl w-full shadow-lg">
        <CardHeader className="space-y-1 pb-2">
          <div className="flex items-center gap-2">
            {isPermissionError ? (
              <ShieldAlert className="h-6 w-6 text-destructive" />
            ) : (
              <AlertCircle className="h-6 w-6 text-destructive" />
            )}
            <CardTitle className="text-2xl">
              {isPermissionError ? "Access Denied" : "Something went wrong"}
            </CardTitle>
          </div>
          <CardDescription className="text-base">
            {getErrorHelp()}
          </CardDescription>
        </CardHeader>
        <CardContent className="pb-2">
          <div className="space-y-4">
            <div className="rounded-md bg-muted p-4">
              <p className="font-mono text-sm break-words">{error.message}</p>
            </div>
            
            {!isPermissionError && (
              <Accordion type="single" collapsible className="w-full">
                <AccordionItem value="technical-details">
                  <AccordionTrigger className="text-sm">
                    <div className="flex items-center gap-2">
                      <Terminal className="size-4" />
                      <span>Technical Details</span>
                    </div>
                  </AccordionTrigger>
                  <AccordionContent>
                    <div className="space-y-2">
                      {error.digest && (
                        <div className="space-y-1">
                          <p className="text-xs font-medium">Error ID:</p>
                          <p className="font-mono text-xs bg-muted p-2 rounded-md">{error.digest}</p>
                        </div>
                      )}
                      {error.stack && (
                        <div className="space-y-1">
                          <p className="text-xs font-medium">Stack Trace:</p>
                          <pre className="font-mono text-xs bg-muted p-2 rounded-md overflow-auto max-h-40">
                            {formatStack(error.stack)}
                          </pre>
                        </div>
                      )}
                    </div>
                  </AccordionContent>
                </AccordionItem>
              </Accordion>
            )}
          </div>
        </CardContent>
        <CardFooter className="flex flex-col sm:flex-row gap-2 pt-2">
          <Button 
            variant="outline" 
            className="w-full sm:w-auto" 
            onClick={() => router.back()}
          >
            <ArrowLeft className="mr-2 size-4" />
            Go Back
          </Button>
          <Button 
            variant="outline" 
            className="w-full sm:w-auto"
            asChild
          >
            <Link href="/dashboard">
              <Home className="mr-2 size-4" />
              Dashboard
            </Link>
          </Button>
          <Button 
            className="w-full sm:w-auto" 
            onClick={() => reset()}
          >
            <RefreshCw className="mr-2 size-4" />
            Try Again
          </Button>
        </CardFooter>
      </Card>
    </div>
  );
}
