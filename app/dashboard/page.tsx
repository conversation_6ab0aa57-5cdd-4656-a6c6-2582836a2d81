"use client"
import { <PERSON>, CardContent, CardDescription, CardH<PERSON>er, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { DashboardStats } from "@/components/dashboard/dashboard-stats"

import { EnhancedCallVolume } from "@/components/dashboard/enhanced-call-volume"
import { EnhancedAgentPerformance } from "@/components/dashboard/enhanced-agent-performance"
import { PeriodSelector } from "@/components/dashboard/period-selector"
import { TicketsTable } from "@/components/tickets/tickets-table"
import { useMyTickets } from "@/lib/hooks/useMyTickets";
import { useDashboardPermissions, getDashboardTitle, shouldShowComponent } from "@/lib/hooks/useDashboardPermissions"

import { useState } from "react"
import type { Ticket } from "@/lib/api/types"

export default function DashboardPage() {
  const [currentTicket, setCurrentTicket] = useState<Ticket | null>(null);
  const { myTickets, isLoading, error } = useMyTickets();
  const permissions = useDashboardPermissions();

  const handleViewTicket = (ticket: Ticket) => {
    setCurrentTicket(ticket);
    // Add any view ticket logic
  };

  const handleAssignTicket = (ticket: Ticket) => {
    setCurrentTicket(ticket);
    // Add any assign ticket logic
  };

  const handleEscalateTicket = (ticket: Ticket) => {
    setCurrentTicket(ticket);
    // Add any escalate ticket logic
  };

  return (
    <div className="flex-1 space-y-4 p-4 md:p-8 pt-6">
      <div className="flex items-center justify-between space-y-2">
        <h2 className="text-3xl font-bold tracking-tight">{getDashboardTitle(permissions)}</h2>
        <div className="flex items-center space-x-2">
          <PeriodSelector />
        </div>
      </div>

      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          {permissions.canViewAdvancedAnalytics && (
            <TabsTrigger value="analytics">Analytics</TabsTrigger>
          )}
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          {/* Dashboard Stats - Always visible */}
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-7">
            <div className="col-span-7">
              <DashboardStats />
            </div>
          </div>

          {/* Call Volume and Agent Performance - Role-based visibility */}
          {(shouldShowComponent('call-volume-analytics', permissions) || shouldShowComponent('agent-performance', permissions)) && (
            <div className="grid gap-4 grid-cols-1 md:grid-cols-7">
              {/* Call Volume Analytics - Only for supervisors and above */}
              {shouldShowComponent('call-volume-analytics', permissions) && (
                <Card className={shouldShowComponent('agent-performance', permissions) ? "col-span-4" : "col-span-7"}>
                  <CardHeader>
                    <CardTitle>Call Volume Analytics</CardTitle>
                    <CardDescription>Interactive call volume analysis with drill-down capabilities</CardDescription>
                  </CardHeader>
                  <CardContent className="p-2">
                    <EnhancedCallVolume
                      showComparative={permissions.canViewAdvancedAnalytics}
                      allowDrillDown={shouldShowComponent('drill-down-analytics', permissions)}
                    />
                  </CardContent>
                </Card>
              )}

              {/* Agent Performance - Role-based content */}
              {shouldShowComponent('agent-performance', permissions) && (
                <Card className={shouldShowComponent('call-volume-analytics', permissions) ? "col-span-3" : "col-span-7"}>
                  <CardHeader>
                    <CardTitle>
                      {permissions.dataScope === 'own' ? 'My Performance' : 'Agent Performance'}
                    </CardTitle>
                    <CardDescription>
                      {permissions.dataScope === 'own'
                        ? 'Your personal performance metrics and targets'
                        : 'Ticket resolution tracking and performance metrics'
                      }
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <Tabs defaultValue="performance" className="space-y-4">
                      <TabsList>
                        <TabsTrigger value="performance">Performance</TabsTrigger>
                        {shouldShowComponent('targets-analytics', permissions) && (
                          <TabsTrigger value="targets">Targets</TabsTrigger>
                        )}
                        {shouldShowComponent('trends-analytics', permissions) && (
                          <TabsTrigger value="trends">Trends</TabsTrigger>
                        )}
                      </TabsList>

                      <div className="p-0">
                        <EnhancedAgentPerformance
                          showTrends={shouldShowComponent('trends-analytics', permissions)}
                          maxAgents={permissions.maxAgentsToShow}
                        />
                      </div>
                    </Tabs>
                  </CardContent>
                </Card>
              )}
            </div>
          )}

          {/* Recent Tickets - Always visible but filtered */}
          <Card>
            <CardHeader>
              <CardTitle>
                {permissions.dataScope === 'own' ? 'My Recent Tickets' : 'Recent Tickets'}
              </CardTitle>
              <CardDescription>
                {permissions.dataScope === 'own'
                  ? 'Your recently assigned and updated tickets'
                  : 'Recently created and updated tickets'
                }
              </CardDescription>
            </CardHeader>
            <CardContent>
              <TicketsTable
                tickets={myTickets}
                onViewTicket={handleViewTicket}
                onAssignTicket={handleAssignTicket}
                onEscalateTicket={handleEscalateTicket}
              />
            </CardContent>
          </Card>
        </TabsContent>

        {/* Advanced Analytics Tab - Only for supervisors and above */}
        {permissions.canViewAdvancedAnalytics && (
          <TabsContent value="analytics" className="space-y-4">
            <div className="grid gap-4 space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Advanced Call Analytics</CardTitle>
                  <CardDescription>Detailed call volume analysis with comparative data and trends</CardDescription>
                </CardHeader>
                <CardContent className="p-0">
                  <EnhancedCallVolume showComparative={true} allowDrillDown={true} />
                </CardContent>
              </Card>
              <Card>
                <CardHeader>
                  <CardTitle>Detailed Agent Performance</CardTitle>
                  <CardDescription>Comprehensive agent performance metrics with targets and trends</CardDescription>
                </CardHeader>
                <CardContent className="p-0">
                  <EnhancedAgentPerformance showTrends={true} maxAgents={permissions.maxAgentsToShow} />
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        )}
      </Tabs>
    </div>
  );
}
