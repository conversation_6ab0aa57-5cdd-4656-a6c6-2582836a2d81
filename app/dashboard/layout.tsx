"use client";

import type React from "react";
import Link from "next/link";
import { AppSidebar } from "@/components/app-sidebar";
import { SidebarProvider } from "@/components/ui/sidebar";
import { Navbar } from "@/components/navbar";
import { Button } from "@/components/ui/button";
import { Volume2 } from "lucide-react";

export default function DashboardLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <SidebarProvider>
      <div className="flex h-screen flex-col w-screen">
        <Navbar />
        <div className="flex flex-1 overflow-hidden">
          <div>

          <AppSidebar />
          </div>
          <div className="flex-1 overflow-auto relative">
            {children}

            {/* Sound Test Button */}
            {/* <div className="fixed bottom-4 right-4">
              <Link href="/dashboard/sound-test">
                <Button size="sm" variant="outline" className="flex items-center gap-2">
                  <Volume2 className="h-4 w-4" />
                  Sound Test
                </Button>
              </Link>
            </div> */}
          </div>
        </div>
      </div>
    </SidebarProvider>
  );
}
