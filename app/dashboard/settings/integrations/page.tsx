import { Card, CardContent, CardDescription, Card<PERSON>eader, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>oot<PERSON> } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Switch } from "@/components/ui/switch"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import { Separator } from "@/components/ui/separator"
import { Badge } from "@/components/ui/badge"
import { AlertCircle, Check, ExternalLink, MessageSquare, Phone, Mail, Database } from "lucide-react"

export default function IntegrationsSettingsPage() {
  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Communication Integrations</CardTitle>
          <CardDescription>Connect your call center with external communication services</CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="grid gap-6 md:grid-cols-2">
            <Card className="border-2 border-primary/20">
              <CardHeader className="pb-2">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Phone className="h-5 w-5 text-primary" />
                    <CardTitle className="text-base">Twilio</CardTitle>
                  </div>
                  <Badge variant="outline" className="bg-primary/10 text-primary">
                    Connected
                  </Badge>
                </div>
                <CardDescription>Voice, SMS, and WhatsApp integration</CardDescription>
              </CardHeader>
              <CardContent className="text-sm">
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Status:</span>
                    <span className="flex items-center gap-1 text-primary">
                      <Check className="h-3 w-3" /> Active
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Account:</span>
                    <span>AC87d*****3a91</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Phone numbers:</span>
                    <span>3 configured</span>
                  </div>
                </div>
              </CardContent>
              <CardFooter className="flex justify-between border-t pt-4">
                <Button variant="outline" size="sm">
                  Configure
                </Button>
                <Button variant="ghost" size="sm" className="text-destructive">
                  Disconnect
                </Button>
              </CardFooter>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <MessageSquare className="h-5 w-5 text-muted-foreground" />
                    <CardTitle className="text-base">Slack</CardTitle>
                  </div>
                  <Badge variant="outline">Not Connected</Badge>
                </div>
                <CardDescription>Receive notifications in Slack channels</CardDescription>
              </CardHeader>
              <CardContent className="text-sm">
                <p className="text-muted-foreground">
                  Connect your Slack workspace to receive ticket notifications and collaborate with your team.
                </p>
              </CardContent>
              <CardFooter className="border-t pt-4">
                <Button size="sm">Connect Slack</Button>
              </CardFooter>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Mail className="h-5 w-5 text-muted-foreground" />
                    <CardTitle className="text-base">Mailgun</CardTitle>
                  </div>
                  <Badge variant="outline">Not Connected</Badge>
                </div>
                <CardDescription>Email delivery service</CardDescription>
              </CardHeader>
              <CardContent className="text-sm">
                <p className="text-muted-foreground">
                  Connect Mailgun to send transactional emails and notifications to customers and agents.
                </p>
              </CardContent>
              <CardFooter className="border-t pt-4">
                <Button size="sm">Connect Mailgun</Button>
              </CardFooter>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <MessageSquare className="h-5 w-5 text-muted-foreground" />
                    <CardTitle className="text-base">WhatsApp Business</CardTitle>
                  </div>
                  <Badge variant="outline">Not Connected</Badge>
                </div>
                <CardDescription>WhatsApp customer communication</CardDescription>
              </CardHeader>
              <CardContent className="text-sm">
                <p className="text-muted-foreground">
                  Connect WhatsApp Business API to communicate with customers via WhatsApp.
                </p>
              </CardContent>
              <CardFooter className="border-t pt-4">
                <Button size="sm">Connect WhatsApp</Button>
              </CardFooter>
            </Card>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>CRM & Ticketing Integrations</CardTitle>
          <CardDescription>Connect with external CRM and ticketing systems</CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="grid gap-6 md:grid-cols-2">
            <Card>
              <CardHeader className="pb-2">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Database className="h-5 w-5 text-muted-foreground" />
                    <CardTitle className="text-base">Salesforce</CardTitle>
                  </div>
                  <Badge variant="outline">Not Connected</Badge>
                </div>
                <CardDescription>CRM integration</CardDescription>
              </CardHeader>
              <CardContent className="text-sm">
                <p className="text-muted-foreground">
                  Sync customer data, tickets, and interactions with Salesforce CRM.
                </p>
              </CardContent>
              <CardFooter className="border-t pt-4">
                <Button size="sm">Connect Salesforce</Button>
              </CardFooter>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <AlertCircle className="h-5 w-5 text-muted-foreground" />
                    <CardTitle className="text-base">Zendesk</CardTitle>
                  </div>
                  <Badge variant="outline">Not Connected</Badge>
                </div>
                <CardDescription>Ticketing system integration</CardDescription>
              </CardHeader>
              <CardContent className="text-sm">
                <p className="text-muted-foreground">Sync tickets and customer support interactions with Zendesk.</p>
              </CardContent>
              <CardFooter className="border-t pt-4">
                <Button size="sm">Connect Zendesk</Button>
              </CardFooter>
            </Card>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Webhook Configuration</CardTitle>
          <CardDescription>Set up webhooks to receive real-time event notifications</CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label htmlFor="webhooks-enabled">Enable webhooks</Label>
                <p className="text-xs text-muted-foreground">Send event notifications to external systems</p>
              </div>
              <Switch id="webhooks-enabled" />
            </div>

            <div className="space-y-2">
              <Label htmlFor="webhook-url">Webhook URL</Label>
              <Input id="webhook-url" placeholder="https://your-app.com/webhook" />
            </div>

            <div className="space-y-2">
              <Label>Events to send</Label>
              <div className="grid grid-cols-1 gap-2 sm:grid-cols-2">
                <div className="flex items-center space-x-2">
                  <Switch id="webhook-ticket-created" />
                  <Label htmlFor="webhook-ticket-created">Ticket created</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Switch id="webhook-ticket-updated" />
                  <Label htmlFor="webhook-ticket-updated">Ticket updated</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Switch id="webhook-ticket-resolved" />
                  <Label htmlFor="webhook-ticket-resolved">Ticket resolved</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Switch id="webhook-user-created" />
                  <Label htmlFor="webhook-user-created">User created</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Switch id="webhook-call-completed" />
                  <Label htmlFor="webhook-call-completed">Call completed</Label>
                </div>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="webhook-secret">Webhook Secret</Label>
              <Input id="webhook-secret" type="password" placeholder="••••••••••••••••" />
              <p className="text-xs text-muted-foreground">
                Used to verify webhook requests are coming from our system
              </p>
            </div>

            <Separator />

            <div className="space-y-2">
              <Label>Webhook Testing</Label>
              <div className="flex items-center gap-2">
                <Button variant="outline">Send Test Webhook</Button>
                <Button variant="outline">View Webhook Logs</Button>
              </div>
            </div>
          </div>

          <Button>Save webhook configuration</Button>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>API Configuration</CardTitle>
          <CardDescription>Manage API access and documentation</CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label htmlFor="api-enabled">Enable API access</Label>
                <p className="text-xs text-muted-foreground">Allow external systems to access your data via API</p>
              </div>
              <Switch id="api-enabled" defaultChecked />
            </div>

            <div className="space-y-2">
              <Label>API Documentation</Label>
              <div className="rounded-md border p-4">
                <div className="flex items-center justify-between">
                  <div className="space-y-1">
                    <h4 className="text-sm font-medium">REST API Documentation</h4>
                    <p className="text-xs text-muted-foreground">Complete documentation for our REST API endpoints</p>
                  </div>
                  <Button variant="outline" size="sm" className="gap-1">
                    <ExternalLink className="h-3.5 w-3.5" />
                    View Docs
                  </Button>
                </div>
              </div>
            </div>

            <div className="space-y-2">
              <Label>Rate Limiting</Label>
              <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                <div className="space-y-2">
                  <Label htmlFor="rate-limit-requests">Requests per minute</Label>
                  <Input id="rate-limit-requests" type="number" defaultValue="60" />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="rate-limit-burst">Burst limit</Label>
                  <Input id="rate-limit-burst" type="number" defaultValue="100" />
                </div>
              </div>
              <p className="text-xs text-muted-foreground">Configure how many API requests can be made per minute</p>
            </div>
          </div>

          <Button>Save API configuration</Button>
        </CardContent>
      </Card>
    </div>
  )
}

