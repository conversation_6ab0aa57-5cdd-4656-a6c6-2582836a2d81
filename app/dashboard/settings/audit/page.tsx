"use client"

import { useState, useEffect } from "react"
import { useAuditLogs } from "@/lib/hooks/useAuditLogs"
import { usePermissions } from "@/lib/hooks/usePermissions"
import { PermissionError } from "@/components/ui/permission-error"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Separator } from "@/components/ui/separator"
import { Badge } from "@/components/ui/badge"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from "@/components/ui/table"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Pagination } from "@/components/ui/pagination"
import {
  AlertCircle,
  CheckCircle,
  Clock,
  Download,
  Filter,
  Info,
  RefreshCw,
  Search,
  XCircle
} from "lucide-react"
import { format } from "date-fns"
import { AUDIT_ACTIONS, AUDIT_RESOURCE_TYPES, AUDIT_STATUS } from "@/lib/constants/audit-actions"

export default function AuditLogsPage() {
  // State for filters
  const [currentPage, setCurrentPage] = useState(1)
  const [limit] = useState(10)
  const [actionFilter, setActionFilter] = useState("all")
  const [resourceTypeFilter, setResourceTypeFilter] = useState("all")
  const [statusFilter, setStatusFilter] = useState("all")
  const [searchQuery, setSearchQuery] = useState("")
  const [startDate, setStartDate] = useState("")
  const [endDate, setEndDate] = useState("")
  const [selectedLog, setSelectedLog] = useState<any>(null)
  const [isDetailsDialogOpen, setIsDetailsDialogOpen] = useState(false)

  // Check permissions
  const { hasPermission, hasAdminAccess } = usePermissions()
  const canViewAuditLogs = hasPermission('system:settings') || hasAdminAccess()

  // Use the audit logs hook
  const {
    auditLogs,
    isLoadingAuditLogs,
    auditLogsError,
    refetchAuditLogs,
    pagination,
    getAllActionTypes,
    getUniqueResourceTypes,
    formatLogDetails,
  } = useAuditLogs({
    page: currentPage,
    limit,
    ...(actionFilter !== 'all' && { action: actionFilter }),
    ...(resourceTypeFilter !== 'all' && { resource_type: resourceTypeFilter }),
    ...(statusFilter !== 'all' && { status: statusFilter }),
    ...(startDate && { start_date: startDate }),
    ...(endDate && { end_date: endDate }),
  })

  // Refetch when filters change
  useEffect(() => {
    refetchAuditLogs()
  }, [currentPage, actionFilter, resourceTypeFilter, statusFilter, startDate, endDate, refetchAuditLogs])

  // Handle view details
  const handleViewDetails = (log: any) => {
    setSelectedLog(log)
    setIsDetailsDialogOpen(true)
  }

  // Format date
  const formatDate = (dateString: string) => {
    try {
      return format(new Date(dateString), 'MMM dd, yyyy HH:mm:ss')
    } catch (error) {
      return dateString
    }
  }

  // Get status badge
  const getStatusBadge = (status: string) => {
    switch (status.toUpperCase()) {
      case AUDIT_STATUS.SUCCESS:
        return <Badge className="bg-green-500"><CheckCircle className="mr-1 h-3 w-3" /> Success</Badge>
      case AUDIT_STATUS.ERROR:
      case 'FAILED':
        return <Badge variant="destructive"><XCircle className="mr-1 h-3 w-3" /> Failed</Badge>
      case AUDIT_STATUS.WARNING:
        return <Badge variant="warning"><AlertCircle className="mr-1 h-3 w-3" /> Warning</Badge>
      case AUDIT_STATUS.INFO:
        return <Badge variant="outline"><Info className="mr-1 h-3 w-3" /> Info</Badge>
      case AUDIT_STATUS.PENDING:
        return <Badge variant="outline"><Clock className="mr-1 h-3 w-3" /> Pending</Badge>
      default:
        return <Badge variant="outline">{status}</Badge>
    }
  }

  // Extract error type from details
  const getErrorType = (details: any): string => {
    if (!details) return 'null';

    // Check for common error type fields
    if (typeof details === 'object') {
      // Direct properties
      if (details.errorType) return details.errorType;
      if (details.error_type) return details.error_type;
      if (details.type) return details.type;

      // Nested in error object
      if (details.error && typeof details.error === 'object') {
        if (details.error.type) return details.error.type;
        if (details.error.name) return details.error.name;
      }

      // Check for statusCode to create a HTTP error type
      if (details.statusCode) return `HTTP Error ${details.statusCode}`;

      // If details is an array, check the first item
      if (Array.isArray(details) && details.length > 0) {
        const firstItem = details[0];
        if (typeof firstItem === 'object') {
          if (firstItem.type) return firstItem.type;
          if (firstItem.errorType) return firstItem.errorType;
          if (firstItem.name) return firstItem.name;
        }
      }
    }

    return 'null';
  }

  // Extract error message from details
  const getErrorMessage = (details: any, fallbackMessage: string | null): string => {
    if (!details) return fallbackMessage || 'No error message available';

    // Check for common error message fields
    if (typeof details === 'object') {
      // Direct properties
      if (details.errorMessage) return details.errorMessage;
      if (details.error_message) return details.error_message;
      if (details.message) return details.message;

      // Nested in error object
      if (details.error && typeof details.error === 'object') {
        if (details.error.message) return details.error.message;
      }

      // If details is an array, check the first item
      if (Array.isArray(details) && details.length > 0) {
        const firstItem = details[0];
        if (typeof firstItem === 'object') {
          if (firstItem.message) return firstItem.message;
          if (firstItem.errorMessage) return firstItem.errorMessage;
        } else if (typeof firstItem === 'string') {
          return firstItem; // If the array contains strings, use the first one
        }
      }
    }

    return fallbackMessage || 'No error message available';
  }

  // If user doesn't have permission, show error
  if (!canViewAuditLogs) {
    return <PermissionError permission="system:settings" />
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
            <div>
              <CardTitle>Audit Logs</CardTitle>
              <CardDescription>View system activity and user actions</CardDescription>
            </div>
            <div className="flex flex-col sm:flex-row gap-2">
              <Button variant="outline" size="sm" onClick={() => refetchAuditLogs()} disabled={isLoadingAuditLogs}>
                <RefreshCw className={`h-4 w-4 mr-2 ${isLoadingAuditLogs ? 'animate-spin' : ''}`} />
                Refresh
              </Button>
              <Button variant="outline" size="sm">
                <Download className="h-4 w-4 mr-2" />
                Export
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Filters */}
          <div className="space-y-4">
            <div className="flex flex-col md:flex-row gap-4">
              <div className="flex-1 relative">
                <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search logs..."
                  className="pl-8"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>
              <div className="flex flex-col sm:flex-row gap-2">
                <Button variant="outline" size="sm" className="flex items-center gap-1">
                  <Filter className="h-4 w-4" />
                  Filters
                </Button>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div>
                <Label htmlFor="action-filter">Action</Label>
                <Select value={actionFilter} onValueChange={setActionFilter}>
                  <SelectTrigger id="action-filter">
                    <SelectValue placeholder="Filter by action" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Actions</SelectItem>
                    {Object.values(AUDIT_ACTIONS).map((action) => (
                      <SelectItem key={action} value={action}>{action.replace(/_/g, ' ')}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label htmlFor="resource-filter">Resource Type</Label>
                <Select value={resourceTypeFilter} onValueChange={setResourceTypeFilter}>
                  <SelectTrigger id="resource-filter">
                    <SelectValue placeholder="Filter by resource" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Resources</SelectItem>
                    {Object.values(AUDIT_RESOURCE_TYPES).map((type) => (
                      <SelectItem key={type} value={type}>{type}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label htmlFor="status-filter">Status</Label>
                <Select value={statusFilter} onValueChange={setStatusFilter}>
                  <SelectTrigger id="status-filter">
                    <SelectValue placeholder="Filter by status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Statuses</SelectItem>
                    {Object.values(AUDIT_STATUS).map((status) => (
                      <SelectItem key={status} value={status}>{status}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label htmlFor="date-filter">Date Range</Label>
                <div className="flex gap-2">
                  <Input
                    id="start-date"
                    type="date"
                    value={startDate}
                    onChange={(e) => setStartDate(e.target.value)}
                  />
                  <Input
                    id="end-date"
                    type="date"
                    value={endDate}
                    onChange={(e) => setEndDate(e.target.value)}
                  />
                </div>
              </div>
            </div>
          </div>

          <Separator />

          {/* Logs Table */}
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Timestamp</TableHead>
                  <TableHead>User</TableHead>
                  <TableHead>Action</TableHead>
                  <TableHead>Resource</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead className="text-right">Details</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {isLoadingAuditLogs ? (
                  <TableRow>
                    <TableCell colSpan={6} className="h-24 text-center">
                      <div className="flex flex-col items-center justify-center">
                        <RefreshCw className="h-6 w-6 animate-spin text-muted-foreground" />
                        <p className="mt-2 text-sm text-muted-foreground">Loading audit logs...</p>
                      </div>
                    </TableCell>
                  </TableRow>
                ) : auditLogsError ? (
                  <TableRow>
                    <TableCell colSpan={6} className="h-24 text-center">
                      <div className="flex flex-col items-center justify-center">
                        <AlertCircle className="h-6 w-6 text-destructive" />
                        <p className="mt-2 text-sm text-destructive">Error loading audit logs</p>
                      </div>
                    </TableCell>
                  </TableRow>
                ) : auditLogs.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={6} className="h-24 text-center">
                      <p className="text-sm text-muted-foreground">No audit logs found</p>
                    </TableCell>
                  </TableRow>
                ) : (
                  auditLogs.map((log) => (
                    <TableRow key={log.id}>
                      <TableCell className="whitespace-nowrap">{formatDate(log.timestamp)}</TableCell>
                      <TableCell>
                        <div className="flex flex-col">
                          <span className="font-medium">{log.user.first_name} {log.user.last_name}</span>
                          <span className="text-xs text-muted-foreground">{log.user.email}</span>
                        </div>
                      </TableCell>
                      <TableCell>{log.action}</TableCell>
                      <TableCell>
                        <div className="flex flex-col">
                          <span>{log.resource_type}</span>
                          <span className="text-xs text-muted-foreground truncate max-w-[150px]">{log.resource_id}</span>
                        </div>
                      </TableCell>
                      <TableCell>{getStatusBadge(log.status)}</TableCell>
                      <TableCell className="text-right">
                        <Button variant="ghost" size="sm" onClick={() => handleViewDetails(log)}>
                          View
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>

          {/* Pagination */}
          {auditLogs.length > 0 && (
            <Pagination
              currentPage={currentPage}
              totalPages={pagination.total_pages || 1}
              onPageChange={setCurrentPage}
            />
          )}
        </CardContent>
      </Card>

      {/* Log Details Dialog */}
      <Dialog open={isDetailsDialogOpen} onOpenChange={setIsDetailsDialogOpen}>
        <DialogContent className="max-w-3xl">
          <DialogHeader>
            <DialogTitle>Audit Log Details</DialogTitle>
            <DialogDescription>
              {selectedLog && (
                <span>
                  {selectedLog.action} on {selectedLog.resource_type} at {formatDate(selectedLog.timestamp)}
                </span>
              )}
            </DialogDescription>
          </DialogHeader>

          {selectedLog && (
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <h4 className="text-sm font-medium mb-1">User</h4>
                  <p className="text-sm">{selectedLog.user.first_name} {selectedLog.user.last_name} ({selectedLog.user.email})</p>
                </div>
                <div>
                  <h4 className="text-sm font-medium mb-1">IP Address</h4>
                  <p className="text-sm">{selectedLog.ip_address}</p>
                </div>
                <div>
                  <h4 className="text-sm font-medium mb-1">User Agent</h4>
                  <p className="text-sm truncate">{selectedLog.user_agent}</p>
                </div>
                <div>
                  <h4 className="text-sm font-medium mb-1">Status</h4>
                  <p className="text-sm">{getStatusBadge(selectedLog.status)}</p>
                </div>
                <div>
                      <h4 className="text-sm font-medium mb-1 text-destructive">Error Type</h4>
                      <p className="text-sm text-destructive">
                        {getErrorType(selectedLog.details)}
                      </p>
                    </div>
                    <div>
                      <h4 className="text-sm font-medium mb-1 text-destructive">Error Message</h4>
                      <p className="text-sm text-destructive">
                        {getErrorMessage(selectedLog.details, selectedLog.error_message)}
                      </p>
                    </div>
              </div>

              <Separator />

              <div>
                <h4 className="text-sm font-medium mb-1">Details</h4>
                <pre className="bg-muted p-4 rounded-md text-xs overflow-auto max-h-[300px]">
                  {formatLogDetails(selectedLog.details)}
                </pre>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  )
}
