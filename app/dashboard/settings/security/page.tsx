import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { Separator } from "@/components/ui/separator"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { ShieldAlert, Info, KeyRound } from "lucide-react"

export default function SecuritySettingsPage() {
  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Password Policy</CardTitle>
          <CardDescription>Configure password requirements for all users</CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="space-y-4">
            <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
              <div className="space-y-2">
                <Label htmlFor="min-length">Minimum password length</Label>
                <Input id="min-length" type="number" defaultValue="8" min="6" max="32" />
              </div>
              <div className="space-y-2">
                <Label htmlFor="max-age">Password expiration (days)</Label>
                <Input id="max-age" type="number" defaultValue="90" min="0" max="365" />
                <p className="text-xs text-muted-foreground">Set to 0 for no expiration</p>
              </div>
            </div>

            <div className="space-y-4">
              <Label>Password requirements</Label>

              <div className="flex items-center space-x-2">
                <Switch id="require-uppercase" defaultChecked />
                <Label htmlFor="require-uppercase">Require at least one uppercase letter</Label>
              </div>

              <div className="flex items-center space-x-2">
                <Switch id="require-lowercase" defaultChecked />
                <Label htmlFor="require-lowercase">Require at least one lowercase letter</Label>
              </div>

              <div className="flex items-center space-x-2">
                <Switch id="require-number" defaultChecked />
                <Label htmlFor="require-number">Require at least one number</Label>
              </div>

              <div className="flex items-center space-x-2">
                <Switch id="require-special" defaultChecked />
                <Label htmlFor="require-special">Require at least one special character</Label>
              </div>

              <div className="flex items-center space-x-2">
                <Switch id="prevent-reuse" defaultChecked />
                <Label htmlFor="prevent-reuse">Prevent password reuse (last 5 passwords)</Label>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="timeout-attempts">Account timeout threshold</Label>
              <Input id="timeout-attempts" type="number" defaultValue="5" min="1" max="10" />
              <p className="text-xs text-muted-foreground">Number of failed login attempts before account is timed out</p>
            </div>

            <div className="space-y-2">
              <Label htmlFor="timeout-duration">Account timeout duration (minutes)</Label>
              <Input id="timeout-duration" type="number" defaultValue="30" min="5" max="1440" />
            </div>
          </div>

          <Button>Save password policy</Button>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Two-Factor Authentication</CardTitle>
          <CardDescription>Configure two-factor authentication settings</CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label htmlFor="require-2fa">Require 2FA for all users</Label>
                <p className="text-sm text-muted-foreground">Force all users to set up two-factor authentication</p>
              </div>
              <Switch id="require-2fa" />
            </div>

            <Separator />

            <div className="space-y-2">
              <Label htmlFor="2fa-methods">Allowed 2FA methods</Label>
              <div className="space-y-2">
                <div className="flex items-center space-x-2">
                  <Switch id="2fa-app" defaultChecked />
                  <Label htmlFor="2fa-app">Authenticator app (TOTP)</Label>
                </div>

                <div className="flex items-center space-x-2">
                  <Switch id="2fa-sms" defaultChecked />
                  <Label htmlFor="2fa-sms">SMS</Label>
                </div>

                <div className="flex items-center space-x-2">
                  <Switch id="2fa-email" defaultChecked />
                  <Label htmlFor="2fa-email">Email</Label>
                </div>

                <div className="flex items-center space-x-2">
                  <Switch id="2fa-security-key" />
                  <Label htmlFor="2fa-security-key">Security key (WebAuthn/FIDO2)</Label>
                </div>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="2fa-remember">Remember 2FA for (days)</Label>
              <Input id="2fa-remember" type="number" defaultValue="30" min="0" max="90" />
              <p className="text-xs text-muted-foreground">
                How long to remember 2FA verification on trusted devices (0 to always require)
              </p>
            </div>
          </div>

          <Button>Save 2FA settings</Button>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Session Management</CardTitle>
          <CardDescription>Configure user session settings</CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="session-timeout">Session timeout (minutes)</Label>
              <Input id="session-timeout" type="number" defaultValue="60" min="5" max="1440" />
              <p className="text-xs text-muted-foreground">How long until an inactive session expires</p>
            </div>

            <div className="space-y-2">
              <Label htmlFor="max-sessions">Maximum concurrent sessions</Label>
              <Input id="max-sessions" type="number" defaultValue="5" min="1" max="10" />
              <p className="text-xs text-muted-foreground">Maximum number of active sessions per user</p>
            </div>

            <div className="flex items-center space-x-2">
              <Switch id="force-logout" />
              <Label htmlFor="force-logout">Force logout on password change</Label>
            </div>
          </div>

          <Button>Save session settings</Button>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <ShieldAlert className="h-5 w-5 text-destructive" />
            <span>API Security</span>
          </CardTitle>
          <CardDescription>Manage API keys and access tokens</CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <Alert>
            <Info className="size-4" />
            <AlertTitle>Information</AlertTitle>
            <AlertDescription>
              API keys provide full access to your account. Keep them secure and never share them publicly.
            </AlertDescription>
          </Alert>

          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <h4 className="text-sm font-medium">Production API Key</h4>
                <p className="text-xs text-muted-foreground">Created on May 12, 2023</p>
              </div>
              <div className="flex items-center gap-2">
                <Button variant="outline" size="sm">
                  <KeyRound className="mr-2 size-4" />
                  View Key
                </Button>
                <Button variant="destructive" size="sm">
                  Revoke
                </Button>
              </div>
            </div>

            <Separator />

            <div className="flex items-center justify-between">
              <div>
                <h4 className="text-sm font-medium">Development API Key</h4>
                <p className="text-xs text-muted-foreground">Created on June 3, 2023</p>
              </div>
              <div className="flex items-center gap-2">
                <Button variant="outline" size="sm">
                  <KeyRound className="mr-2 size-4" />
                  View Key
                </Button>
                <Button variant="destructive" size="sm">
                  Revoke
                </Button>
              </div>
            </div>
          </div>

          <Button>Generate New API Key</Button>
        </CardContent>
      </Card>
    </div>
  )
}

