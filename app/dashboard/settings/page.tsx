import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Button } from "@/components/ui/button"
import { Switch } from "@/components/ui/switch"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Separator } from "@/components/ui/separator"

export default function GeneralSettingsPage() {
  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Company Information</CardTitle>
          <CardDescription>Update your company details and information</CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
            <div className="space-y-2">
              <Label htmlFor="company-name">Company name</Label>
              <Input id="company-name" defaultValue="CallCenter Inc." />
            </div>
            <div className="space-y-2">
              <Label htmlFor="company-website">Website</Label>
              <Input id="company-website" defaultValue="https://callcenter.example.com" />
            </div>
            <div className="space-y-2">
              <Label htmlFor="company-email">Support email</Label>
              <Input id="company-email" type="email" defaultValue="<EMAIL>" />
            </div>
            <div className="space-y-2">
              <Label htmlFor="company-phone">Support phone</Label>
              <Input id="company-phone" defaultValue="+****************" />
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="company-address">Address</Label>
            <Input id="company-address" defaultValue="123 Business Ave, Suite 100" />
          </div>

          <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
            <div className="space-y-2">
              <Label htmlFor="company-city">City</Label>
              <Input id="company-city" defaultValue="San Francisco" />
            </div>
            <div className="space-y-2">
              <Label htmlFor="company-state">State</Label>
              <Input id="company-state" defaultValue="CA" />
            </div>
            <div className="space-y-2">
              <Label htmlFor="company-zip">ZIP / Postal code</Label>
              <Input id="company-zip" defaultValue="94103" />
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="company-country">Country</Label>
            <Select defaultValue="us">
              <SelectTrigger id="company-country">
                <SelectValue placeholder="Select country" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="us">United States</SelectItem>
                <SelectItem value="ca">Canada</SelectItem>
                <SelectItem value="uk">United Kingdom</SelectItem>
                <SelectItem value="au">Australia</SelectItem>
                <SelectItem value="ke">Kenya</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <Button>Save company information</Button>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>System Settings</CardTitle>
          <CardDescription>Configure global system settings and defaults</CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label htmlFor="auto-assign">Auto-assign tickets</Label>
                <p className="text-sm text-muted-foreground">Automatically assign new tickets to available agents</p>
              </div>
              <Switch id="auto-assign" defaultChecked />
            </div>

            <Separator />

            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label htmlFor="auto-close">Auto-close resolved tickets</Label>
                <p className="text-sm text-muted-foreground">
                  Automatically close tickets after they have been resolved for a certain period
                </p>
              </div>
              <Switch id="auto-close" />
            </div>

            <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
              <div className="space-y-2">
                <Label htmlFor="auto-close-days">Days until auto-close</Label>
                <Input id="auto-close-days" type="number" defaultValue="7" />
              </div>
              <div className="space-y-2">
                <Label htmlFor="default-priority">Default ticket priority</Label>
                <Select defaultValue="medium">
                  <SelectTrigger id="default-priority">
                    <SelectValue placeholder="Select priority" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="low">Low</SelectItem>
                    <SelectItem value="medium">Medium</SelectItem>
                    <SelectItem value="high">High</SelectItem>
                    <SelectItem value="critical">Critical</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <Separator />

            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label htmlFor="maintenance-mode">Maintenance mode</Label>
                <p className="text-sm text-muted-foreground">
                  Put the system in maintenance mode (only admins can access)
                </p>
              </div>
              <Switch id="maintenance-mode" />
            </div>
          </div>

          <Button>Save system settings</Button>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Business Hours</CardTitle>
          <CardDescription>Set your call center's operating hours</CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label>24/7 Operation</Label>
                <p className="text-sm text-muted-foreground">
                  Enable if your call center operates 24 hours a day, 7 days a week
                </p>
              </div>
              <Switch id="24-7-operation" />
            </div>

            <Separator />

            <div className="space-y-4">
              <Label>Business Days & Hours</Label>

              {["Monday", "Tuesday", "Wednesday", "Thursday", "Friday"].map((day) => (
                <div key={day} className="grid grid-cols-8 items-center gap-4">
                  <div className="col-span-2">
                    <div className="flex items-center space-x-2">
                      <Switch id={`${day.toLowerCase()}-active`} defaultChecked />
                      <Label htmlFor={`${day.toLowerCase()}-active`}>{day}</Label>
                    </div>
                  </div>
                  <div className="col-span-3">
                    <Select defaultValue="09:00">
                      <SelectTrigger id={`${day.toLowerCase()}-start`}>
                        <SelectValue placeholder="Start time" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="08:00">8:00 AM</SelectItem>
                        <SelectItem value="09:00">9:00 AM</SelectItem>
                        <SelectItem value="10:00">10:00 AM</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="col-span-3">
                    <Select defaultValue="17:00">
                      <SelectTrigger id={`${day.toLowerCase()}-end`}>
                        <SelectValue placeholder="End time" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="17:00">5:00 PM</SelectItem>
                        <SelectItem value="18:00">6:00 PM</SelectItem>
                        <SelectItem value="19:00">7:00 PM</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              ))}

              {["Saturday", "Sunday"].map((day) => (
                <div key={day} className="grid grid-cols-8 items-center gap-4">
                  <div className="col-span-2">
                    <div className="flex items-center space-x-2">
                      <Switch id={`${day.toLowerCase()}-active`} />
                      <Label htmlFor={`${day.toLowerCase()}-active`}>{day}</Label>
                    </div>
                  </div>
                  <div className="col-span-3">
                    <Select defaultValue="closed" disabled>
                      <SelectTrigger id={`${day.toLowerCase()}-start`}>
                        <SelectValue placeholder="Start time" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="closed">Closed</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="col-span-3">
                    <Select defaultValue="closed" disabled>
                      <SelectTrigger id={`${day.toLowerCase()}-end`}>
                        <SelectValue placeholder="End time" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="closed">Closed</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              ))}
            </div>
          </div>

          <Button>Save business hours</Button>
        </CardContent>
      </Card>
    </div>
  )
}

