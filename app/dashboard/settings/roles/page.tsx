"use client"

import { useState } from "react"
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import { toast } from "@/components/ui/use-toast"
import { MoreHorizontal, Pencil, Plus, Trash2 } from "lucide-react"
import { Label } from "@/components/ui/label"
import type { Role } from "@/lib/api/types"
import { roleService } from "@/lib/api/roles"
import { type RoleUpdate, roleUpdateSchema, permissionTypes, permissionCategories, getRequiredPermissions } from "@/lib/validations/roles"
import { PermissionError } from "@/components/ui/permission-error"

import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { Textarea } from "@/components/ui/textarea"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"

import { useRoles } from "@/lib/hooks/useRoles"
import { usePermissions } from "@/lib/hooks/usePermissions"
import { RoleComparison } from "@/components/roles/role-comparison"
import { RolePermissions } from "@/components/roles/role-permissions"
import { RoleAssignment } from "@/components/roles/role-assignment"
import { Switch } from "@/components/ui/switch"
import { Separator } from "@/components/ui/separator"

export default function RolesSettingsPage() {
  const { roles, isLoading, error, refetchRoles, getRoleNameByLevel, availableRoleLevels } = useRoles()
  const { hasPermission, hasAdminAccess } = usePermissions()
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  const [currentRole, setCurrentRole] = useState<Role | null>(null)
  const [searchQuery, setSearchQuery] = useState("")

  // Log permissions for debugging
  console.log('Roles page loaded, checking permissions:');
  console.log('Can create role:', hasPermission('role:create'));
  console.log('Can delete role:', hasPermission('role:delete'));
  console.log('Can view role:', hasPermission('role:view'));
  console.log('Has admin access:', hasAdminAccess());

  const filteredRoles = roles?.filter(
    (role) =>
      role.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      role.description.toLowerCase().includes(searchQuery.toLowerCase()),
  ) || []

  const form = useForm<RoleUpdate>({
    resolver: zodResolver(roleUpdateSchema),
    defaultValues: {
      name: "",
      description: "",
      permission_level: 1,
      permissions: [],
    },
  })

  const handleEditRole = (role: Role) => {
    // For API permissions like 'ticket:view', we need to map them to our UI format 'tickets.view'
    const mappedPermissions = role.permissions.map(p => {
      // Handle both formats: objects with category/code or strings like 'ticket:view'
      if (typeof p === 'string') {
        const [category, action] = p.split(':');
        // Convert singular category names to plural as expected by our UI
        const pluralCategory = category.endsWith('s') ? category : `${category}s`;
        return `${pluralCategory}.${action}`;
      } else {
        return `${p.category}.${p.code}`;
      }
    });

    const roleUpdate: RoleUpdate = {
      name: role.name,
      description: role.description,
      permission_level: role.permission_level,
      permissions: mappedPermissions,
    }
    form.reset(roleUpdate)
    setCurrentRole(role)
    setIsEditDialogOpen(true)
  }

  const handleCreateRole = () => {
    form.reset()
    setCurrentRole(null)
    setIsEditDialogOpen(true)
  }

  const handleSaveRole = async (data: RoleUpdate) => {
    try {
      // Validate the data
      const validatedData = roleUpdateSchema.parse(data)

      // Map permissions to the format expected by the API
      // From: ['tickets.view', 'users.create']
      // To: ['ticket:view', 'user:create']
      const mappedPermissions = validatedData.permissions.map(permission => {
        const [category, action] = permission.split('.');
        // Convert plural category names to singular as expected by the API
        const singularCategory = category.endsWith('s') ? category.slice(0, -1) : category;
        return `${singularCategory}:${action}`;
      });

      // Determine permission level based on role name if not explicitly set
      let permissionLevel = validatedData.permission_level;
      const roleName = validatedData.name.toLowerCase();

      // Apply role hierarchy rules if the name matches a known role type
      if (!currentRole) {
        if (roleName.includes('platform owner') || roleName.includes('platform_owner')) {
          permissionLevel = 10; // Platform Owner
        } else if (roleName.includes('super admin')) {
          permissionLevel = 5; // Super Admin
        } else if (roleName.includes('operation manager') || roleName.includes('operations manager')) {
          permissionLevel = 4; // Operation Manager
        } else if (roleName.includes('head of department') || roleName.includes('department head')) {
          permissionLevel = 3; // Head of Department
        } else if (roleName.includes('team lead') || roleName.includes('supervisor')) {
          permissionLevel = 2; // Team Lead/Supervisor
        } else if (roleName.includes('agent')) {
          permissionLevel = 1; // Agent
        } else if (roleName.includes('user')) {
          permissionLevel = 1; // Normal User (same level as Agent)
        }
      }

      const rolePayload = {
        name: validatedData.name,
        description: validatedData.description,
        permission_level: permissionLevel,
        permissions: mappedPermissions
      };

      // Call the appropriate API method based on whether we're creating or updating
      let result;
      try {
        if (currentRole) {
          console.log('Updating role with payload:', JSON.stringify(rolePayload, null, 2));
          result = await roleService.updateRole(currentRole.id, rolePayload);
          console.log('Update role response:', result);
        } else {
          console.log('Creating role with payload:', JSON.stringify(rolePayload, null, 2));
          result = await roleService.createRole(rolePayload);
          console.log('Create role response:', result);
        }
      } catch (apiError) {
        console.error('API error:', apiError);
        // Log detailed error information
        if (apiError instanceof Error) {
          console.error('Error message:', apiError.message);
          console.error('Error stack:', apiError.stack);
        }
        throw apiError; // Re-throw to be caught by the outer catch block
      }

      toast({
        title: "Success",
        description: `Role ${currentRole ? 'updated' : 'created'} successfully`,
      })

      setIsEditDialogOpen(false)
      setCurrentRole(null)
      refetchRoles()
    } catch (error) {
      console.error('Error saving role:', error);

      // Extract error message from the API response if available
      let errorMessage = "Failed to save role. Please try again.";
      if (error instanceof Error) {
        errorMessage = error.message || errorMessage;
      }

      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      })
    }
  }

  const handleDeleteRole = async (roleId: string) => {
    try {
      // Call API to delete role
      console.log(`Deleting role with ID: ${roleId}`);
      const result = await roleService.deleteRole(roleId);
      console.log('Delete role response:', result);

      toast({
        title: "Success",
        description: "Role deleted successfully",
      })
      refetchRoles()
    } catch (error) {
      console.error('Error deleting role:', error);

      // Extract error message from the API response if available
      let errorMessage = "Failed to delete role. Please try again.";
      if (error instanceof Error) {
        errorMessage = error.message || errorMessage;
      }

      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      })
    }
  }

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-center h-32">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary" />
        </div>
      </div>
    )
  }

  if (error) {
    // Check if it's a permission error (403 Forbidden)
    const isPermissionError = error instanceof Error &&
      (error.message.includes("permission") ||
       error.message.includes("403") ||
       error.message.includes("Forbidden"));

    return (
      <div className="space-y-6">
        {isPermissionError ? (
          <PermissionError
            message="You do not have the required permissions to view roles. Please contact your administrator for assistance."
          />
        ) : (
          <div className="flex items-center justify-center h-32">
            <div className="text-destructive">Failed to load roles. Please try again.</div>
          </div>
        )}
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <Tabs defaultValue="roles" className="space-y-6">
        <TabsList>
          <TabsTrigger value="roles">Roles</TabsTrigger>
          <TabsTrigger value="comparison">Compare</TabsTrigger>
          <TabsTrigger value="assignment">Assignment</TabsTrigger>
        </TabsList>

        <TabsContent value="roles" className="space-y-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-6">
              <div>
                <CardTitle>Role Management</CardTitle>
                <CardDescription>Create and manage roles and their permissions</CardDescription>
              </div>
              <Button
                onClick={handleCreateRole}
                disabled={!hasPermission('role:create')}
              >
                <Plus className="mr-2 size-4" /> Add Role
              </Button>
            </CardHeader>
            <CardContent>
              <div className="flex items-center py-4">
                <Input
                  placeholder="Search roles..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="max-w-sm"
                />
              </div>

              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead className="w-[200px]">Name</TableHead>
                      <TableHead className="w-[300px]">Description</TableHead>
                      <TableHead>Level</TableHead>
                      <TableHead>Type</TableHead>
                      <TableHead className="text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredRoles.map((role) => (
                      <TableRow key={role.id}>
                        <TableCell className="font-medium">{role.name}</TableCell>
                        <TableCell>{role.description}</TableCell>
                        <TableCell>
                          <Badge variant="outline">
                            {getRoleNameByLevel(role.permission_level)}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          {role.is_system_role ? (
                            <Badge variant="secondary">System</Badge>
                          ) : (
                            <Badge>Custom</Badge>
                          )}
                        </TableCell>
                        <TableCell className="text-right">
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" className="h-8 w-8 p-0">
                                <span className="sr-only">Open menu</span>
                                <MoreHorizontal className="size-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuLabel>Actions</DropdownMenuLabel>
                              <DropdownMenuItem
                                onClick={() => handleEditRole(role)}
                                disabled={!hasPermission('role:update')}
                              >
                                <Pencil className="mr-2 size-4" />
                                Edit
                              </DropdownMenuItem>
                              <DropdownMenuSeparator />
                              <DropdownMenuItem
                                onClick={() => handleDeleteRole(role.id)}
                                disabled={!hasPermission('role:delete')}
                                className="text-destructive"
                              >
                                <Trash2 className="mr-2 size-4" />
                                Delete
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="comparison" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Role Comparison</CardTitle>
              <CardDescription>Compare permissions between different roles</CardDescription>
            </CardHeader>
            <CardContent>
              <RoleComparison roles={roles || []} />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="assignment" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Role Assignment</CardTitle>
              <CardDescription>Assign roles to users</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid gap-6">
                <div className="flex flex-col space-y-4">
                  <div className="flex flex-col space-y-2">
                    <Label>Select Role</Label>
                    <RoleAssignment
                      roles={roles || []}
                      selectedRoleId={currentRole?.id}
                      onRoleSelect={(roleId) => {
                        const role = roles?.find((r) => r.id === roleId)
                        if (role) {
                          setCurrentRole(role)
                        }
                      }}
                      disabled={!hasPermission('role:assign')}
                    />
                  </div>
                  {currentRole && (
                    <div className="space-y-4">
                      <Label>Role Permissions</Label>
                      <RolePermissions role={currentRole} />
                    </div>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <form onSubmit={form.handleSubmit(handleSaveRole)}>
            <DialogHeader>
              <DialogTitle>{currentRole ? "Edit Role" : "Create Role"}</DialogTitle>
              <DialogDescription>
                {currentRole
                  ? "Make changes to the role and its permissions."
                  : "Create a new role and assign permissions."}
              </DialogDescription>
            </DialogHeader>

            <div className="grid gap-6 py-4">
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="name">Role Name</Label>
                  <Input
                    id="name"
                    {...form.register("name")}
                    className="mt-2"
                    placeholder="Enter role name"
                  />
                  {form.formState.errors.name && (
                    <p className="text-sm text-destructive">{form.formState.errors.name.message}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="description">Description</Label>
                  <Input
                    id="description"
                    {...form.register("description")}
                    className="mt-2"
                    placeholder="Enter role description"
                  />
                  {form.formState.errors.description && (
                    <p className="text-sm text-destructive">{form.formState.errors.description.message}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="permission_level">Permission Level</Label>
                  <select
                    id="permission_level"
                    {...form.register("permission_level", { valueAsNumber: true })}
                    className="w-full p-2 border rounded-md mt-2"
                  >
                    {availableRoleLevels.map((level) => (
                      <option key={level} value={level}>
                        {getRoleNameByLevel(level)}
                      </option>
                    ))}
                  </select>
                  {form.formState.errors.permission_level && (
                    <p className="text-sm text-destructive">{form.formState.errors.permission_level.message}</p>
                  )}
                </div>

                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <h5 className="font-medium">Permissions</h5>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        const permissions = form.watch('permissions');
                        const allPermissions = permissionCategories.flatMap(category =>
                          permissionTypes.map(pt => `${category}.${pt}`)
                        );
                        const hasAllPermissions = allPermissions.every(p => permissions.includes(p));

                        if (hasAllPermissions) {
                          // Disable all permissions
                          form.setValue('permissions', []);
                        } else {
                          // Enable all permissions
                          form.setValue('permissions', allPermissions);
                        }
                      }}
                    >
                      {permissionCategories.every(category =>
                        permissionTypes.every(pt =>
                          form.watch('permissions').includes(`${category}.${pt}`)
                        )
                      ) ? 'Disable All' : 'Enable All'}
                    </Button>
                  </div>
                  <div className="space-y-6">
                    {permissionCategories.map((category) => (
                      <div key={category} className="space-y-2">
                        <div className="flex items-center justify-between">
                          <h6 className="text-sm font-medium capitalize">{category}</h6>
                          <div className="flex items-center gap-2">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => {
                                const permissions = form.watch('permissions');
                                const categoryPermissions = permissionTypes.map(pt => `${category}.${pt}`);
                                const hasAllPermissions = categoryPermissions.every(p => permissions.includes(p));

                                if (hasAllPermissions) {
                                  // Remove all permissions in this category and their dependents
                                  form.setValue(
                                    'permissions',
                                    permissions.filter(p => !p.startsWith(`${category}.`))
                                  );
                                } else {
                                  // Add all permissions in this category
                                  const newPermissions = [...permissions];
                                  categoryPermissions.forEach(p => {
                                    if (!newPermissions.includes(p)) {
                                      newPermissions.push(p);
                                    }
                                  });
                                  form.setValue('permissions', newPermissions);
                                }
                              }}
                            >
                              {permissionTypes.every(pt =>
                                form.watch('permissions').includes(`${category}.${pt}`)
                              ) ? 'Disable All' : 'Enable All'}
                            </Button>
                          </div>
                        </div>
                        <div className="grid grid-cols-2 gap-4">
                          {permissionTypes.map((permission) => (
                            <div key={`${category}-${permission}`} className="flex items-center space-x-2">
                              <Switch
                                id={`${category}-${permission}`}
                                checked={form.watch('permissions').includes(`${category}.${permission}`)}
                                onCheckedChange={(checked) => {
                                  const permissions = form.watch('permissions');
                                  const permissionKey = `${category}.${permission}`;
                                  const requiredPermissions = getRequiredPermissions(category, permission);

                                  if (checked) {
                                    // When enabling a permission, add all required dependencies
                                    const newPermissions = [...permissions, permissionKey];
                                    requiredPermissions.forEach(dep => {
                                      if (!newPermissions.includes(dep)) {
                                        newPermissions.push(dep);
                                      }
                                    });
                                    form.setValue('permissions', newPermissions);
                                  } else {
                                    // When disabling a permission, remove it and any permissions that depend on it
                                    const dependentPermissions = permissionTypes
                                      .filter(pt => getRequiredPermissions(category, pt).includes(permissionKey))
                                      .map(pt => `${category}.${pt}`);

                                    form.setValue(
                                      'permissions',
                                      permissions.filter(p => p !== permissionKey && !dependentPermissions.includes(p))
                                    );
                                  }
                                }}
                                disabled={
                                  // Disable if any required permissions are missing
                                  !form.watch('permissions').includes(`${category}.${permission}`) &&
                                  getRequiredPermissions(category, permission).some(
                                    dep => !form.watch('permissions').includes(dep)
                                  )
                                }
                              />
                              <Label htmlFor={`${category}-${permission}`}>
                                {permission.charAt(0).toUpperCase() + permission.slice(1)} {category.charAt(0).toUpperCase() + category.slice(1)}
                              </Label>
                            </div>
                          ))}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>

            <DialogFooter>
              <Button variant="outline" onClick={() => setIsEditDialogOpen(false)} type="button">
                Cancel
              </Button>
              <Button type="submit" disabled={form.formState.isSubmitting}>
                {form.formState.isSubmitting ? (
                  <div className="flex items-center gap-2">
                    <div className="size-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
                    Saving...
                  </div>
                ) : (
                  "Save Role"
                )}
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>
    </div>
  )
}
