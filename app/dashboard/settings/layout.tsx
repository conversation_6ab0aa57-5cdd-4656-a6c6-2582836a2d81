import type React from "react"
import type { <PERSON>ada<PERSON> } from "next"
import { <PERSON><PERSON>, <PERSON><PERSON>L<PERSON>, TabsTrigger } from "@/components/ui/tabs"
import Link from "next/link"

export const metadata: Metadata = {
  title: "Settings - Call Center Management System",
  description: "System settings and configuration",
}

interface SettingsLayoutProps {
  children: React.ReactNode
}

export default function SettingsLayout({ children }: SettingsLayoutProps) {
  return (
    <div className="flex-1 space-y-4 p-4 md:p-8 pt-6">
      <div className="flex items-center justify-between">
        <h2 className="text-3xl font-bold tracking-tight">Settings</h2>
      </div>

      <Tabs defaultValue="general" className="space-y-4">
        <TabsList className="grid w-full grid-cols-7 lg:w-auto">
          <TabsTrigger value="general" asChild>
            <Link href="/dashboard/settings">General</Link>
          </TabsTrigger>
          <TabsTrigger value="kpis" asChild>
            <Link href="/dashboard/settings/kpis">KPIs</Link>
          </TabsTrigger>
          <TabsTrigger value="roles" asChild>
            <Link href="/dashboard/settings/roles">Roles</Link>
          </TabsTrigger>
          <TabsTrigger value="security" asChild>
            <Link href="/dashboard/settings/security">Security</Link>
          </TabsTrigger>
          <TabsTrigger value="notifications" asChild>
            <Link href="/dashboard/settings/notifications">Notifications</Link>
          </TabsTrigger>
          <TabsTrigger value="integrations" asChild>
            <Link href="/dashboard/settings/integrations">Integrations</Link>
          </TabsTrigger>
          <TabsTrigger value="audit" asChild>
            <Link href="/dashboard/settings/audit">Audit Logs</Link>
          </TabsTrigger>
        </TabsList>

        <div className="space-y-4">{children}</div>
      </Tabs>
    </div>
  )
}

