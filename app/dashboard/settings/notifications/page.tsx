import { Card, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Switch } from "@/components/ui/switch"
import { Label } from "@/components/ui/label"
import { Separator } from "@/components/ui/separator"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"

export default function NotificationsSettingsPage() {
  return (
    <div className="space-y-6 w-full">
      <Tabs defaultValue="email" className="space-y-4">
        <TabsList>
          <TabsTrigger value="email">Email Notifications</TabsTrigger>
          <TabsTrigger value="sms">SMS Notifications</TabsTrigger>
          <TabsTrigger value="system">System Notifications</TabsTrigger>
          <TabsTrigger value="templates">Notification Templates</TabsTrigger>
        </TabsList>

        <TabsContent value="email" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Email Notification Settings</CardTitle>
              <CardDescription>Configure when email notifications are sent to users</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-4">
                <h3 className="text-sm font-medium">Ticket Notifications</h3>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="email-new-ticket">New ticket created</Label>
                    <p className="text-xs text-muted-foreground">Send email when a new ticket is created</p>
                  </div>
                  <Switch id="email-new-ticket" defaultChecked />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="email-ticket-assigned">Ticket assigned</Label>
                    <p className="text-xs text-muted-foreground">Send email when a ticket is assigned to an agent</p>
                  </div>
                  <Switch id="email-ticket-assigned" defaultChecked />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="email-ticket-updated">Ticket updated</Label>
                    <p className="text-xs text-muted-foreground">Send email when a ticket is updated</p>
                  </div>
                  <Switch id="email-ticket-updated" defaultChecked />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="email-ticket-resolved">Ticket resolved</Label>
                    <p className="text-xs text-muted-foreground">Send email when a ticket is resolved</p>
                  </div>
                  <Switch id="email-ticket-resolved" defaultChecked />
                </div>

                <Separator />

                <h3 className="text-sm font-medium">User Notifications</h3>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="email-user-created">New user created</Label>
                    <p className="text-xs text-muted-foreground">Send email when a new user account is created</p>
                  </div>
                  <Switch id="email-user-created" defaultChecked />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="email-password-reset">Password reset</Label>
                    <p className="text-xs text-muted-foreground">Send email when a password reset is requested</p>
                  </div>
                  <Switch id="email-password-reset" defaultChecked />
                </div>

                <Separator />

                <h3 className="text-sm font-medium">Report Notifications</h3>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="email-daily-summary">Daily summary</Label>
                    <p className="text-xs text-muted-foreground">Send daily summary of ticket activity</p>
                  </div>
                  <Switch id="email-daily-summary" />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="email-weekly-report">Weekly report</Label>
                    <p className="text-xs text-muted-foreground">Send weekly performance report</p>
                  </div>
                  <Switch id="email-weekly-report" defaultChecked />
                </div>
              </div>

              <Button>Save email settings</Button>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="sms" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>SMS Notification Settings</CardTitle>
              <CardDescription>Configure when SMS notifications are sent to users</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="sms-critical-tickets">Critical tickets</Label>
                    <p className="text-xs text-muted-foreground">Send SMS for critical priority tickets</p>
                  </div>
                  <Switch id="sms-critical-tickets" defaultChecked />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="sms-ticket-assigned">Ticket assigned</Label>
                    <p className="text-xs text-muted-foreground">Send SMS when a ticket is assigned to an agent</p>
                  </div>
                  <Switch id="sms-ticket-assigned" />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="sms-auth-code">Authentication codes</Label>
                    <p className="text-xs text-muted-foreground">Send SMS for two-factor authentication</p>
                  </div>
                  <Switch id="sms-auth-code" defaultChecked />
                </div>
              </div>

              <Button>Save SMS settings</Button>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="system" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>System Notification Settings</CardTitle>
              <CardDescription>Configure in-app notifications and alerts</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="system-new-ticket">New ticket alerts</Label>
                    <p className="text-xs text-muted-foreground">Show notification when new tickets are created</p>
                  </div>
                  <Switch id="system-new-ticket" defaultChecked />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="system-mentions">Mentions</Label>
                    <p className="text-xs text-muted-foreground">Notify when you are mentioned in comments</p>
                  </div>
                  <Switch id="system-mentions" defaultChecked />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="system-assignments">Assignments</Label>
                    <p className="text-xs text-muted-foreground">Notify when tickets are assigned to you</p>
                  </div>
                  <Switch id="system-assignments" defaultChecked />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="system-updates">System updates</Label>
                    <p className="text-xs text-muted-foreground">Notify about system updates and maintenance</p>
                  </div>
                  <Switch id="system-updates" defaultChecked />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="system-sound">Notification sounds</Label>
                    <p className="text-xs text-muted-foreground">Play sound when notifications are received</p>
                  </div>
                  <Switch id="system-sound" />
                </div>
              </div>

              <Button>Save system notification settings</Button>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="templates" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Notification Templates</CardTitle>
              <CardDescription>Customize email and SMS notification templates</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="template-new-ticket">New Ticket Email Template</Label>
                  <Textarea
                    id="template-new-ticket"
                    className="min-h-[150px]"
                    defaultValue={`Hello {agent_name},

A new ticket has been created and assigned to you:

Ticket ID: {ticket_id}
Subject: {ticket_subject}
Priority: {ticket_priority}
Customer: {customer_name}

Please review and respond as soon as possible.

Thank you,
{company_name} Support Team`}
                  />
                  <p className="text-xs text-muted-foreground">
                    Available variables: {"{agent_name}"}, {"{ticket_id}"}, {"{ticket_subject}"}, {"{ticket_priority}"},{" "}
                    {"{customer_name}"}, {"{company_name}"}
                  </p>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="template-ticket-resolved">Ticket Resolved Email Template</Label>
                  <Textarea
                    id="template-ticket-resolved"
                    className="min-h-[150px]"
                    defaultValue={`Hello {customer_name},

Your ticket has been resolved:

Ticket ID: {ticket_id}
Subject: {ticket_subject}

If you have any further questions, please don't hesitate to contact us.

Thank you for your patience,
{company_name} Support Team`}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="template-password-reset">Password Reset Email Template</Label>
                  <Textarea
                    id="template-password-reset"
                    className="min-h-[150px]"
                    defaultValue={`Hello {user_name},

We received a request to reset your password. Click the link below to set a new password:

{reset_link}

If you didn't request this, you can safely ignore this email.

Thank you,
{company_name} Support Team`}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="template-sms">SMS Notification Template</Label>
                  <Input
                    id="template-sms"
                    defaultValue="{company_name}: New ticket #{ticket_id} assigned to you. Priority: {ticket_priority}"
                  />
                  <p className="text-xs text-muted-foreground">Keep SMS templates short (160 characters max)</p>
                </div>
              </div>

              <Button>Save notification templates</Button>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}

