"use client"

import { useState } from "react"
import { zod<PERSON>esolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import { z } from "zod"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { But<PERSON> } from "@/components/ui/button"
import { Separator } from "@/components/ui/separator"
import { Switch } from "@/components/ui/switch"
import { Slider } from "@/components/ui/slider"
import { Ta<PERSON>, Ta<PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { TimePickerInput } from "@/components/ui/time-picker-input"
import { toast } from "@/components/ui/use-toast"
import { 
  Clock, 
  PhoneCall, 
  PhoneMissed, 
  CheckCircle2, 
  BarChart3, 
  Calendar<PERSON>lock 
} from "lucide-react"

// Define the schema for KPI settings
const kpiSettingsSchema = z.object({
  // First Contact Resolution
  firstContactResolutionTarget: z.number().min(1).max(100),
  firstContactResolutionEnabled: z.boolean(),
  
  // Customer Wait Time
  maxRingsBeforeAnswer: z.number().min(1).max(10),
  customerWaitTimeEnabled: z.boolean(),
  
  // Average Handling Time
  maxHandlingTimeMinutes: z.number().min(1).max(60),
  avgHandlingTimeEnabled: z.boolean(),
  
  // Call Abandonment
  callAbandonmentThreshold: z.number().min(1).max(100),
  callAbandonmentEnabled: z.boolean(),
  
  // Resolution Rate
  resolutionRateTarget: z.number().min(1).max(100),
  resolutionRateEnabled: z.boolean(),
  
  // Schedule Adherence
  scheduleStartHour: z.number().min(0).max(23),
  scheduleStartMinute: z.number().min(0).max(59),
  scheduleEndHour: z.number().min(0).max(23),
  scheduleEndMinute: z.number().min(0).max(59),
  scheduleAdherenceEnabled: z.boolean(),
})

type KpiSettingsFormValues = z.infer<typeof kpiSettingsSchema>

// Default values for the form
const defaultValues: KpiSettingsFormValues = {
  firstContactResolutionTarget: 85,
  firstContactResolutionEnabled: true,
  
  maxRingsBeforeAnswer: 2,
  customerWaitTimeEnabled: true,
  
  maxHandlingTimeMinutes: 5,
  avgHandlingTimeEnabled: true,
  
  callAbandonmentThreshold: 5,
  callAbandonmentEnabled: true,
  
  resolutionRateTarget: 90,
  resolutionRateEnabled: true,
  
  scheduleStartHour: 7,
  scheduleStartMinute: 0,
  scheduleEndHour: 18,
  scheduleEndMinute: 0,
  scheduleAdherenceEnabled: true,
}

export default function KpiSettingsPage() {
  const [activeTab, setActiveTab] = useState("general")
  
  const form = useForm<KpiSettingsFormValues>({
    resolver: zodResolver(kpiSettingsSchema),
    defaultValues,
  })
  
  const { watch, setValue } = form
  
  // Watch form values for real-time updates
  const formValues = watch()
  
  // Handle form submission
  const onSubmit = (data: KpiSettingsFormValues) => {
    console.log("KPI Settings:", data)
    
    // In a real application, you would save these settings to your backend
    toast({
      title: "KPI Settings Updated",
      description: "Your call center KPI settings have been saved successfully.",
    })
  }
  
  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Call Center KPI Settings</CardTitle>
          <CardDescription>
            Configure Key Performance Indicators (KPIs) for your call center operations
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
            <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
              <TabsList className="grid w-full grid-cols-3">
                <TabsTrigger value="general">General KPIs</TabsTrigger>
                <TabsTrigger value="call-metrics">Call Metrics</TabsTrigger>
                <TabsTrigger value="scheduling">Scheduling</TabsTrigger>
              </TabsList>
              
              {/* General KPIs Tab */}
              <TabsContent value="general" className="space-y-4">
                {/* First Contact Resolution */}
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <CheckCircle2 className="h-5 w-5 text-primary" />
                      <div className="space-y-0.5">
                        <Label htmlFor="firstContactResolutionTarget">First Contact Resolution</Label>
                        <p className="text-sm text-muted-foreground">
                          Percentage of issues resolved on first contact
                        </p>
                      </div>
                    </div>
                    <Switch 
                      checked={formValues.firstContactResolutionEnabled}
                      onCheckedChange={(checked) => setValue("firstContactResolutionEnabled", checked)}
                    />
                  </div>
                  
                  {formValues.firstContactResolutionEnabled && (
                    <div className="pl-7 space-y-2">
                      <div className="flex items-center justify-between">
                        <span className="text-sm">Target: {formValues.firstContactResolutionTarget}%</span>
                      </div>
                      <Slider
                        value={[formValues.firstContactResolutionTarget]}
                        min={50}
                        max={100}
                        step={1}
                        onValueChange={(value) => setValue("firstContactResolutionTarget", value[0])}
                      />
                    </div>
                  )}
                </div>
                
                <Separator />
                
                {/* Resolution Rate */}
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <BarChart3 className="h-5 w-5 text-primary" />
                      <div className="space-y-0.5">
                        <Label htmlFor="resolutionRateTarget">Resolution Rate</Label>
                        <p className="text-sm text-muted-foreground">
                          Overall percentage of tickets/calls successfully resolved
                        </p>
                      </div>
                    </div>
                    <Switch 
                      checked={formValues.resolutionRateEnabled}
                      onCheckedChange={(checked) => setValue("resolutionRateEnabled", checked)}
                    />
                  </div>
                  
                  {formValues.resolutionRateEnabled && (
                    <div className="pl-7 space-y-2">
                      <div className="flex items-center justify-between">
                        <span className="text-sm">Target: {formValues.resolutionRateTarget}%</span>
                      </div>
                      <Slider
                        value={[formValues.resolutionRateTarget]}
                        min={50}
                        max={100}
                        step={1}
                        onValueChange={(value) => setValue("resolutionRateTarget", value[0])}
                      />
                    </div>
                  )}
                </div>
              </TabsContent>
              
              {/* Call Metrics Tab */}
              <TabsContent value="call-metrics" className="space-y-4">
                {/* Customer Wait Time */}
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <PhoneCall className="h-5 w-5 text-primary" />
                      <div className="space-y-0.5">
                        <Label htmlFor="maxRingsBeforeAnswer">Customer Wait Time</Label>
                        <p className="text-sm text-muted-foreground">
                          Maximum number of rings before a call should be answered
                        </p>
                      </div>
                    </div>
                    <Switch 
                      checked={formValues.customerWaitTimeEnabled}
                      onCheckedChange={(checked) => setValue("customerWaitTimeEnabled", checked)}
                    />
                  </div>
                  
                  {formValues.customerWaitTimeEnabled && (
                    <div className="pl-7 space-y-2">
                      <div className="flex items-center justify-between">
                        <span className="text-sm">Max Rings: {formValues.maxRingsBeforeAnswer}</span>
                      </div>
                      <Slider
                        value={[formValues.maxRingsBeforeAnswer]}
                        min={1}
                        max={10}
                        step={1}
                        onValueChange={(value) => setValue("maxRingsBeforeAnswer", value[0])}
                      />
                    </div>
                  )}
                </div>
                
                <Separator />
                
                {/* Average Handling Time */}
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <Clock className="h-5 w-5 text-primary" />
                      <div className="space-y-0.5">
                        <Label htmlFor="maxHandlingTimeMinutes">Average Handling Time</Label>
                        <p className="text-sm text-muted-foreground">
                          Maximum time (in minutes) for handling a customer call
                        </p>
                      </div>
                    </div>
                    <Switch 
                      checked={formValues.avgHandlingTimeEnabled}
                      onCheckedChange={(checked) => setValue("avgHandlingTimeEnabled", checked)}
                    />
                  </div>
                  
                  {formValues.avgHandlingTimeEnabled && (
                    <div className="pl-7 space-y-2">
                      <div className="flex items-center justify-between">
                        <span className="text-sm">Max Time: {formValues.maxHandlingTimeMinutes} minutes</span>
                      </div>
                      <Slider
                        value={[formValues.maxHandlingTimeMinutes]}
                        min={1}
                        max={20}
                        step={1}
                        onValueChange={(value) => setValue("maxHandlingTimeMinutes", value[0])}
                      />
                    </div>
                  )}
                </div>
                
                <Separator />
                
                {/* Call Abandonment */}
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <PhoneMissed className="h-5 w-5 text-primary" />
                      <div className="space-y-0.5">
                        <Label htmlFor="callAbandonmentThreshold">Call Abandonment</Label>
                        <p className="text-sm text-muted-foreground">
                          Maximum acceptable percentage of abandoned calls
                        </p>
                      </div>
                    </div>
                    <Switch 
                      checked={formValues.callAbandonmentEnabled}
                      onCheckedChange={(checked) => setValue("callAbandonmentEnabled", checked)}
                    />
                  </div>
                  
                  {formValues.callAbandonmentEnabled && (
                    <div className="pl-7 space-y-2">
                      <div className="flex items-center justify-between">
                        <span className="text-sm">Threshold: {formValues.callAbandonmentThreshold}%</span>
                      </div>
                      <Slider
                        value={[formValues.callAbandonmentThreshold]}
                        min={1}
                        max={20}
                        step={1}
                        onValueChange={(value) => setValue("callAbandonmentThreshold", value[0])}
                      />
                    </div>
                  )}
                </div>
              </TabsContent>
              
              {/* Scheduling Tab */}
              <TabsContent value="scheduling" className="space-y-4">
                {/* Schedule Adherence */}
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <CalendarClock className="h-5 w-5 text-primary" />
                      <div className="space-y-0.5">
                        <Label>Schedule Adherence</Label>
                        <p className="text-sm text-muted-foreground">
                          Working hours for agents (7AM - 6PM by default)
                        </p>
                      </div>
                    </div>
                    <Switch 
                      checked={formValues.scheduleAdherenceEnabled}
                      onCheckedChange={(checked) => setValue("scheduleAdherenceEnabled", checked)}
                    />
                  </div>
                  
                  {formValues.scheduleAdherenceEnabled && (
                    <div className="pl-7 space-y-4">
                      <div className="grid grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Label>Start Time</Label>
                          <div className="flex items-center gap-2">
                            <Input 
                              type="number" 
                              min={0} 
                              max={23} 
                              value={formValues.scheduleStartHour}
                              onChange={(e) => setValue("scheduleStartHour", parseInt(e.target.value))}
                              className="w-20"
                            />
                            <span>:</span>
                            <Input 
                              type="number" 
                              min={0} 
                              max={59} 
                              value={formValues.scheduleStartMinute}
                              onChange={(e) => setValue("scheduleStartMinute", parseInt(e.target.value))}
                              className="w-20"
                            />
                            <span className="ml-2">
                              {formValues.scheduleStartHour < 12 ? 'AM' : 'PM'}
                            </span>
                          </div>
                        </div>
                        
                        <div className="space-y-2">
                          <Label>End Time</Label>
                          <div className="flex items-center gap-2">
                            <Input 
                              type="number" 
                              min={0} 
                              max={23} 
                              value={formValues.scheduleEndHour}
                              onChange={(e) => setValue("scheduleEndHour", parseInt(e.target.value))}
                              className="w-20"
                            />
                            <span>:</span>
                            <Input 
                              type="number" 
                              min={0} 
                              max={59} 
                              value={formValues.scheduleEndMinute}
                              onChange={(e) => setValue("scheduleEndMinute", parseInt(e.target.value))}
                              className="w-20"
                            />
                            <span className="ml-2">
                              {formValues.scheduleEndHour < 12 ? 'AM' : 'PM'}
                            </span>
                          </div>
                        </div>
                      </div>
                      
                      <p className="text-sm text-muted-foreground">
                        Agents are expected to adhere to these working hours. Adherence will be tracked and reported.
                      </p>
                    </div>
                  )}
                </div>
              </TabsContent>
            </Tabs>
            
            <Button type="submit" className="mt-6">Save KPI Settings</Button>
          </form>
        </CardContent>
      </Card>
    </div>
  )
}
