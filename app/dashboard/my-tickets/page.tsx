"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { TicketPlus } from "lucide-react"
import { useMyTickets } from "@/lib/hooks/useMyTickets"

import { TicketsTable } from "@/components/tickets/tickets-table"
import { Pagination } from "@/components/ui/pagination"
import { ViewTicketDrawer } from "@/components/tickets/view-ticket-drawer"
import { EscalateTicketDialog } from "@/components/tickets/escalate-ticket-dialog"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import type { Ticket } from "@/lib/api/types"
import { CreateTicketDialog } from "@/components/tickets/create-ticket-dialog"

export default function MyTicketsPage() {
  const [currentPage, setCurrentPage] = useState(1)

  // Log when page changes and refetch tickets
  useEffect(() => {
    console.log('My Tickets - Page changed to:', currentPage);
    // When page changes, we should refetch tickets with the new page
    // This is handled automatically by the useMyTickets hook because
    // currentPage is included in the dependency array of the query
  }, [currentPage])
  const [limit] = useState(10)
  const [statusFilter, setStatusFilter] = useState('all')
  const [priorityFilter, setPriorityFilter] = useState('all')
  const [isEscalatedFilter, setIsEscalatedFilter] = useState('all')
  const [currentTicket, setCurrentTicket] = useState<Ticket | null>(null)
  const [isViewTicketOpen, setIsViewTicketOpen] = useState(false)
  const [isCreateTicketOpen, setIsCreateTicketOpen] = useState(false)
  const [isEscalateTicketOpen, setIsEscalateTicketOpen] = useState(false)

  const { myTickets, isLoading, error, pagination } = useMyTickets({
    page: currentPage,
    limit,
    ...(statusFilter !== 'all' && { status: statusFilter }),
    ...(priorityFilter !== 'all' && { priority: priorityFilter }),
    ...(isEscalatedFilter !== 'all' && { escalated: isEscalatedFilter === 'yes' ? true : false }),
  });

  let errorMessage = "Error loading tickets. Please try again.";

  if (error) {
    if (
      error instanceof TypeError &&
      (error.message.includes("Failed to fetch") ||
        error.message.includes("NetworkError") ||
        error.message.includes("ERR_CONNECTION_REFUSED"))
    ) {
      errorMessage = "System is offline. Please check your connection or contact support.";
    }
  }

  const handleViewTicket = (ticket: Ticket) => {
    setCurrentTicket(ticket)
    setIsViewTicketOpen(true)
  }

  const handleEscalateTicket = (ticket: Ticket) => {
    setCurrentTicket(ticket)
    setIsEscalateTicketOpen(true)
  }

  return (
    <div className="flex-1 space-y-4 p-4 md:p-8 pt-6">
      <div className="flex items-center justify-between">
        <h2 className="text-3xl font-bold tracking-tight">My Tickets</h2>
        <Button onClick={() => setIsCreateTicketOpen(true)}>
          <TicketPlus className="mr-2 size-4" />
          Create Ticket
        </Button>
      </div>

      {/* Add filters */}
      <div className="flex gap-4 flex-wrap">
        <div className="flex items-center gap-2">
          <label className="text-sm font-medium">Status:</label>
          <Select value={statusFilter} onValueChange={setStatusFilter}>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Filter by status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Status</SelectItem>
              <SelectItem value="open">Open</SelectItem>
              <SelectItem value="in_progress">In Progress</SelectItem>
              <SelectItem value="pending">Pending</SelectItem>
              <SelectItem value="resolved">Resolved</SelectItem>
              <SelectItem value="closed">Closed</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="flex items-center gap-2">
          <label className="text-sm font-medium">Priority:</label>
          <Select value={priorityFilter} onValueChange={setPriorityFilter}>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Filter by priority" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Priority</SelectItem>
              <SelectItem value="low">Low</SelectItem>
              <SelectItem value="medium">Medium</SelectItem>
              <SelectItem value="high">High</SelectItem>
              <SelectItem value="urgent">Urgent</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="flex items-center gap-2">
          <label className="text-sm font-medium">Escalated:</label>
          <Select value={isEscalatedFilter} onValueChange={setIsEscalatedFilter}>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Filter by escalation" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Tickets</SelectItem>
              <SelectItem value="yes">Escalated Only</SelectItem>
              <SelectItem value="no">Not Escalated</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      <div className="space-y-4">
        {isLoading ? (
          <div className="flex items-center justify-center h-32">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary" />
          </div>
        ) : error ? (
          <div className="flex items-center justify-center h-32 text-destructive">
            {errorMessage}
          </div>
        ) : (
          <>
            <TicketsTable
              tickets={myTickets}
              onViewTicket={handleViewTicket}
              onEscalateTicket={handleEscalateTicket}
              onAssignTicket={() => {}}
            />
            {/* Always show pagination for testing */}
            <Pagination
              currentPage={currentPage}
              totalPages={pagination.total_pages || 5}
              onPageChange={setCurrentPage}
            />
          </>
        )}
      </div>

      {currentTicket && (
        <>
          <ViewTicketDrawer
            ticket={currentTicket}
            open={isViewTicketOpen}
            onOpenChange={setIsViewTicketOpen}
          />
          <EscalateTicketDialog
            ticketId={currentTicket.id}
            open={isEscalateTicketOpen}
            onOpenChange={setIsEscalateTicketOpen}
          />
        </>
      )}

      <CreateTicketDialog
        open={isCreateTicketOpen}
        onOpenChange={setIsCreateTicketOpen}
      />
    </div>
  )
}
