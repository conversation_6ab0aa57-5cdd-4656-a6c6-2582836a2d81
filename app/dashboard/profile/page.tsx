"use client"

import { useState } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/tabs"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Separator } from "@/components/ui/separator"
import { Badge } from "@/components/ui/badge"
import {
  User,
  Mail,
  Phone,
  Calendar,
  Clock,
  Shield,
  Edit,
  Save,
  X,
  BarChart3,
  PhoneCall,
  CheckCircle2,
  Clock8,
  Ticket,
} from "lucide-react"
import { Progress } from "@/components/ui/progress"
import { Skeleton } from "@/components/ui/skeleton"
import { useAuthContext } from "@/lib/context/auth-context"

export default function ProfilePage() {
  const [isEditing, setIsEditing] = useState(false)

  // Get authenticated user data from context
  const { user, isLoading } = useAuthContext()
  
  // Handle loading state
  if (isLoading) {
    return (
      <div className="flex h-full w-full items-center justify-center">
        <Skeleton className="h-32 w-32 rounded-full" />
        <div className="space-y-2">
          <Skeleton className="h-4 w-[250px]" />
          <Skeleton className="h-4 w-[200px]" />
        </div>
      </div>
    )
  }
  
  // Handle case where user is not authenticated
  if (!user) {
    return (
      <div className="flex h-full w-full items-center justify-center">
        <p className="text-muted-foreground">Please log in to view your profile</p>
      </div>
    )
  }

  // Mock call statistics
  const callStats = {
    totalCalls: 1245,
    answeredCalls: 1180,
    missedCalls: 65,
    avgCallDuration: "4m 32s",
    callsToday: 28,
    callsThisWeek: 142,
    callsThisMonth: 560,
    satisfactionRate: 92,
  }

  // Mock recent activities
  const recentActivities = [
    {
      id: 1,
      type: "ticket",
      action: "resolved",
      target: "T-1005",
      timestamp: "2023-05-15T14:30:00",
      description: "Resolved ticket #T-1005 - Payment not reflecting on account",
    },
    {
      id: 2,
      type: "call",
      action: "completed",
      target: "C-2034",
      timestamp: "2023-05-15T13:15:00",
      description: "Completed call with customer Sarah Williams",
    },
    {
      id: 3,
      type: "user",
      action: "created",
      target: "U-1008",
      timestamp: "2023-05-15T11:45:00",
      description: "Created new user account for Linda Martinez",
    },
    {
      id: 4,
      type: "ticket",
      action: "assigned",
      target: "T-1003",
      timestamp: "2023-05-15T10:20:00",
      description: "Assigned ticket #T-1003 to Michael Chen",
    },
    {
      id: 5,
      type: "call",
      action: "completed",
      target: "C-2030",
      timestamp: "2023-05-15T09:50:00",
      description: "Completed call with customer Robert Brown",
    },
  ]

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
    })
  }

  const formatDateTime = (dateString: string) => {
    return new Date(dateString).toLocaleString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    })
  }

  const getTimeAgo = (dateString: string) => {
    const now = new Date()
    const past = new Date(dateString)
    const diffMs = now.getTime() - past.getTime()
    const diffMins = Math.floor(diffMs / 60000)
    const diffHours = Math.floor(diffMins / 60)
    const diffDays = Math.floor(diffHours / 24)

    if (diffMins < 60) return `${diffMins} min ago`
    if (diffHours < 24) return `${diffHours} hours ago`
    return `${diffDays} days ago`
  }
  
  // Format snake_case role names to Title Case
  const formatRoleName = (roleName: string) => {
    if (!roleName) return 'User'
    
    // Handle snake_case role names (from API)
    if (roleName.includes('_')) {
      return roleName.split('_')
        .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
        .join(' ')
    }
    
    // Already formatted roles
    return roleName
  }
  
  // Get appropriate badge variant based on role
  const getRoleBadgeVariant = (roleName?: string): "default" | "secondary" | "destructive" | "outline" => {
    if (!roleName) return 'outline'
    
    const normalizedRole = roleName.toLowerCase()
    
    // Apply different variants based on role hierarchy
    if (normalizedRole.includes('super') || normalizedRole.includes('admin')) {
      return 'destructive' // Highest privilege
    } else if (
      normalizedRole.includes('operation') || 
      normalizedRole.includes('head') || 
      normalizedRole.includes('lead')
    ) {
      return 'default' // Admin roles
    } else if (normalizedRole.includes('agent')) {
      return 'secondary' // Agent role
    }
    
    return 'outline' // Default for unknown roles
  }

  return (
    <div className="flex-1 space-y-4 p-4 md:p-8 pt-6">
      <div className="flex items-center justify-between">
        <h2 className="text-3xl font-bold tracking-tight">My Profile</h2>
        {!isEditing ? (
          <Button onClick={() => setIsEditing(true)}>
            <Edit className="mr-2 size-4" />
            Edit Profile
          </Button>
        ) : (
          <div className="flex gap-2">
            <Button variant="outline" onClick={() => setIsEditing(false)}>
              <X className="mr-2 size-4" />
              Cancel
            </Button>
            <Button onClick={() => setIsEditing(false)}>
              <Save className="mr-2 size-4" />
              Save Changes
            </Button>
          </div>
        )}
      </div>

      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="activity">Activity</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            <Card className="col-span-1 md:col-span-2 lg:col-span-1">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-base font-medium">User Information</CardTitle>
              </CardHeader>
              <CardContent className="pt-4">
                <div className="flex flex-col items-center space-y-4">
                  <Avatar className="h-24 w-24">
                    <AvatarImage src="/placeholder.svg?height=96&width=96" alt={`${user.firstName || user.first_name} ${user.lastName || user.last_name}`} />
                    <AvatarFallback className="text-2xl">
                      {(user.firstName || user.first_name).charAt(0)}
                      {(user.lastName || user.last_name).charAt(0)}
                    </AvatarFallback>
                  </Avatar>
                  <div className="space-y-1 text-center">
                    <h3 className="text-xl font-semibold">
                      {user.firstName || user.first_name} {user.lastName || user.last_name}
                    </h3>
                    <p className="text-sm text-muted-foreground">@{user.username || user.email.split('@')[0]}</p>
                    <Badge variant={getRoleBadgeVariant(user.role?.name)}>
                      {formatRoleName(user.role?.name || 'User')}
                    </Badge>
                  </div>
                </div>

                <Separator className="my-4" />

                <div className="space-y-4">
                  <div className="flex items-center">
                    <Mail className="mr-2 size-4 text-muted-foreground" />
                    {isEditing ? (
                      <Input defaultValue={user.email} className="h-8" />
                    ) : (
                      <span className="text-sm">{user.email}</span>
                    )}
                  </div>
                  <div className="flex items-center">
                    <Phone className="mr-2 size-4 text-muted-foreground" />
                    {isEditing ? (
                      <Input defaultValue={user.phone_number || ''} className="h-8" />
                    ) : (
                      <span className="text-sm">{user.phone_number || 'Not provided'}</span>
                    )}
                  </div>
                  <div className="flex items-center">
                    <Shield className="mr-2 size-4 text-muted-foreground" />
                    <span className="text-sm">{user.role?.name || 'User'}</span>
                  </div>
                  <div className="flex items-center">
                    <Calendar className="mr-2 size-4 text-muted-foreground" />
                    <span className="text-sm">Joined {formatDate(user.created_at || new Date().toISOString())}</span>
                  </div>
                  <div className="flex items-center">
                    <Clock className="mr-2 size-4 text-muted-foreground" />
                    <span className="text-sm">Last active {getTimeAgo(user.updated_at || new Date().toISOString())}</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="col-span-1 md:col-span-2">
              <CardHeader>
                <CardTitle>Call Statistics</CardTitle>
                <CardDescription>Your call performance metrics</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <PhoneCall className="size-4 text-primary" />
                      <p className="text-sm font-medium">Total Calls</p>
                    </div>
                    <p className="text-2xl font-bold">{callStats.totalCalls}</p>
                    <p className="text-xs text-muted-foreground">Lifetime</p>
                  </div>
                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <CheckCircle2 className="size-4 text-success" />
                      <p className="text-sm font-medium">Answered</p>
                    </div>
                    <p className="text-2xl font-bold">{callStats.answeredCalls}</p>
                    <p className="text-xs text-muted-foreground">
                      {Math.round((callStats.answeredCalls / callStats.totalCalls) * 100)}% answer rate
                    </p>
                  </div>
                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <Clock8 className="size-4 text-warning" />
                      <p className="text-sm font-medium">Avg Duration</p>
                    </div>
                    <p className="text-2xl font-bold">{callStats.avgCallDuration}</p>
                    <p className="text-xs text-muted-foreground">Per call</p>
                  </div>
                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <BarChart3 className="size-4 text-primary" />
                      <p className="text-sm font-medium">Satisfaction</p>
                    </div>
                    <p className="text-2xl font-bold">{callStats.satisfactionRate}%</p>
                    <div className="w-full">
                      <Progress value={callStats.satisfactionRate} className="h-1" />
                    </div>
                  </div>
                </div>

                <Separator className="my-4" />

                <div className="grid gap-4 md:grid-cols-3">
                  <div className="space-y-2">
                    <p className="text-sm font-medium">Today</p>
                    <p className="text-2xl font-bold">{callStats.callsToday}</p>
                    <p className="text-xs text-muted-foreground">calls handled</p>
                  </div>
                  <div className="space-y-2">
                    <p className="text-sm font-medium">This Week</p>
                    <p className="text-2xl font-bold">{callStats.callsThisWeek}</p>
                    <p className="text-xs text-muted-foreground">calls handled</p>
                  </div>
                  <div className="space-y-2">
                    <p className="text-sm font-medium">This Month</p>
                    <p className="text-2xl font-bold">{callStats.callsThisMonth}</p>
                    <p className="text-xs text-muted-foreground">calls handled</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {isEditing && (
            <Card>
              <CardHeader>
                <CardTitle>Account Settings</CardTitle>
                <CardDescription>Update your account preferences</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                  <div className="space-y-2">
                    <Label htmlFor="firstName">First Name</Label>
                    <Input id="firstName" defaultValue={user.firstName} />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="lastName">Last Name</Label>
                    <Input id="lastName" defaultValue={user.lastName} />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="username">Username</Label>
                    <Input id="username" defaultValue={user.username} />
                  </div>
                  {/* <div className="space-y-2">
                    <Label htmlFor="department">Department</Label>
                    <Input id="department" defaultValue={user.department} />
                  </div> */}
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="activity" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Recent Activity</CardTitle>
              <CardDescription>Your recent actions and events</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-8">
                {recentActivities.map((activity) => (
                  <div key={activity.id} className="flex">
                    <div className="mr-4 flex flex-col items-center">
                      <div className="flex h-10 w-10 items-center justify-center rounded-full border border-muted bg-muted">
                        {activity.type === "ticket" && <Ticket className="h-5 w-5 text-primary" />}
                        {activity.type === "call" && <PhoneCall className="h-5 w-5 text-success" />}
                        {activity.type === "user" && <User className="h-5 w-5 text-warning" />}
                      </div>
                      <div className="h-full w-px bg-border" />
                    </div>
                    <div className="space-y-1 pb-8">
                      <p className="text-sm font-medium">{activity.description}</p>
                      <p className="text-xs text-muted-foreground">{formatDateTime(activity.timestamp)}</p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="analytics" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Performance Analytics</CardTitle>
              <CardDescription>Your performance metrics and statistics</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                  <Card className="p-4">
                    <div className="space-y-2">
                      <p className="text-sm font-medium text-muted-foreground">Resolution Rate</p>
                      <p className="text-2xl font-bold">94%</p>
                      <div className="h-1 w-full bg-muted">
                        <div className="h-1 w-[94%] bg-primary"></div>
                      </div>
                      <p className="text-xs text-muted-foreground">+2.5% from last month</p>
                    </div>
                  </Card>
                  <Card className="p-4">
                    <div className="space-y-2">
                      <p className="text-sm font-medium text-muted-foreground">First Response Time</p>
                      <p className="text-2xl font-bold">1m 45s</p>
                      <div className="h-1 w-full bg-muted">
                        <div className="h-1 w-[85%] bg-success"></div>
                      </div>
                      <p className="text-xs text-muted-foreground">-15s from last month</p>
                    </div>
                  </Card>
                  <Card className="p-4">
                    <div className="space-y-2">
                      <p className="text-sm font-medium text-muted-foreground">Tickets Handled</p>
                      <p className="text-2xl font-bold">187</p>
                      <div className="h-1 w-full bg-muted">
                        <div className="h-1 w-[78%] bg-warning"></div>
                      </div>
                      <p className="text-xs text-muted-foreground">+12 from last month</p>
                    </div>
                  </Card>
                  <Card className="p-4">
                    <div className="space-y-2">
                      <p className="text-sm font-medium text-muted-foreground">Efficiency Score</p>
                      <p className="text-2xl font-bold">89%</p>
                      <div className="h-1 w-full bg-muted">
                        <div className="h-1 w-[89%] bg-primary"></div>
                      </div>
                      <p className="text-xs text-muted-foreground">+1.2% from last month</p>
                    </div>
                  </Card>
                </div>

                <Separator />

                <div className="space-y-2">
                  <h3 className="text-lg font-medium">Monthly Performance</h3>
                  <p className="text-sm text-muted-foreground">Your performance metrics over the last 6 months</p>
                  <div className="h-[300px] w-full rounded-md border p-4">
                    <div className="flex h-full items-center justify-center">
                      <p className="text-sm text-muted-foreground">Performance chart will be displayed here</p>
                    </div>
                  </div>
                </div>

                <Separator />

                <div className="space-y-2">
                  <h3 className="text-lg font-medium">Skills Assessment</h3>
                  <p className="text-sm text-muted-foreground">Your proficiency in different areas</p>
                  <div className="space-y-4">
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <p className="text-sm font-medium">Customer Service</p>
                        <p className="text-sm font-medium">95%</p>
                      </div>
                      <Progress value={95} className="h-2" />
                    </div>
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <p className="text-sm font-medium">Technical Knowledge</p>
                        <p className="text-sm font-medium">88%</p>
                      </div>
                      <Progress value={88} className="h-2" />
                    </div>
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <p className="text-sm font-medium">Problem Solving</p>
                        <p className="text-sm font-medium">92%</p>
                      </div>
                      <Progress value={92} className="h-2" />
                    </div>
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <p className="text-sm font-medium">Communication</p>
                        <p className="text-sm font-medium">97%</p>
                      </div>
                      <Progress value={97} className="h-2" />
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}

