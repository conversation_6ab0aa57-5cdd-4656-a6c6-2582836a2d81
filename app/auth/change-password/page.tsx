"use client"

import { useState, useEffect } from "react"
import { useRout<PERSON> } from "next/navigation"
import Link from "next/link"
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import { Headphones, Loader2, ArrowLeft, KeyRound, Eye, EyeOff } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { useAuth } from "@/lib/hooks"
import { authService } from "@/lib/api"
import { updatePasswordSchema, UpdatePasswordFormValues } from "@/lib/validations/auth"
import { toast } from "sonner"

// We're using the updatePasswordSchema from our validations

export default function ChangePasswordPage() {
  const router = useRouter()
  const [isActivating, setIsActivating] = useState(false)
  const { updatePassword, isUpdatingPassword, updatePasswordError } = useAuth()
  const [showPassword, setShowPassword] = useState(false)

  // Initialize the form
  const form = useForm<UpdatePasswordFormValues>({
    resolver: zodResolver(updatePasswordSchema),
    defaultValues: {
      currentPassword: "",
      newPassword: "",
    },
  })

  // Handle form submission
  const onSubmit = async (values: UpdatePasswordFormValues) => {
    if (isActivating) return; // Prevent double submission
    
    try {
      setIsActivating(true);
      
      // Update the password
      const response = await authService.activateAccount({
        currentPassword: values.currentPassword,
        newPassword: values.newPassword,
        temporaryToken: localStorage.getItem('temporaryToken') || '',
      });
      
      if (response.success) {
        // Show success toast
        toast.success('Password changed successfully. Your account is now active.', {
          duration: 5000,
          id: 'password-changed',
        });
        
        // Clear the temporary token
        localStorage.removeItem('temporaryToken');
        
        // On success, redirect to login page with activated=true to show success message
        router.push("/auth/login?activated=true");
      } else {
        // Show error toast
        toast.error(response.message || 'Failed to activate account. Please try again.', {
          duration: 6000,
          id: 'activation-error',
        });
        setIsActivating(false); // Reset loading state on error
      }
    } catch (error: any) {
      // Show error toast with the error message
      toast.error(error.message || 'Failed to activate account. Please try again.', {
        duration: 6000,
        id: 'activation-error',
      });
      console.error('Error during account activation:', error);
      setIsActivating(false); // Reset loading state on error
    }
  }

  return (
    <div className="flex min-h-screen flex-col items-center justify-center bg-muted/40 p-4">
      <div className="mb-4 flex items-center gap-2 text-2xl font-bold">
        <Headphones className="h-6 w-6 text-primary" />
        <span>CallCenter</span>
      </div>

      <Card className="w-full max-w-md">
        <CardHeader className="space-y-1">
          <div className="flex items-center">
            <Link
              href="/auth/login"
              className="mr-2 inline-flex items-center text-sm text-muted-foreground hover:text-foreground"
            >
              <ArrowLeft className="mr-1 size-4" />
              Back to login
            </Link>
          </div>
          <div className="flex items-center gap-2">
            <KeyRound className="h-5 w-5 text-primary" />
            <CardTitle className="text-2xl font-bold">Activate Your Account</CardTitle>
          </div>
          <CardDescription>Please change your current password to activate your account</CardDescription>
        </CardHeader>
        <CardContent>
          {updatePasswordError && (
            <Alert variant="destructive" className="mb-4">
              <AlertDescription>{updatePasswordError.toString()}</AlertDescription>
            </Alert>
          )}

          <Form {...form}>
            <form onSubmit={(e) => {
              e.preventDefault();
              form.handleSubmit(onSubmit)(e);
            }} className="space-y-4">
              <Alert className="mb-4 bg-blue-50 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400">
                <AlertDescription>
                  Your account needs to be activated. Please change your current password to continue.
                </AlertDescription>
              </Alert>
              <FormField
                control={form.control}
                name="currentPassword"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Current Password</FormLabel>
                    <div className="relative">
                      <FormControl>
                        <Input
                          type={showPassword ? "text" : "password"}
                          placeholder="Enter your current password"
                          disabled={isActivating}
                          {...field}
                        />
                      </FormControl>
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        className="absolute right-0 top-0 h-full px-3 py-2"
                        onClick={() => setShowPassword(!showPassword)}
                      >
                        {showPassword ? (
                          <EyeOff className="size-4" />
                        ) : (
                          <Eye className="size-4" />
                        )}
                      </Button>
                    </div>

                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="newPassword"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>New Password</FormLabel>
                    <div className="relative">
                      <FormControl>
                        <Input type={showPassword ? "text" : "password"} placeholder="Create a new password" disabled={isActivating} {...field} />
                      </FormControl>
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        className="absolute right-0 top-0 h-full px-3 py-2"
                        onClick={() => setShowPassword(!showPassword)}
                      >
                        {showPassword ? (
                          <EyeOff className="size-4" />
                        ) : (
                          <Eye className="size-4" />
                        )}
                      </Button>
                    </div>
                    <FormMessage />
                    <p className="text-xs text-muted-foreground">
                      Password must be at least 8 characters and include uppercase, lowercase, number, and special
                      character.
                    </p>
                  </FormItem>
                )}
              />

              <Button type="submit" className="w-full" disabled={isActivating}>
                {isActivating ? (
                  <>
                    <Loader2 className="mr-2 size-4 animate-spin" />
                    Activating Account...
                  </>
                ) : (
                  "Activate Account"
                )}
              </Button>
            </form>
          </Form>
        </CardContent>
        <CardFooter className="flex flex-col space-y-4">
          <div className="text-center text-sm">
            Need help?{" "}
            <Link href="#" className="text-primary hover:underline">
              Contact support
            </Link>
          </div>
        </CardFooter>
      </Card>
    </div>
  )
}

