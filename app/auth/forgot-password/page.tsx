"use client"

import type React from "react"

import { useState, useEffect, Suspense } from "react"
import Link from "next/link"
import { Headphones, Loader2, ArrowLeft, CheckCircle } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { useAuth } from "@/lib/hooks"

// Component that uses the auth hook
function ForgotPasswordForm() {
  const [isSubmitted, setIsSubmitted] = useState(false)
  const [msisdn, setMsisdn] = useState("")

  const {
    forgotPassword,
    isForgotPasswordPending: isLoading,
    forgotPasswordError
  } = useAuth()

  // Error state derived from forgotPasswordError
  const [error, setError] = useState<string | null>(null)

  // Update error when forgotPasswordError changes
  useEffect(() => {
    if (forgotPasswordError) {
      setError(forgotPasswordError.toString())
    }
  }, [forgotPasswordError])

  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault()
    setError(null)

    // Call the forgot password mutation
    forgotPassword(msisdn)

    // Set submitted state to show success message
    setIsSubmitted(true)
  }

  return (
    <div className="flex min-h-screen flex-col items-center justify-center bg-muted/40 p-4">
      <div className="mb-4 flex items-center gap-2 text-2xl font-bold">
        <Headphones className="h-6 w-6 text-primary" />
        <span>CallCenter</span>
      </div>

      <Card className="w-full max-w-md">
        <CardHeader className="space-y-1">
          <div className="flex items-center">
            <Link
              href="/auth/login"
              className="mr-2 inline-flex items-center text-sm text-muted-foreground hover:text-foreground"
            >
              <ArrowLeft className="mr-1 size-4" />
              Back to login
            </Link>
          </div>
          <CardTitle className="text-2xl font-bold">Forgot password</CardTitle>
          <CardDescription>
            Enter your phone number and we&apos;ll send you an OTP code to reset your password
          </CardDescription>
        </CardHeader>
        <CardContent>
          {error && (
            <Alert variant="destructive" className="mb-4">
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {isSubmitted ? (
            <div className="flex flex-col items-center justify-center space-y-3 py-6 text-center">
              <div className="rounded-full bg-primary/10 p-3">
                <CheckCircle className="h-6 w-6 text-primary" />
              </div>
              <div className="space-y-1">
                <h3 className="text-lg font-medium">Check your phone number</h3>
                <p className="text-sm text-muted-foreground">
                  We&apos;ve sent a password reset code to <span className="font-medium text-foreground">{msisdn}</span>
                </p>
              </div>
              <Button asChild variant="link" className="mt-4">
                <Link href={`/auth/reset-password?phone=${encodeURIComponent(msisdn)}`}>Enter OTP</Link>
              </Button>
            </div>
          ) : (
            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="msisdn">Phone Number</Label>
                <Input
                  id="msisdn"
                  type="text"
                  placeholder="+254712345678"
                  required
                  disabled={isLoading}
                  value={msisdn}
                  onChange={(e) => setMsisdn(e.target.value)}
                />
              </div>
              <Button type="submit" className="w-full" disabled={isLoading}>
                {isLoading ? (
                  <>
                    <Loader2 className="mr-2 size-4 animate-spin" />
                    Sending code...
                  </>
                ) : (
                  "Send code"
                )}
              </Button>
            </form>
          )}
        </CardContent>
        <CardFooter className="flex flex-col space-y-4">
          <div className="text-center text-sm">
            Remember your password?{" "}
            <Link href="/auth/login" className="text-primary hover:underline">
              Sign in
            </Link>
          </div>
        </CardFooter>
      </Card>
    </div>
  )
}

// Main page component with Suspense boundary
export default function ForgotPasswordPage() {
  return (
    <Suspense fallback={
      <div className="flex min-h-screen flex-col items-center justify-center bg-muted/40 p-4">
        <div className="mb-4 flex items-center gap-2 text-2xl font-bold">
          <Headphones className="h-6 w-6 text-primary" />
          <span>CallCenter</span>
        </div>
        <Card className="w-full max-w-md">
          <CardContent className="flex items-center justify-center py-10">
            <Loader2 className="h-8 w-8 animate-spin text-primary" />
          </CardContent>
        </Card>
      </div>
    }>
      <ForgotPasswordForm />
    </Suspense>
  )
}

