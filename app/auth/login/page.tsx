"use client"

import { useState, useEffect } from "react"
import { useSearchParams } from "next/navigation"
import Link from "next/link"
import { useRouter } from "next/navigation"
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import { Headphones, Loader2, CheckCircle, AlertCircle, Eye, EyeOff } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Checkbox } from "@/components/ui/checkbox"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { useAuth } from "@/lib/hooks"
import { loginSchema, LoginFormValues } from "@/lib/validations/auth"
import { toast } from "sonner"

export default function LoginPage() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const [showActivationSuccess, setShowActivationSuccess] = useState(false)
  const [showPassword, setShowPassword] = useState(false)
  
  const { login, isLoggingIn, loginError } = useAuth()

  // Check if user was redirected after account activation
  useEffect(() => {
    if (searchParams.get("activated") === "true") {
      setShowActivationSuccess(true)
      // Hide the success message after 5 seconds
      const timer = setTimeout(() => {
        setShowActivationSuccess(false)
      }, 5000)
      return () => clearTimeout(timer)
    }
  }, [searchParams])

  // Initialize the form
  const form = useForm<LoginFormValues>({
    resolver: zodResolver(loginSchema),
    defaultValues: {
      email: "",
      password: "",
      rememberMe: false,
    },
  })

  // Handle form submission
  const onSubmit = async (values: LoginFormValues) => {
    try {
      // The login mutation from useAuth handles navigation on success
      login(values);
    } catch (error) {
      // Error will be handled by the useEffect hook
      console.error('Login error:', error);
    }
  }
  
  // Show toast notification when login error occurs
  useEffect(() => {
    if (loginError) {
      toast.error(loginError, {
        duration: 5000, // 5 seconds
        id: 'login-error', // Prevent duplicate toasts
      })
    }
  }, [loginError])

  return (
    <div className="flex min-h-screen flex-col items-center justify-center p-4">
      <div className="mb-4 flex items-center gap-2 text-2xl font-bold">
        <Headphones className="h-6 w-6 text-primary" />
        <span>CallCenter</span>
      </div>

      <Card className="w-full max-w-md">
        <CardHeader className="space-y-1">
          <CardTitle className="text-2xl font-bold">Login</CardTitle>
          <CardDescription>Enter your credentials to access your account</CardDescription>
        </CardHeader>
        <CardContent>
          {showActivationSuccess && (
            <Alert className="mb-4 bg-green-50 text-green-800 dark:bg-green-900/20 dark:text-green-400">
              <CheckCircle className="size-4" />
              <AlertDescription>Your account has been successfully activated. You can now log in.</AlertDescription>
            </Alert>
          )}

          <Form {...form}>
            <form onSubmit={(e) => {
              e.preventDefault();
              form.handleSubmit(onSubmit)(e);
            }} className="space-y-4">
              <FormField
                control={form.control}
                name="email"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Email</FormLabel>
                    <FormControl>
                      <Input type="email" placeholder="Enter your email" disabled={isLoggingIn} {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="password"
                render={({ field }) => (
                  <FormItem>
                    <div className="flex items-center justify-between">
                      <FormLabel>Password</FormLabel>
                      <Link href="/auth/forgot-password" className="text-xs text-primary hover:underline">
                        Forgot password?
                      </Link>
                    </div>
                    <div className="relative">
                      <FormControl>
                        <Input 
                          type={showPassword ? "text" : "password"} 
                          placeholder="••••••••" 
                          disabled={isLoggingIn} 
                          {...field} 
                        />
                      </FormControl>
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        className="absolute right-0 top-0 h-full px-3 py-2"
                        onClick={() => setShowPassword(!showPassword)}
                      >
                        {showPassword ? (
                          <EyeOff className="size-4" />
                        ) : (
                          <Eye className="size-4" />
                        )}
                      </Button>
                    </div>
                    {field.value && (
                      <div className="mt-2 space-y-2">
                        <div className="flex gap-2">
                          <div className={`h-1 flex-1 rounded-full ${field.value.length >= 4 ? 'bg-green-500' : 'bg-gray-200'}`} />
                        </div>
                        <div className="text-xs text-muted-foreground">
                          Password must contain:
                          <ul className="mt-1 space-y-1">
                            <li className={field.value.length >= 4 ? 'text-green-500' : ''}>• At least 4 characters</li>
                          </ul>
                        </div>
                      </div>
                    )}
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="rememberMe"
                render={({ field }) => (
                  <FormItem className="flex items-center space-x-2">
                    <FormControl>
                      <Checkbox 
                        checked={field.value} 
                        onCheckedChange={field.onChange} 
                        disabled={isLoggingIn} 
                      />
                    </FormControl>
                    <FormLabel className="text-sm font-normal">Remember me for 30 days</FormLabel>
                  </FormItem>
                )}
              />

              <Button type="submit" className="w-full" disabled={isLoggingIn}>
                {isLoggingIn ? (
                  <>
                    <Loader2 className="mr-2 size-4 animate-spin" />
                    Signing in...
                  </>
                ) : (
                  "Sign in"
                )}
              </Button>
            </form>
          </Form>
        </CardContent>
      </Card>
    </div>
  )
}

