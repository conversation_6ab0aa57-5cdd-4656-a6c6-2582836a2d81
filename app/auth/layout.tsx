import type React from "react"
import type { <PERSON>ada<PERSON> } from "next"
import { Toaster } from "@/components/ui/sonner"

export const metadata: Metadata = {
  title: "Authentication - Call Center Management System",
  description: "Authentication for the call center management system",
}

export default function AuthLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  return (
    <div className="flex items-center justify-center min-h-screen bg-background">
      <div className="w-full max-w-2xl p-6 space-y-6">
        {children}
      </div>
      <Toaster 
        richColors 
        position="top-right" 
        duration={6000} 
        closeButton 
      />
    </div>
  )
}

