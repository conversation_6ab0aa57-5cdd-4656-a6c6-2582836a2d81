"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import { Headphones, Loader2, CheckCircle, XCircle } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"

export default function VerifyEmailPage() {
  const [isLoading, setIsLoading] = useState(true)
  const [isVerified, setIsVerified] = useState<boolean | null>(null)

  useEffect(() => {
    // Simulate verification process
    const timer = setTimeout(() => {
      // For demo purposes, we'll just set it to verified
      // In a real app, you would verify the token from the URL
      setIsVerified(true)
      setIsLoading(false)
    }, 2000)

    return () => clearTimeout(timer)
  }, [])

  return (
    <div className="flex min-h-screen flex-col items-center justify-center bg-muted/40 p-4">
      <div className="mb-4 flex items-center gap-2 text-2xl font-bold">
        <Headphones className="h-6 w-6 text-primary" />
        <span>CallCenter</span>
      </div>

      <Card className="w-full max-w-md">
        <CardHeader className="space-y-1">
          <CardTitle className="text-2xl font-bold">Email verification</CardTitle>
          <CardDescription>Verifying your email address</CardDescription>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="flex flex-col items-center justify-center space-y-3 py-6 text-center">
              <Loader2 className="h-8 w-8 animate-spin text-primary" />
              <p className="text-sm text-muted-foreground">Verifying your email address...</p>
            </div>
          ) : isVerified ? (
            <div className="flex flex-col items-center justify-center space-y-3 py-6 text-center">
              <div className="rounded-full bg-primary/10 p-3">
                <CheckCircle className="h-6 w-6 text-primary" />
              </div>
              <div className="space-y-1">
                <h3 className="text-lg font-medium">Email verified</h3>
                <p className="text-sm text-muted-foreground">
                  Your email has been verified successfully. You can now log in to your account.
                </p>
              </div>
              <Button asChild className="mt-4">
                <Link href="/auth/login">Go to login</Link>
              </Button>
            </div>
          ) : (
            <div className="flex flex-col items-center justify-center space-y-3 py-6 text-center">
              <div className="rounded-full bg-destructive/10 p-3">
                <XCircle className="h-6 w-6 text-destructive" />
              </div>
              <div className="space-y-1">
                <h3 className="text-lg font-medium">Verification failed</h3>
                <p className="text-sm text-muted-foreground">
                  We couldn&apos;t verify your email address. The link may have expired or is invalid.
                </p>
              </div>
              <Button asChild variant="outline" className="mt-4">
                <Link href="/auth/login">Back to login</Link>
              </Button>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}

