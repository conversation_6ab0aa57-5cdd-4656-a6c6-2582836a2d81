"use client";

import React from 'react';
import { AttendanceDashboard } from '@/components/attendance/attendance-dashboard';
import { But<PERSON> } from '@/components/ui/button';
import { ArrowLeft } from 'lucide-react';
import Link from 'next/link';
import { useAuth } from '@/lib/hooks/useAuth';
import { hasPermission } from '@/lib/utils/roles';

export default function AttendancePage() {
  const { user } = useAuth();
  const userRole = user?.role;
  
  // Check if user has supervisor permissions
  const isSupervisor = userRole && (
    hasPermission(userRole, 'canViewUsers') || 
    userRole.name?.toLowerCase().includes('supervisor') ||
    userRole.name?.toLowerCase().includes('admin')
  );

  return (
    <div className="flex-1 space-y-6 p-4 md:p-8 pt-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Attendance Management</h1>
          <p className="text-muted-foreground">
            Track work hours, breaks, and productivity metrics
          </p>
        </div>
        
        <Link href="/dashboard">
          <Button variant="outline">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Dashboard
          </Button>
        </Link>
      </div>

      {/* Attendance Dashboard */}
      <AttendanceDashboard 
        showSupervisorView={!!isSupervisor}
        className="w-full"
      />
    </div>
  );
}