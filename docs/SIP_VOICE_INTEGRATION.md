# SIP Voice Integration Documentation

## Overview

This document describes the integration of the SIP developer's WebRTC voice implementation into our React/Next.js call center application.

## Architecture

### Core Components

1. **`lib/services/webrtc-voice-service.ts`** - Main voice service using HTTPWebSocketVoice
2. **`lib/hooks/useWebRTCVoice.ts`** - React hook for voice management
3. **`lib/services/sip-voice-loader.ts`** - SIP system loader and placeholder implementation
4. **`components/voice/VoiceSystemInitializer.tsx`** - App-level voice system initialization

### Configuration

```typescript
const VOICE_CONFIG = {
  sipServer: '*************',
  extension: '1003',
  password: 'webrtc1003'
};

// Updated WebSocket URLs (ngrok)
const WEBSOCKET_URL = 'wss://c66e8de5f99b.ngrok-free.app/ws';
const SECURE_WEBSOCKET_URL = 'wss://ded9c059d113.ngrok-free.app/ws';
```

## Integration Points

### 1. Application Startup

The voice system is automatically initialized when the app starts:

```typescript
// In app/layout.tsx
<VoiceSystemInitializer>
  {children}
</VoiceSystemInitializer>
```

### 2. Call Handling

#### Answer Incoming Call
```typescript
const answerCall = async () => {
  // 1. Answer in CCR system
  socketService.emit(SocketEvent.ANSWER_CALL, { callId: currentCall.id });
  
  // 2. Establish voice connection
  await webrtcVoice.answerIncomingCall(currentCall.channelId, user.id);
};
```

#### Make Outbound Call
```typescript
const makeOutboundCall = async (phoneNumber: string) => {
  // 1. Initiate in CCR system
  // 2. Establish voice connection
  await webrtcVoice.makeOutboundCall(phoneNumber, user.id);
};
```

#### End Call
```typescript
const endCall = () => {
  // 1. Hang up voice call
  webrtcVoice.hangupCurrentCall();
  
  // 2. Update CCR system
  socketService.emit(SocketEvent.END_CALL, { callId: currentCall.id });
};
```

### 3. Voice Controls

#### Mute/Unmute
```typescript
const toggleMute = () => {
  const isMuted = webrtcVoice.toggleMute();
  // UI updates automatically via state management
};
```

#### Voice Status
```typescript
const status = webrtcVoice.getStatus();
// Returns: { connected, hasActiveCall, isMuted, extension, websocketUrl }
```

## UI Components

### ActiveCall Component

Shows voice status and controls:

- **Voice Status Indicator**: Shows connection state and extension
- **Mute/Unmute Button**: Available when call is active
- **Visual Feedback**: Color-coded status (green=connected, yellow=connecting, red=error)

### Voice Status States

1. **Voice Disabled** (gray) - System not loaded/supported
2. **Connecting voice...** (yellow) - Initializing
3. **Voice Ready (Ext. 1003)** (blue) - Connected but no active call
4. **Voice Connected (Ext. 1003)** (green) - Active call with voice
5. **Voice Error** (red) - Error state

## API Endpoints

The voice system expects these endpoints to be available:

### Answer Call
```
POST /api/answer
{
  "channelId": "1753850262.28",
  "agentId": "user-id",
  "extension": "1003"
}
```

### Make Outbound Call
```
POST /api/make-call
{
  "phoneNumber": "+1234567890",
  "agentId": "user-id",
  "extension": "1003"
}
```

## SIP Developer Integration

### Required Implementation

The SIP developer needs to provide the actual `HTTPWebSocketVoice` class implementation. Currently, we have a placeholder implementation in `sip-voice-loader.ts`.

### HTTPWebSocketVoice Interface

```typescript
interface HTTPWebSocketVoice {
  initialize(): Promise<boolean>;
  makeOutboundCall(phoneNumber: string): Promise<boolean>;
  hangupCall(): boolean;
  toggleMute(): boolean;
  getStatus(): {
    connected: boolean;
    hasActiveCall: boolean;
    isMuted: boolean;
    error?: string;
  };
}
```

### Integration Steps for SIP Developer

1. **Replace Placeholder**: Replace the placeholder `HTTPWebSocketVoice` class in `sip-voice-loader.ts` with the actual implementation
2. **Update WebSocket URLs**: Ensure the implementation uses the ngrok URLs
3. **Test Integration**: Verify all methods work with our React components
4. **Error Handling**: Ensure proper error messages are returned

## Testing

### Development Mode

In development, a voice status indicator appears in the bottom-right corner showing:
- Voice system status (Ready/Disabled)
- Extension number
- Connection state

### Testing Checklist

- [ ] Voice system initializes on app startup
- [ ] Incoming calls trigger voice connection
- [ ] Outbound calls establish voice connection
- [ ] Mute/unmute functionality works
- [ ] Call hangup cleans up voice resources
- [ ] Error states are handled gracefully
- [ ] UI shows correct voice status

## Troubleshooting

### Common Issues

1. **"Voice system not supported"**
   - Ensure `HTTPWebSocketVoice` class is loaded
   - Check browser console for loading errors

2. **"Voice setup incomplete"**
   - Check API endpoints are responding
   - Verify WebSocket connections
   - Check SIP server connectivity

3. **Mute button not appearing**
   - Ensure call has active voice connection
   - Check `hasActiveCall` state

### Debug Information

Enable debug logging by checking browser console for:
- `🎙️` Voice system messages
- `📞` Call handling messages
- `✅` Success indicators
- `❌` Error messages

## Migration Notes

### Changes from Previous Implementation

1. **Removed browser-based WebRTC**: No longer using `navigator.mediaDevices.getUserMedia`
2. **Added SIP integration**: Now uses HTTPWebSocketVoice for SIP-based calling
3. **Updated state management**: New state properties for SIP system
4. **Enhanced error handling**: Better error messages and fallback behavior

### Backward Compatibility

The integration maintains compatibility with:
- Existing call timer functionality
- Current call state management
- UI components and styling
- Error handling and notifications

## Next Steps

1. **SIP Developer**: Provide actual `HTTPWebSocketVoice` implementation
2. **Testing**: Comprehensive testing with real SIP server
3. **Documentation**: Update user documentation for voice features
4. **Monitoring**: Add logging for voice system performance
